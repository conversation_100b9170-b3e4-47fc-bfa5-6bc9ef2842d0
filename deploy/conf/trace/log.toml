# 日志文件名称
FileName="{gdp.LogDir}/trace/trace.log"

# 日志切分规则,可选参数，默认为1hour
# 可选值和切分的文件后缀如下：
# 1hour -> .2020072714
# no 不切分
# 1day -> .20200727
# 1min -> .202007271452
# 5min -> .202007271450
# 10min -> .202007271450
# 30min -> .202007271430
# 若上述默认规则不满足，也可以自定义，详见 baidu/gdp/extension ：writer
RotateRule="1day"

# 日志文件保留个数，可选参数
# 该个数是notice和wf文件的总个数
# 默认48个，若为-1，日志文件将不清理
MaxFileNum=48

# json 打印日志。 true 代表方便使用者查看的json，  false json 打印在一行内
PrettyPrint=false

# span 信息中的时间。 true 打印：2022-03-10T14:09:24.857904165+08:00 。 false 打印：0001-01-01T00:00:00Z
# 可选 默认false
WithoutTimeStamps=true
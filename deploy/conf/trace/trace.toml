# SampleType 采样类型
# "always_on": AlwaysOnSampler
# "always_off": AlwaysOffSampler
# "traceidratio": TraceIdRatioBased
# "parentbased_always_on": ParentBased(root=AlwaysOnSampler)
# "parentbased_always_off": ParentBased(root=AlwaysOffSampler)
# "parentbased_traceidratio": ParentBased(root=TraceIdRatioBased)
Sampler = "always_on"
#SamplerArg 格式：字符串
#For traceidratio and parentbased_traceidratio samplers: Sampling probability, a number in the [0..1] range, e.g. “0.25”. Default is 1.0 if unset.
SamplerArg = "1.0"
# propagator 配置（默认值 w3c)
# OpenTelemetry 使用 Propagators 来序列化和反序列话横切关注点的值，例如 Span （通常只有 SpanContext 的部分）和 Baggage 。
# 不同的 Propagators 类型定义了特定传输和绑定到数据类型的限制。
# 可选值
# "w3c": W3C Trace Context
# "baggage": W3C Baggage
# "b3": B3 Single
# "none": No automatically configured propagator.
Propagators = "w3c"
[Exporters]
[Exporters.jaeger]
ServicerName = "jaeger"
EndPointURI = "/api/traces"
#UserName = "xxx"
#Password = "123456"
#[Exporters]
#[Exporters.stdouttrace]
#EndPointURI = "{gdp.ConfDir}/trace/log.toml"

# 可选，额外的 tag 信息，key 和 Value 都是字符串
#[Tags]
#K1="V1"
#K2="v2"
#GOROOT="{env.GOROOT}"
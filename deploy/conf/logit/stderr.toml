# 用于打印进程的 stderr 日志
# 未 recover 的 panic 日志也会出现在这个文件(log/std/stderr.log)里
# 监控系统可以通过采集关键字 "panic" 来采集 panic 次数
# 标准库默认的 log.Print 打印的日志也会出现在这里

# 日志文件名称 
# 建议不要调整 FileName 的值，这样在各个 PaaS 上时，监控系统可以采用统一的采集规则
FileName="{gdp.LogDir}/std/stderr.log"

# 日志切分规则,可选参数，默认为1hour
# 可选值和切分的文件后缀如下：
# 1hour -> .2020072714
# no 不切分
# 1day -> .20200727
# 1min -> .202007271452
# 5min -> .202007271450
# 10min -> .202007271450
# 30min -> .202007271430
# 若上述默认规则不满足，也可以自定义，详见 baidu/gdp/extension ：writer
RotateRule="1hour"

# 日志文件保留个数，可选参数
# 是notice、wf文件的分别的个数
# 默认48个，若为-1，日志文件将不清理
MaxFileNum=48

# 日志异步队列大小，可选参数
# 默认值 4096，若为-1，则队列大小为0
BufferSize=4096

# 日志进入待写队列超时时间，毫秒
# 默认为0，不超时，若出现落盘慢的时候，调用写日志的地方会出现同步等待
# 若配置为 >0 的值，当达到超时时间仍然没有写出，则该条消息会直接丢弃掉，Writer 会返回成功
# 若配置为 -1, 则队列出现阻塞时，立即丢弃该消息，Writer 返回成功 （22年8月新增可配置）
WriterTimeout = -1

# 日志编码的对象池名称，可选参数
# 默认为 default_text（普通文本编码）
# 可选值：default_json，支持自定义
EncoderPool="default_text"

# 日志内容前缀，可选参数
# 默认为default (包含日志等级、当前时间[精确到秒]、调用位置)
# 可选值：default-默认，时间精确到秒，default_nano-时间精确到纳秒、no-无前缀。
# 可通过 RegisterPrefixFunc 自定义
# 注意，这里配置 default_nano 的不是默认值
Prefix="default_nano"

# 不需要 配置 [[Dispatch]] 部分

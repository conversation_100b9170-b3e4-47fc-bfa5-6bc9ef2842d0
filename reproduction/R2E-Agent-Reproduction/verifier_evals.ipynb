{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from tqdm import tqdm\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from datasets import load_dataset\n", "\n", "num_estimation_trials = 1000"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def get_execution_based_aggregation(\n", "    regression_list: list[int],\n", "    testgen_list: list[int],\n", "    verifier_list: list[float],\n", "    gt_list: list[bool],\n", "    sample_indices: list[int],\n", "):\n", "    \"\"\"\n", "    Get execution-based aggregation. First, we filter the the samples with maximum regression value. \n", "    Next, we rank the samples via testgen values and get the average pass rate.\n", "    \"\"\"\n", "    \n", "    sample_list = [\n", "        (regression_list[i], testgen_list[i], verifier_list[i], gt_list[i])\n", "        for i in sample_indices\n", "    ]\n", "\n", "    # Sort by regression value\n", "    sample_list.sort(reverse=True, key=lambda x: (x[0], x[1], x[2]))\n", "\n", "    # Get the maximum regression value\n", "    max_regression_value = sample_list[0][0]\n", "\n", "    # Filter the samples with maximum regression value\n", "    filtered_samples = [\n", "        (x[0], x[1], x[2], x[3]) for x in sample_list if x[0] == max_regression_value\n", "    ]\n", "    \n", "    # Sort by testgen value\n", "    filtered_samples.sort(reverse=True, key=lambda x: (x[1]))\n", "\n", "    # Get the maximum testgen value\n", "    max_testgen_value = filtered_samples[0][1]\n", "\n", "    # Filter the samples with maximum testgen value\n", "    filtered_samples = [\n", "        (x[0], x[1], x[2], x[3]) for x in filtered_samples if x[1] == max_testgen_value\n", "    ]\n", "\n", "    mean_pass_rate = np.mean(\n", "        [x[3] for x in filtered_samples]\n", "    )    \n", "    return mean_pass_rate\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def get_execution_free_aggregation(\n", "    regression_list: list[int],\n", "    testgen_list: list[int],\n", "    verifier_list: list[float],\n", "    gt_list: list[bool],\n", "    sample_indices: list[int],\n", "):\n", "    \"\"\"\n", "    Get execution-free aggregation. First, we filter the the samples with maximum verifier value and get the average pass rate.\n", "    \"\"\"\n", "    \n", "    sample_list = [\n", "        (regression_list[i], testgen_list[i], verifier_list[i], gt_list[i])\n", "        for i in sample_indices\n", "    ]\n", "\n", "    # Sort by verifier value\n", "    sample_list.sort(reverse=True, key=lambda x: x[2])\n", "\n", "    # Get the maximum verifier value\n", "    max_verifier_value = sample_list[0][2]\n", "\n", "    # Filter the samples with maximum verifier value\n", "    filtered_samples = [\n", "        (x[0], x[1], x[2], x[3]) for x in sample_list if x[2] == max_verifier_value\n", "    ]\n", "\n", "    mean_pass_rate = np.mean(\n", "        [x[3] for x in filtered_samples]\n", "    )    \n", "    return mean_pass_rate"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def get_hybrid_aggregation(\n", "    regression_list: list[int],\n", "    testgen_list: list[int],\n", "    verifier_list: list[float],\n", "    gt_list: list[bool],\n", "    sample_indices: list[int],\n", "):\n", "    \"\"\"\n", "    Get gybrid aggregation. First, we rank and filter based on the top-k verifier values. Next, we filter the samples with maximum regression value, followed by maximum testgen values and get the average pass rate.\n", "    \"\"\"\n", "\n", "    sample_list = [\n", "        (regression_list[i], testgen_list[i], verifier_list[i], gt_list[i])\n", "        for i in sample_indices\n", "    ]\n", "\n", "    # Sort by verifier value\n", "    sample_list.sort(reverse=True, key=lambda x: (x[2], x[0], x[1]))\n", "\n", "    # Get the top-k samples based on verifier value\n", "    top_k = max(3, int(len(sample_list) * 0.5))\n", "    top_k_samples = sample_list[:top_k]\n", "\n", "    # Sort by regression value\n", "    top_k_samples.sort(reverse=True, key=lambda x: (x[0], x[2], x[1]))\n", "\n", "    # Get the maximum regression value\n", "    max_regression_value = top_k_samples[0][0]\n", "\n", "    # Filter the samples with maximum regression value\n", "    filtered_samples = [\n", "        (x[0], x[1], x[2], x[3]) for x in top_k_samples if x[0] == max_regression_value\n", "    ]\n", "\n", "    # Sort by testgen value\n", "    filtered_samples.sort(reverse=True, key=lambda x: (x[1], x[2]))\n", "\n", "    # Get the maximum testgen value\n", "    max_testgen_value = filtered_samples[0][1]\n", "\n", "    # Filter the samples with maximum testgen value\n", "    filtered_samples = [\n", "        (x[0], x[1], x[2], x[3]) for x in filtered_samples if x[1] == max_testgen_value\n", "    ]\n", "\n", "    # Sort by verifier value\n", "    filtered_samples.sort(reverse=True, key=lambda x: (x[2]))\n", "\n", "    # Get the maximum verifier value\n", "    max_verifier_value = filtered_samples[0][2]\n", "\n", "    # Filter the samples with maximum verifier value\n", "    filtered_samples = [\n", "        (x[0], x[1], x[2], x[3]) for x in filtered_samples if x[2] == max_verifier_value\n", "    ]\n", "\n", "    mean_pass_rate = np.mean([x[3] for x in filtered_samples])\n", "    return mean_pass_rate"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def subsample_passrates(\n", "    gt_list, regression_list, testgen_list, verifier_list, steps_list\n", "):\n", "    total_programs = len(gt_list)\n", "    assert (\n", "        len(gt_list)\n", "        == len(regression_list)\n", "        == len(testgen_list)\n", "        == len(verifier_list)\n", "        == len(steps_list)\n", "    ), f\"Length mismatch: {len(gt_list)}, {len(regression_list)}, {len(testgen_list)}, {len(verifier_list)}, {len(steps_list)}\"\n", "\n", "    passrate = {}\n", "    executionbased = {}\n", "    executionfree = {}\n", "    hybrid = {}\n", "\n", "    for idx in range(1, 27):\n", "        passrate[idx] = []\n", "        executionbased[idx] = []\n", "        executionfree[idx] = []\n", "        hybrid[idx] = []\n", "        for _ in range(num_estimation_trials):  ## num trials\n", "            size = min(total_programs, idx)\n", "            sample_indices = np.random.choice(\n", "                range(total_programs), size=size, replace=False\n", "            )\n", "            sample_gt = [gt_list[i] for i in sample_indices]\n", "\n", "            passrate[idx].append(np.any(sample_gt))\n", "            executionbased[idx].append(\n", "                get_execution_based_aggregation(\n", "                    regression_list,\n", "                    testgen_list,\n", "                    verifier_list,\n", "                    gt_list,\n", "                    sample_indices,\n", "                )\n", "            )\n", "            executionfree[idx].append(\n", "                get_execution_free_aggregation(\n", "                    regression_list,\n", "                    testgen_list,\n", "                    verifier_list,\n", "                    gt_list,\n", "                    sample_indices,\n", "                )\n", "            )\n", "            hybrid[idx].append(\n", "                get_hybrid_aggregation(\n", "                    regression_list,\n", "                    testgen_list,\n", "                    verifier_list,\n", "                    gt_list,\n", "                    sample_indices,\n", "                )\n", "            )\n", "\n", "    return [\n", "        passrate,\n", "        executionbased,\n", "        executionfree,\n", "        hybrid,\n", "    ]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 500/500 [00:33<00:00, 14.86it/s]\n"]}], "source": ["import pandas as pd\n", "from tqdm import tqdm\n", "import multiprocessing as mp\n", "from functools import partial\n", "\n", "def process_row(row, subsample_func):\n", "    gt_list = eval(row['gt_rewards'])\n", "    regression_list = eval(row['regression_rates'])\n", "    testgen_list = eval(row['mean_predicted_rewards'])\n", "    verifier_list = eval(row['avg_yes_prob'])\n", "    steps_list = eval(row['step_counts'])\n", "    \n", "    sample_out = subsample_func(gt_list, regression_list, testgen_list, verifier_list, steps_list)\n", "    \n", "    return (sample_out, row.iloc[0])\n", "\n", "def parallelize_processing(test_results):\n", "    # Define number of processes to use\n", "    num_processes = mp.cpu_count()\n", "    \n", "    # Create a pool of workers\n", "    pool = mp.Pool(processes=num_processes)\n", "    \n", "    # Create a partial function with the subsample_passrates function already specified\n", "    process_func = partial(process_row, subsample_func=subsample_passrates)\n", "    \n", "    # Process rows in parallel and collect results\n", "    results = list(tqdm(\n", "        pool.imap(process_func, [row for _, row in test_results.iterrows()]), \n", "        total=test_results.shape[0]\n", "    ))\n", "    \n", "    # Close the pool\n", "    pool.close()\n", "    pool.join()\n", "    \n", "    return results\n", "\n", "# Load the data\n", "test_results = load_dataset('R2E-Gym/32B_predictions')['predictions'].to_pandas()\n", "\n", "# Run the parallelized processing\n", "df_list = parallelize_processing(test_results)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 26/26 [00:20<00:00,  1.28it/s]\n"]}], "source": ["mean_passrates = []\n", "std_passrates = []\n", "mean_execution_based = []\n", "std_execution_based = []\n", "mean_execution_free = []\n", "std_execution_free = []\n", "mean_hybrid = []\n", "std_hybrid = []\n", "\n", "old_regresstestverif=None\n", "\n", "for i in tqdm(list(range(1, 27))):\n", "    passrates = []\n", "    execution_based = []\n", "    execution_free = []\n", "    hybrid = []\n", "\n", "    for j in range(num_estimation_trials):\n", "        passrate = [x[0][0][i][j] for x in df_list]\n", "        regresstest = [x[0][1][i][j] for x in df_list]\n", "        regressverif = [x[0][2][i][j] for x in df_list]\n", "        regresstestverif = [x[0][3][i][j] for x in df_list]\n", "        passrates.append(np.mean(passrate))\n", "        execution_based.append(np.mean(regresstest))\n", "        execution_free.append(np.mean(regressverif))\n", "        hybrid.append(np.mean(regresstestverif))\n", "        \n", "    mean_passrates.append(\n", "        np.mean(passrates)\n", "    )\n", "    std_passrates.append(np.std(passrates))\n", "\n", "    mean_execution_based.append(\n", "        np.mean(execution_based)\n", "    )\n", "    std_execution_based.append(np.std(execution_based))\n", "\n", "    mean_execution_free.append(\n", "        np.mean(execution_free)\n", "    )\n", "\n", "    std_execution_free.append(np.std(execution_free))\n", "    \n", "    mean_hybrid.append(\n", "        np.mean(hybrid)\n", "    )\n", "    std_hybrid.append(np.std(hybrid))\n", "\n", "\n", "mean_passrates = np.array(mean_passrates) * 100\n", "std_passrates = np.array(std_passrates) * 100\n", "mean_execution_based = np.array(mean_execution_based) * 100\n", "std_execution_based = np.array(std_execution_based) * 100\n", "mean_execution_free = np.array(mean_execution_free) * 100\n", "std_execution_free = np.array(std_execution_free) * 100\n", "mean_hybrid = np.array(mean_hybrid) * 100\n", "std_hybrid = np.array(std_hybrid) * 100\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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******************************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***************************************************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*******************************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# X-axis values\n", "x = range(1, 27)\n", "\n", "# Start plotting\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Set style\n", "sns.set(style=\"whitegrid\")\n", "\n", "# Lighten the grid\n", "sns.set_style(\"whitegrid\", {\"grid.color\": \".9\"})\n", "\n", "def annotate_line(x, y, label, offset):\n", "    for i in range(len(x)):\n", "        if (\n", "            i % 5 == 0 or i == len(x) - 1\n", "        ):  \n", "            if i == 0:\n", "                continue\n", "            plt.annotate(\n", "                f\"{y[i]:.1f}\",\n", "                (x[i], y[i]),\n", "                textcoords=\"offset points\",\n", "                xytext=(0, offset),  \n", "                ha=\"center\",\n", "                fontsize=20,\n", "                fontstyle=\"italic\",  \n", "                color=\"black\",  \n", "                alpha=0.8,  \n", "            )\n", "\n", "plt.annotate(\n", "    f\"{mean_passrates[0]:.1f}\",\n", "    (x[0], mean_passrates[0]),\n", "    textcoords=\"offset points\",\n", "    xytext=(0, -25),  \n", "    ha=\"center\",\n", "                fontsize=20,\n", "                fontstyle=\"italic\",  \n", "                color=\"black\",  \n", "                alpha=0.8,  \n", ")\n", "\n", "\n", "plot4 = sns.lineplot(\n", "    x=x,\n", "    y=mean_hybrid,\n", "    label=\"Hybrid Verifier\",\n", "    color=\"m\",\n", "    marker=\"D\",\n", "    markersize=8, lw=4\n", ")\n", "plt.fill_between(\n", "    x,\n", "    np.array(mean_hybrid) - np.array(std_hybrid),\n", "    np.array(mean_hybrid) + np.array(std_hybrid),\n", "    alpha=0.2,\n", "    color=\"m\",\n", ")\n", "annotate_line(x, mean_hybrid, \"Hybrid Verifier\", 5)\n", "\n", "plot2 = sns.lineplot(\n", "    x=x,\n", "    y=mean_execution_based,\n", "    label=\"Execution-Based Verifier\",\n", "    color=\"g\",\n", "    marker=\"s\",\n", "    markersize=8, lw=4\n", ")\n", "plt.fill_between(\n", "    x,\n", "    np.array(mean_execution_based) - np.array(std_execution_based),\n", "    np.array(mean_execution_based) + np.array(std_execution_based),\n", "    alpha=0.2,\n", "    color=\"g\",\n", ")\n", "annotate_line(x, mean_execution_based, \"Test Agent Verifier\", 15)\n", "\n", "plot3 = sns.lineplot(\n", "    x=x,\n", "    y=mean_execution_free,\n", "    label=\"Execution-Free Verifier\",\n", "    color=\"r\",\n", "    marker=\"^\",\n", "    markersize=8, lw=4\n", ")\n", "plt.fill_between(\n", "    x,\n", "    np.array(mean_execution_free) - np.array(std_execution_free),\n", "    np.array(mean_execution_free) + np.array(std_execution_free),\n", "    alpha=0.2,\n", "    color=\"r\",\n", ")\n", "annotate_line(x, mean_execution_free, \"Trajectory Verifier\", -35)\n", "\n", "# Set x-ticks\n", "plt.xticks(\n", "    ticks=x,\n", "    labels=[str(i) if (i % 2 == 0 or i == 1) else \"\" for i in range(1, 27)],\n", "    fontsize=23,\n", "    rotation=45,\n", ")\n", "plt.yticks(fontsize=23)\n", "\n", "# Labels and title with font adjustments\n", "plt.xlabel(\"# Agent Rollouts (K)\", fontsize=30)\n", "plt.ylabel(\"Best@K\", fontsize=30)\n", "plt.title(\"Best@K with Editing Agent Rollouts\", fontsize=32, fontweight=\"bold\")\n", "\n", "# Separate handles and labels for the legends\n", "handles_pass, labels_pass = plot4.get_legend_handles_labels()\n", "\n", "handles_verifiers, labels_verifiers = [], []\n", "for plot in [plot4][:1]:\n", "    h, l = plot.get_legend_handles_labels()\n", "    handles_verifiers.extend(h)\n", "    labels_verifiers.extend(l)\n", "\n", "legend_verifiers = plt.legend(\n", "    handles=handles_verifiers,\n", "    labels=labels_verifiers,\n", "    title=\"Verifiers\",\n", "    loc=\"lower right\",\n", "    fontsize=22,\n", "    title_fontsize=24,\n", ")\n", "\n", "# Save and finalize the plot\n", "plt.tight_layout()  # Ensure everything fits without overlapping\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
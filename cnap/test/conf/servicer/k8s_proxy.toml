# Service的名字，必选，需自定义修改
Name = "k8s_proxy"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，ms
ConnTimeOut = 500
# 写数据超时，ms
WriteTimeOut = 10000
# 读数据超时，ms 1小时
ReadTimeOut = 3600000

# 请求失败后的重试次数：总请求次数 = Retry + 1
Retry = 3

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name = "RoundRobin"

 [Panel.Health]
 Ignore = true  # 跳过下游健康检查 
 
# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.k8s-proxy-service.K8S.all"
# PortKey = "http"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
[[Resource.Manual.default]]

Host = "*************"
Port = 8131

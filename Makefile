# 初始化项目目录变量
OS		:= $(shell uname -s)
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# 应用名称/二进制文件名称
APPNAME = application

# 部署环境 online、test
ifdef DEPLOY_ENV
	DEPLOY_ENV := $(DEPLOY_ENV)
else
	DEPLOY_ENV := online
endif

# GO环境
ifdef GOROOT
	GOROOT := $(GOROOT)
	GO     := $(GOROOT)/bin/go
endif
# 使用 EE 预装的 Go 版本: http://buildcloud.baidu.com/deck/deck#2.-Development-Tools
ifdef GO_1_23_HOME
    GOROOT  := $(GO_1_23_HOME)
    GO      := $(GOROOT)/bin/go
endif
GOMOD   := $(GO) mod
GOBUILD := $(GO) build
GOTEST  := $(GO) test
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")
PATH    := $(GOROOT)/bin:/opt/compiler/gcc-12/bin/:$(PATH)
ifeq ($(OS), Darwin)
	export GOENV = $(HOMEDIR)/go-darwin.env
else
	export GOENV = $(HOMEDIR)/go.env
endif
export GOBIN = $(HOMEDIR)/../../gobin/

# 执行编译，可使用命令 make 或 make all 执行, 顺序执行 prepare -> compile -> test -> package 几个阶段
all: prepare compile package

set-env:
	# 打印 相关 环境信息，若编译异常是可辅助排查
	git version # 低于 2.17.1 可能不能正常工作
	$(GO) env

# prepare阶段, 使用 bcloud 下载非 Go 依赖， 可单独执行命令: make prepare
prepare:
	git config --global http.sslVerify false

# compile 阶段，执行编译命令,可单独执行命令: make compile
compile: set-env
	#先下载 Go 依赖，若异常可查看 http://gdp.baidu-int.com/gdp2/docs/faq/dependent/
	$(GOMOD) download -x || $(GOMOD) download -x
	$(GOBUILD) -o $(HOMEDIR)/bin/$(APPNAME)

# test 阶段，进行单元测试， 可单独执行命令: make test
test: set-env
	$(GOTEST) -race -v -cover $(GOPKGS) -gcflags="-N -l"

# test-cov 阶段，打通覆盖率平台
test-cov: set-env
	$(GOTEST) -v -json -coverprofile=coverage.out $(GOPKGS)

# package阶段，对编译产出进行打包，输出到 output 目录, 可单独执行命令: make package
package:
	$(shell rm -rf $(OUTDIR))
	$(shell mkdir -p $(OUTDIR))
	$(shell mkdir -p $(OUTDIR)/var)
	$(shell mkdir -p $(OUTDIR)/data)
	$(shell mkdir -p $(OUTDIR)/log)
	$(shell cp -a bin $(OUTDIR)/bin)
	$(shell cp -a deploy/* $(OUTDIR)/)
	$(shell if [ -d "script" ]; then cp -r script $(OUTDIR)/; fi)
	tree $(OUTDIR)

gdp-run:
	gdp run

# clean 阶段，清除过程中的输出, 可单独执行命令: make clean
clean:
	rm -rf $(OUTDIR)


# mcp_runtime配置
MCP_RUNTIME_IMAGE_NAME := mcp/mcp-runtime
MCP_RUNTIME_TAG := latest
MCP_RUNTIME_REGISTRY := ccr-2y3xupwh-pub.cnc.bj.baidubce.com
go-build-linux:
	@echo "构建Go应用程序（Linux环境，用于Docker）..."
	cd cmd && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o mcp_runtime ./mcp_runtime.go
	@echo "✓ Linux Go应用程序构建完成: cmd/mcp_runtime"
# Docker 相关命令
docker-build: go-build-linux
	@echo "构建Docker镜像..."
	docker build -f Dockerfile_mcpruntime --platform linux/amd64 -t $(MCP_RUNTIME_IMAGE_NAME):$(MCP_RUNTIME_TAG) .
	@echo "✓ Docker镜像构建完成: $(MCP_RUNTIME_IMAGE_NAME):$(MCP_RUNTIME_TAG)"

docker-push:
	@echo "推送Docker镜像..."
	@if [ -z "$(MCP_RUNTIME_REGISTRY)" ]; then \
		echo "错误: 请设置REGISTRY变量"; \
		exit 1; \
	fi
	docker tag $(MCP_RUNTIME_IMAGE_NAME):$(MCP_RUNTIME_TAG) $(MCP_RUNTIME_REGISTRY)/$(MCP_RUNTIME_IMAGE_NAME):$(MCP_RUNTIME_TAG)
	docker push $(MCP_RUNTIME_REGISTRY)/$(MCP_RUNTIME_IMAGE_NAME):$(MCP_RUNTIME_TAG)

# 压力测试相关命令
stress-test-help:
	@echo "MCP 压力测试工具"
	@echo ""
	@echo "可用命令:"
	@echo "  stress-test-small      - 运行小负载测试 (5个会话)"
	@echo "  stress-test-full       - 运行完整初始化测试 (服务器+环境)"
	@echo ""
	@echo "示例:"
	@echo "  make stress-test-small"

# 创建日志目录
stress-test-logs:
	@mkdir -p benchmark/logs

# 运行小负载测试
stress-test-small: stress-test-logs
	$(GO) run benchmark/cmd/main.go -config benchmark/config/small_load.yaml -verbose

# 运行完整初始化测试
stress-test-full: stress-test-logs
	$(GO) run benchmark/cmd/main.go -config benchmark/config/full_init.yaml


# 清理压力测试日志
stress-test-clean:
	rm -rf benchmark/logs/
# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build stress-test-help stress-test-small stress-test-default stress-test-high stress-test-db stress-test-server stress-test-env stress-test-full stress-test-concurrent stress-test-stop stress-test-custom stress-test-build stress-test-clean stress-test-logs
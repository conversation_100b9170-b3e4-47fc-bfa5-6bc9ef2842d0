// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package resource

import (
	"github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
)

// StorageBosClient 变量，bos 对象存储客户端
var StorageBosClient *bos.Client

// LoggerService 业务访问日志(access_log)：log/service/service.log
var LoggerService logit.Logger

// LoggerBg 后台异步执行程序的日志
var LoggerBg logit.Logger

var MySQLClientMain mysql.Client

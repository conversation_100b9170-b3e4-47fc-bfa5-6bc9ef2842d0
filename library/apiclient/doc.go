// Package apiclient 提供了MCP在线服务的API客户端实现
//
// 这个包封装了与MCP在线服务API交互的所有功能，包括：
// - 会话管理（获取信息、就绪通知、停止会话）
// - 任务管理（获取待处理任务、认领任务、完成任务）
// - 统一的错误处理和响应解析
// - 类型安全的API调用
//
// 基本使用方式：
//
//	client := apiclient.NewClient("http://localhost:8080")
//
//	// 获取会话信息
//	session, servers, env, err := client.GetSessionInfo(sessionID)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	// 报告会话就绪
//	err = client.ReportSessionReady(sessionID, tools)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	// 获取待处理任务
//	task, err := client.GetPendingTask(sessionID)
//	if err != nil {
//		log.Fatal(err)
//	}
//
// 错误处理：
//
//	err := client.SomeAPICall()
//	if err != nil {
//		if apiErr, ok := apiclient.IsAPIError(err); ok {
//			log.Printf("API Error - Status: %d, Code: %d, Message: %s",
//				apiErr.StatusCode, apiErr.Code, apiErr.Message)
//		} else {
//			log.Printf("Other error: %v", err)
//		}
//	}
package apiclient

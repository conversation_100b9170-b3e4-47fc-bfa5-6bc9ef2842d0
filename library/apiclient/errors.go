package apiclient

import "fmt"

// APIError API错误类型
type APIError struct {
	Code       int    // API响应中的错误码
	Message    string // API响应中的错误消息
	StatusCode int    // HTTP状态码
}

// Error 实现error接口
func (e *APIError) Error() string {
	return fmt.Sprintf("API error (status: %d, code: %d): %s", e.StatusCode, e.Code, e.Message)
}

// IsAPIError 检查错误是否为API错误
func IsAPIError(err error) (*APIError, bool) {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr, true
	}
	return nil, false
}

// NewAPIError 创建新的API错误
func NewAPIError(statusCode, code int, message string) *APIError {
	return &APIError{
		StatusCode: statusCode,
		Code:       code,
		Message:    message,
	}
}

// NewHTTPError 创建HTTP错误（用于非200状态码）
func NewHTTPError(statusCode int, message string) *APIError {
	return &APIError{
		StatusCode: statusCode,
		Code:       -1,
		Message:    message,
	}
}

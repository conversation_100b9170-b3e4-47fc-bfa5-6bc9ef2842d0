package apiclient

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewClient(t *testing.T) {
	t.Run("TestNewClient", func(t *testing.T) {
		baseURL := "http://localhost:8080"
		client := NewClient(baseURL)

		assert.NotNil(t, client)
		assert.Equal(t, baseURL, client.BaseURL)
		assert.NotNil(t, client.client)
		assert.Equal(t, 65*time.Second, client.client.Timeout)
	})

	t.Run("TestNewClientWithTimeout", func(t *testing.T) {
		baseURL := "http://localhost:8080"
		timeout := 30 * time.Second
		client := NewClientWithTimeout(baseURL, timeout)

		assert.NotNil(t, client)
		assert.Equal(t, baseURL, client.BaseURL)
		assert.NotNil(t, client.client)
		assert.Equal(t, timeout, client.client.Timeout)
	})
}

func TestAPIError(t *testing.T) {
	t.Run("TestAPIErrorCreation", func(t *testing.T) {
		err := NewAPIError(400, 1001, "Invalid request")

		assert.Equal(t, 400, err.StatusCode)
		assert.Equal(t, 1001, err.Code)
		assert.Equal(t, "Invalid request", err.Message)
		assert.Contains(t, err.Error(), "API error (status: 400, code: 1001): Invalid request")
	})

	t.Run("TestHTTPErrorCreation", func(t *testing.T) {
		err := NewHTTPError(500, "Internal server error")

		assert.Equal(t, 500, err.StatusCode)
		assert.Equal(t, -1, err.Code)
		assert.Equal(t, "Internal server error", err.Message)
	})

	t.Run("TestIsAPIError", func(t *testing.T) {
		apiErr := NewAPIError(400, 1001, "Invalid request")

		// Test with APIError
		detectedErr, ok := IsAPIError(apiErr)
		assert.True(t, ok)
		assert.Equal(t, apiErr, detectedErr)

		// Test with regular error
		regularErr := assert.AnError
		detectedErr, ok = IsAPIError(regularErr)
		assert.False(t, ok)
		assert.Nil(t, detectedErr)
	})
}

func TestRequestTypes(t *testing.T) {
	t.Run("TestSessionReadyRequest", func(t *testing.T) {
		req := SessionReadyRequest{
			SessionID: 123,
			MCPTools:  []string{"tool1", "tool2"},
		}

		assert.Equal(t, int64(123), req.SessionID)
		assert.NotNil(t, req.MCPTools)
	})

	t.Run("TestSessionStopRequest", func(t *testing.T) {
		req := SessionStopRequest{
			SessionID: 456,
		}

		assert.Equal(t, int64(456), req.SessionID)
	})

	t.Run("TestClaimTaskRequest", func(t *testing.T) {
		req := ClaimTaskRequest{
			CallID:    "call-123",
			SessionID: 789,
		}

		assert.Equal(t, "call-123", req.CallID)
		assert.Equal(t, int64(789), req.SessionID)
	})

	t.Run("TestCompleteTaskRequest", func(t *testing.T) {
		req := CompleteTaskRequest{
			CallID:       "call-456",
			SessionID:    101112,
			Status:       "completed",
			Result:       map[string]string{"key": "value"},
			OldEnvMD5:    "old-md5",
			NewEnvMD5:    "new-md5",
			OldEnvURL:    "old-url",
			NewEnvURL:    "new-url",
			StartedAt:    "2023-01-01T00:00:00Z",
			ErrorMessage: "",
		}

		assert.Equal(t, "call-456", req.CallID)
		assert.Equal(t, int64(101112), req.SessionID)
		assert.Equal(t, "completed", req.Status)
		assert.NotNil(t, req.Result)
	})
}

func TestResponseTypes(t *testing.T) {
	t.Run("TestStandardAPIResponse", func(t *testing.T) {
		response := StandardAPIResponse[string]{
			Code:    0,
			Message: "success",
			Data:    "test data",
		}

		assert.Equal(t, 0, response.Code)
		assert.Equal(t, "success", response.Message)
		assert.Equal(t, "test data", response.Data)
	})

	t.Run("TestClaimTaskResponseData", func(t *testing.T) {
		response := ClaimTaskResponseData{
			Success:   true,
			CallID:    "call-123",
			Name:      "test-tool",
			Arguments: `{"arg1": "value1"}`,
			Message:   "Task claimed successfully",
		}

		assert.True(t, response.Success)
		assert.Equal(t, "call-123", response.CallID)
		assert.Equal(t, "test-tool", response.Name)
		assert.Contains(t, response.Arguments, "arg1")
		assert.Equal(t, "Task claimed successfully", response.Message)
	})

	t.Run("TestCompleteTaskResponseData", func(t *testing.T) {
		response := CompleteTaskResponseData{
			Success: true,
			CallID:  "call-456",
			Message: "Task completed successfully",
		}

		assert.True(t, response.Success)
		assert.Equal(t, "call-456", response.CallID)
		assert.Equal(t, "Task completed successfully", response.Message)
	})
}

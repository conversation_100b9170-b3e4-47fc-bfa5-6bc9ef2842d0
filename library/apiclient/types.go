package apiclient

import (
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// StandardAPIResponse 标准API响应格式
type StandardAPIResponse[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
}

// SessionInfoResponseData 获取会话信息响应数据
type SessionInfoResponseData struct {
	Session     *dao_session.ObjSession                         `json:"session"`
	Servers     []*dao_register_mcp_server.ObjRegisterMcpServer `json:"servers"`
	Environment *dao_mcp_env.ObjMcpEnv                          `json:"environment"`
}

// PendingTaskResponseData 获取待处理任务响应数据
type PendingTaskResponseData struct {
	Tasks dao_tool_call_task.ObjToolCallTask `json:"tasks"`
}

// ClaimTaskResponseData 认领任务响应数据
type ClaimTaskResponseData struct {
	Success   bool   `json:"success"`
	CallID    string `json:"call_id"`
	Name      string `json:"name,omitempty"`
	Arguments string `json:"arguments,omitempty"`
	Message   string `json:"message,omitempty"`
}

// CompleteTaskResponseData 完成任务响应数据
type CompleteTaskResponseData struct {
	Success bool   `json:"success"`
	CallID  string `json:"call_id"`
	Message string `json:"message"`
}

// SessionReadyRequest 会话就绪请求数据
type SessionReadyRequest struct {
	SessionID int64       `json:"session_id"`
	MCPTools  interface{} `json:"mcp_tools"`
}

// SessionStopRequest 会话停止请求数据
type SessionStopRequest struct {
	SessionID int64  `json:"session_id"`
	Message   string `json:"message"`
}

// ClaimTaskRequest 认领任务请求数据
type ClaimTaskRequest struct {
	CallID      string `json:"call_id"`
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
}

// CompleteTaskRequest 完成任务请求数据
type CompleteTaskRequest struct {
	CallID       string      `json:"call_id"`
	SessionID    int64       `json:"session_id,omitempty"`
	SessionCode  string      `json:"session_code,omitempty"`
	Status       string      `json:"status"`
	Result       interface{} `json:"result"`
	OldEnvMD5    string      `json:"old_env_md5"`
	NewEnvMD5    string      `json:"new_env_md5"`
	OldEnvURL    string      `json:"old_env_url"`
	NewEnvURL    string      `json:"new_env_url"`
	StartedAt    string      `json:"started_at"`
	ErrorMessage string      `json:"error_message"`
}

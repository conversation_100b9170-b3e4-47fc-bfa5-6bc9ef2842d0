package apiclient

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// Client MCP在线服务API客户端
type Client struct {
	BaseURL string
	client  *http.Client
}

// NewClient 创建新的API客户端
func NewClient(baseURL string) *Client {
	return &Client{
		BaseURL: baseURL,
		client:  &http.Client{Timeout: 65 * time.Second},
	}
}

// NewClientWithTimeout 创建带自定义超时的API客户端
func NewClientWithTimeout(baseURL string, timeout time.Duration) *Client {
	return &Client{
		BaseURL: baseURL,
		client:  &http.Client{Timeout: timeout},
	}
}

// doRequest 执行HTTP请求并返回原始响应
func (c *Client) doRequest(method, path string, body interface{}) (*http.Response, error) {
	url := fmt.Sprintf("%s%s", c.BaseURL, path)

	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := sonic.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body: %w", err)
		}
		bodyReader = strings.NewReader(string(bodyBytes))
	}

	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return c.client.Do(req)
}

// doRequestWithData 执行HTTP请求并解析返回数据
func doRequestWithData[T any](client *Client, method, path string, body interface{}) (T, error) {
	var zero T

	resp, err := client.doRequest(method, path, body)
	if err != nil {
		return zero, fmt.Errorf("execute request: %w", err)
	}
	defer resp.Body.Close()

	// 统一处理HTTP状态码
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return zero, NewHTTPError(resp.StatusCode, fmt.Sprintf("HTTP error: %s", string(bodyBytes)))
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return zero, fmt.Errorf("read response body: %w", err)
	}

	// 解析标准API响应格式
	var apiResponse StandardAPIResponse[T]
	if err := sonic.Unmarshal(bodyBytes, &apiResponse); err != nil {
		return zero, fmt.Errorf("decode response: %w, body: %s", err, string(bodyBytes))
	}

	// 检查API业务逻辑错误
	if apiResponse.Code != 0 {
		return zero, NewAPIError(resp.StatusCode, apiResponse.Code, apiResponse.Message)
	}

	return apiResponse.Data, nil
}

// doRequestRaw 执行HTTP请求并返回原始响应（用于不需要解析数据的场景）
func (c *Client) doRequestRaw(method, path string, body interface{}) error {
	resp, err := c.doRequest(method, path, body)
	if err != nil {
		return fmt.Errorf("execute request: %w", err)
	}
	defer resp.Body.Close()

	// 统一处理HTTP状态码
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return NewHTTPError(resp.StatusCode, fmt.Sprintf("HTTP error: %s", string(bodyBytes)))
	}

	// 读取响应体并检查API响应码
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read response body: %w", err)
	}

	var apiResponse StandardAPIResponse[interface{}]
	if err := sonic.Unmarshal(bodyBytes, &apiResponse); err != nil {
		return fmt.Errorf("decode response: %w, body: %s", err, string(bodyBytes))
	}

	// 检查API业务逻辑错误
	if apiResponse.Code != 0 {
		return NewAPIError(resp.StatusCode, apiResponse.Code, apiResponse.Message)
	}

	return nil
}

// GetSessionInfo 获取会话信息
func (c *Client) GetSessionInfo(sessionID int64) (
	*dao_session.ObjSession,
	[]*dao_register_mcp_server.ObjRegisterMcpServer,
	*dao_mcp_env.ObjMcpEnv, error) {

	data, err := doRequestWithData[SessionInfoResponseData](c, "GET",
		fmt.Sprintf("/api/v1/mcp/internal/session/info?session_id=%s", strconv.FormatInt(sessionID, 10)), nil)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("get session info: %w", err)
	}

	return data.Session, data.Servers, data.Environment, nil
}

// ReportSessionReady 报告会话就绪状态
func (c *Client) ReportSessionReady(sessionID int64, tools []types.MCPTool) error {
	request := SessionReadyRequest{
		SessionID: sessionID,
		MCPTools:  tools,
	}

	return c.doRequestRaw("POST", "/api/v1/mcp/internal/session/ready", request)
}

// StopSession 停止会话
func (c *Client) StopSession(sessionID int64, message string) error {
	request := SessionStopRequest{
		SessionID: sessionID,
		Message:   message,
	}

	return c.doRequestRaw("POST", "/api/v1/mcp/internal/session/stop", request)
}

// GetPendingTask 获取待执行任务
func (c *Client) GetPendingTask(sessionID int64) (*dao_tool_call_task.ObjToolCallTask, error) {
	data, err := doRequestWithData[PendingTaskResponseData](c, "GET",
		fmt.Sprintf("/api/v1/mcp/internal/tool/pending?session_id=%s", strconv.FormatInt(sessionID, 10)), nil)
	if err != nil {
		return nil, fmt.Errorf("get pending task: %w", err)
	}

	if data.Tasks.CallID == "" {
		return nil, nil
	}

	return &data.Tasks, nil
}

// ClaimTask 认领任务
func (c *Client) ClaimTask(callID string, sessionID int64) error {
	request := ClaimTaskRequest{
		CallID:    callID,
		SessionID: sessionID,
	}

	claimData, err := doRequestWithData[ClaimTaskResponseData](c, "POST", "/api/v1/mcp/internal/tool/claim", request)
	if err != nil {
		return fmt.Errorf("claim task API request failed: %w", err)
	}

	// Check business logic success (data.success must be true)
	if !claimData.Success {
		return fmt.Errorf("claim task business logic failed - success: %t, message: %s, call_id: %s",
			claimData.Success, claimData.Message, claimData.CallID)
	}

	return nil
}

// CompleteTask 完成任务执行
func (c *Client) CompleteTask(callID string, sessionID int64, status string, result interface{}, oldEnvMD5, newEnvMD5, oldEnvURL, newEnvURL string, startedAt time.Time, errorMessage string) error {
	request := CompleteTaskRequest{
		CallID:       callID,
		SessionID:    sessionID,
		Status:       status,
		Result:       result,
		OldEnvMD5:    oldEnvMD5,
		NewEnvMD5:    newEnvMD5,
		OldEnvURL:    oldEnvURL,
		NewEnvURL:    newEnvURL,
		StartedAt:    startedAt.Format(time.RFC3339Nano),
		ErrorMessage: errorMessage,
	}

	completeData, err := doRequestWithData[CompleteTaskResponseData](c, "POST", "/api/v1/mcp/internal/tool/complete", request)
	if err != nil {
		return fmt.Errorf("complete task API request failed: %w", err)
	}

	// Check business logic success (data.success must be true)
	if !completeData.Success {
		return fmt.Errorf("complete task business logic failed - success: %t, message: %s, call_id: %s",
			completeData.Success, completeData.Message, completeData.CallID)
	}

	return nil
}

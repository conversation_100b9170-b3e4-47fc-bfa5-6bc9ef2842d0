package config

import (
	"github.com/BurntSushi/toml"
	"icode.baidu.com/baidu/gdp/env"
)

type BosConfig struct {
	AccessKey  string `toml:"AccessKey"`
	SecretKey  string `toml:"SecretKey"`
	Endpoint   string `toml:"Endpoint"`
	Bucket     string `toml:"Bucket"`
	TempBucket string `toml:"TempBucket"`

	// 续期业务token 验证
	ExtendPeriodToken string `toml:"ExtendPeriodToken"`
}

type Config struct {
	DataStorageBos *BosConfig
	StsEndpoint    string `toml:"StsEndpoint"`
}

// LoadBosConfig 加载Bos配置，返回一个指向BosConfig结构体的指针和一个error类型的错误信息
func LoadBosConfig() (*Config, error) {
	var conf Config
	bosConfPath := env.ConfDir() + "/business/bos.toml"
	_, err := toml.DecodeFile(bosConfPath, &conf)
	if err != nil {
		return nil, err
	}
	return &conf, nil
}

var DataStorageBosConfigGlobal *BosConfig

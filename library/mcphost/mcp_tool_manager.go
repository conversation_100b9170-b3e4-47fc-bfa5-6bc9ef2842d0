package mcphost

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/client/transport"
	"github.com/mark3labs/mcp-go/mcp"
)

// MCPToolManager MCP工具管理器，使用mcphost实现
type MCPToolManager struct {
	clients map[string]client.MCPClient
}

// NewMCPToolManager 创建MCP工具管理器
func NewMCPToolManager() *MCPToolManager {
	return &MCPToolManager{
		clients: make(map[string]client.MCPClient),
	}
}

func (m *MCPToolManager) LoadTools(ctx context.Context, config *Config) error {
	var loadErrors []string

	for serverName, serverConfig := range config.MCPServers {
		if err := m.loadServerTools(ctx, serverName, serverConfig); err != nil {
			loadErrors = append(loadErrors, fmt.Sprintf("server %s: %v", serverName, err))
			fmt.Printf("Warning: Failed to load MCP server '%s': %v\n", serverName, err)
			continue
		}
	}

	// If all servers failed to load, return an error
	if len(loadErrors) == len(config.MCPServers) && len(config.MCPServers) > 0 {
		return fmt.Errorf("all MCP servers failed to load: %s", strings.Join(loadErrors, "; "))
	}

	return nil
}

// loadServerTools loads tools from a single MCP server
func (m *MCPToolManager) loadServerTools(ctx context.Context, serverName string, serverConfig MCPServerConfig) error {
	client, err := m.createMCPClient(ctx, serverName, serverConfig)
	if err != nil {
		return fmt.Errorf("failed to create MCP client: %v", err)
	}

	m.clients[serverName] = client

	// Initialize the client
	if err := m.initializeClient(ctx, client); err != nil {
		return fmt.Errorf("failed to initialize MCP client: %v", err)
	}

	// Create name set for allowed tools
	var nameSet map[string]struct{}
	if len(serverConfig.AllowedTools) > 0 {
		nameSet = make(map[string]struct{})
		for _, name := range serverConfig.AllowedTools {
			nameSet[name] = struct{}{}
		}
	}

	return nil
}

func (m *MCPToolManager) GetAvailableTools(ctx context.Context) (map[string][]mcp.Tool, error) {
	tools := map[string][]mcp.Tool{}
	for serverName, client := range m.clients {
		listResults, err := client.ListTools(ctx, mcp.ListToolsRequest{})
		if err != nil {
			return nil, fmt.Errorf("failed to list tools: %v", err)
		}
		tools[serverName] = listResults.Tools
	}
	return tools, nil
}

func (m *MCPToolManager) GetClient(serverName string) client.MCPClient {
	return m.clients[serverName]
}

// InvokableRun 执行工具调用
func (t *MCPToolManager) InvokableRun(ctx context.Context,
	serverName string, toolName string, argumentsInJSON string) (string, error) {
	// Handle empty or invalid JSON arguments
	var arguments any
	if argumentsInJSON == "" || argumentsInJSON == "{}" {
		arguments = nil
	} else {
		// Validate that argumentsInJSON is valid JSON before using it
		var temp any
		if err := json.Unmarshal([]byte(argumentsInJSON), &temp); err != nil {
			return "", fmt.Errorf("invalid JSON arguments: %w", err)
		}
		arguments = json.RawMessage(argumentsInJSON)
	}
	if _, ok := t.clients[serverName]; !ok {
		return "", fmt.Errorf("mcp server %s not found", serverName)
	}
	result, err := t.clients[serverName].CallTool(ctx, mcp.CallToolRequest{
		Request: mcp.Request{
			Method: "tools/call",
		},
		Params: struct {
			Name      string    `json:"name"`
			Arguments any       `json:"arguments,omitempty"`
			Meta      *mcp.Meta `json:"_meta,omitempty"`
		}{
			Name:      toolName,
			Arguments: arguments,
		},
	})
	if err != nil {
		return "", fmt.Errorf("failed to call mcp tool: %w", err)
	}

	marshaledResult, err := sonic.MarshalString(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal mcp tool result: %w", err)
	}

	// If the MCP server returned an error, we still return the error content as the response
	// to the LLM so it can see what went wrong. The error will be shown to the user via
	// the UI callbacks, but the LLM needs to see the actual error details to continue
	// the conversation appropriately.
	return marshaledResult, nil
}

// Close 关闭所有MCP客户端
func (m *MCPToolManager) Close() error {
	for _, client := range m.clients {
		if err := client.Close(); err != nil {
			return fmt.Errorf("关闭客户端失败: %w", err)
		}
	}
	m.clients = make(map[string]client.MCPClient)
	return nil
}

func (m *MCPToolManager) createMCPClient(ctx context.Context, serverName string, serverConfig MCPServerConfig) (client.MCPClient, error) {
	transportType := serverConfig.GetTransportType()

	switch transportType {
	case "stdio":
		// STDIO client
		var env []string
		var command string
		var args []string

		// Handle command and environment
		if len(serverConfig.Command) > 0 {
			command = serverConfig.Command[0]
			if len(serverConfig.Command) > 1 {
				args = serverConfig.Command[1:]
			} else if len(serverConfig.Args) > 0 {
				// Legacy fallback: Command only has the command, Args has the arguments
				// This handles cases where legacy config conversion didn't work properly
				args = serverConfig.Args
			}
		}

		// Convert environment variables
		if serverConfig.Environment != nil {
			for k, v := range serverConfig.Environment {
				env = append(env, fmt.Sprintf("%s=%s", k, v))
			}
		}

		// Legacy environment support
		if serverConfig.Env != nil {
			for k, v := range serverConfig.Env {
				env = append(env, fmt.Sprintf("%s=%v", k, v))
			}
		}

		stdioClient, err := client.NewStdioMCPClient(command, env, args...)
		if err != nil {
			return nil, fmt.Errorf("failed to create stdio client: %v", err)
		}

		// Add a brief delay to allow the process to start and potentially fail
		time.Sleep(100 * time.Millisecond)

		// TODO: Add process health check here if the mcp-go library exposes process info
		// For now, we rely on the timeout in initializeClient to catch dead processes

		return stdioClient, nil

	case "sse":
		// SSE client
		var options []transport.ClientOption

		// Add headers if specified
		if len(serverConfig.Headers) > 0 {
			headers := make(map[string]string)
			for key, value := range serverConfig.Headers {
				headers[key] = value.(string)
			}
			if len(headers) > 0 {
				options = append(options, transport.WithHeaders(headers))
			}
		}

		sseClient, err := client.NewSSEMCPClient(serverConfig.URL, options...)
		if err != nil {
			return nil, err
		}

		// Start the SSE client
		if err := sseClient.Start(ctx); err != nil {
			return nil, fmt.Errorf("failed to start SSE client: %v", err)
		}

		return sseClient, nil

	case "streamable":
		// Streamable HTTP client
		var options []transport.StreamableHTTPCOption

		// Add headers if specified
		if len(serverConfig.Headers) > 0 {
			headers := make(map[string]string)
			for key, value := range serverConfig.Headers {
				headers[key] = value.(string)
			}
			if len(headers) > 0 {
				options = append(options, transport.WithHTTPHeaders(headers))
			}
		}

		streamableClient, err := client.NewStreamableHttpClient(serverConfig.URL, options...)
		if err != nil {
			return nil, err
		}

		// Start the streamable HTTP client
		if err := streamableClient.Start(ctx); err != nil {
			return nil, fmt.Errorf("failed to start streamable HTTP client: %v", err)
		}

		return streamableClient, nil

	default:
		return nil, fmt.Errorf("unsupported transport type '%s' for server %s", transportType, serverName)
	}
}

func (m *MCPToolManager) initializeClient(ctx context.Context, client client.MCPClient) error {
	// Create a timeout context for initialization to prevent deadlocks
	initCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "mcphost",
		Version: "1.0.0",
	}
	initRequest.Params.Capabilities = mcp.ClientCapabilities{}

	_, err := client.Initialize(initCtx, initRequest)
	if err != nil {
		return fmt.Errorf("initialization timeout or failed: %v", err)
	}
	return nil
}

package errcode

import (
	"context"
	"fmt"
	"runtime"
	"strings"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
)

// NewCustomErr 创建 CustomErr 对象
func NewCustomErr(ctx context.Context, err error, errorCode int, details ...string) *lib_error.CustomErr {

	// 如果输入的错误已经是 CustomErr 类型，直接透传，避免重复包装
	if customErr, ok := err.(*lib_error.CustomErr); ok {
		// 记录透传日志
		resource.LoggerService.Warning(ctx,
			fmt.Sprintf("Error passthrough - ErrorCode: %d, original_code: %d, message: %s",
				errorCode, customErr.Code, customErr.Msg))
		return customErr
	}

	// 获取调用栈信息
	pc, file, line, ok := runtime.Caller(1)
	var funcName string
	var fileInfo string

	if ok {
		// 提取函数名
		fn := runtime.FuncForPC(pc)
		if fn != nil {
			funcName = fn.Name()
			if idx := strings.LastIndex(funcName, "."); idx != -1 {
				funcName = funcName[idx+1:]
			}
		}

		// 保留文件相对路径（从GOPATH或go mod根目录开始）
		fileInfo = fmt.Sprintf("%s:%d", file, line)
	}

	// 获取安全的错误消息
	errMsg := ""
	if err != nil {
		errMsg += err.Error()
	}
	if len(details) > 0 {
		errMsg += strings.Join(details, " ")
	}
	if errMsg == "" {
		errMsg = "no more messages"
	}

	// 记录详细的错误日志
	logMessage := fmt.Sprintf("Error occurred - ErrorCode: %d, details: %s", errorCode, errMsg)
	if funcName != "" {
		logMessage += fmt.Sprintf(", function: %s", funcName)
	}
	if fileInfo != "" {
		logMessage += fmt.Sprintf(", location: %s", fileInfo)
	}

	resource.LoggerService.Error(ctx, logMessage)

	return &lib_error.CustomErr{
		Code: errorCode,
		Msg:  errMsg,
	}
}

// AssertCustomError 统一的错误断言函数，用于测试中判断CustomErr类型和属性
// 参数:
//   - err: 要检查的错误
//   - expectedCode: 期望的错误码
//   - expectedMsgContains: 期望错误消息包含的内容（可选，为空则不检查）
//
// 返回:
//   - isCustomErr: 是否为CustomErr类型
//   - actualCode: 实际错误码（如果是CustomErr）
//   - actualMsg: 实际错误消息（如果是CustomErr）
func AssertCustomError(err error, expectedCode int, expectedMsgContains string) (isCustomErr bool, actualCode int, actualMsg string) {
	if err == nil {
		return false, 0, ""
	}

	if customErr, ok := err.(*lib_error.CustomErr); ok {
		return true, customErr.Code, customErr.Msg
	}

	return false, 0, ""
}

// IsCustomErrorWithCode 检查错误是否为指定错误码的CustomErr
func IsCustomErrorWithCode(err error, expectedCode int) bool {
	if customErr, ok := err.(*lib_error.CustomErr); ok {
		return customErr.Code == expectedCode
	}
	return false
}

// GetCustomErrorDetails 获取CustomErr的详细信息，如果不是CustomErr则返回默认值
func GetCustomErrorDetails(err error) (code int, msg string, isCustomErr bool) {
	if customErr, ok := err.(*lib_error.CustomErr); ok {
		return customErr.Code, customErr.Msg, true
	}
	return 0, "", false
}

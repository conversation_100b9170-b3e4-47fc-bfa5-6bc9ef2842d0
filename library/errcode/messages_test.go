package errcode_test

import (
	"context"
	"errors"
	"testing"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
)

func init() {
	bootstrap.MustInitTest(context.Background())
}

func TestNewCustomErr(t *testing.T) {

	tests := []struct {
		name              string
		inputErr          error
		errorCode         int
		details           []string
		expectedCode      int
		expectedMsg       string
		shouldPassthrough bool
	}{
		{
			name:         "数据库插入错误",
			inputErr:     errors.New("duplicate key value violates unique constraint"),
			errorCode:    errcode.SQLInsertError,
			details:      []string{"用户数据插入失败"},
			expectedCode: errcode.SQLInsertError,
			expectedMsg:  "duplicate key value violates unique constraint用户数据插入失败",
		},
		{
			name:         "数据库查询错误",
			inputErr:     errors.New("connection timeout"),
			errorCode:    errcode.SQLSelectError,
			details:      []string{"查询用户信息失败"},
			expectedCode: errcode.SQLSelectError,
			expectedMsg:  "connection timeout查询用户信息失败",
		},
		{
			name:         "文件读取错误",
			inputErr:     errors.New("no such file or directory"),
			errorCode:    errcode.SysFileReadError,
			details:      []string{"配置文件读取失败"},
			expectedCode: errcode.SysFileReadError,
			expectedMsg:  "no such file or directory配置文件读取失败",
		},
		{
			name:         "无原始错误，仅错误码",
			inputErr:     nil,
			errorCode:    errcode.AuthTokenMissing,
			details:      nil,
			expectedCode: errcode.AuthTokenMissing,
			expectedMsg:  "no more messages", // 无原始错误和details时，返回默认消息
		},
		{
			name:         "未定义错误码使用详细信息",
			inputErr:     errors.New("unknown error"),
			errorCode:    99999, // 未定义的错误码
			details:      []string{"自定义错误消息"},
			expectedCode: 99999,
			expectedMsg:  "unknown error自定义错误消息",
		},
		{
			name:              "CustomErr透传测试",
			inputErr:          &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: "原始错误消息"},
			errorCode:         errcode.SQLInsertError, // 传入不同的错误码
			details:           []string{"新的错误详情"},
			expectedCode:      errcode.SQLUpdateError, // 应该保持原始错误码
			expectedMsg:       "原始错误消息",               // 应该保持原始消息
			shouldPassthrough: true,
		},
		{
			name:         "认证令牌无效错误",
			inputErr:     errors.New("jwt token expired"),
			errorCode:    errcode.AuthTokenExpired,
			details:      []string{"令牌已过期"},
			expectedCode: errcode.AuthTokenExpired,
			expectedMsg:  "jwt token expired令牌已过期",
		},
		{
			name:         "K8s限流错误",
			inputErr:     errors.New("rate limit exceeded"),
			errorCode:    errcode.K8sRateLimitError,
			details:      []string{"请求过于频繁"},
			expectedCode: errcode.K8sRateLimitError,
			expectedMsg:  "rate limit exceeded请求过于频繁",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := errcode.NewCustomErr(context.Background(), tt.inputErr, tt.errorCode, tt.details...)

			// 验证返回的错误不为空
			if result == nil {
				t.Fatal("期望返回非空错误，但得到了nil")
			}

			// 验证错误码
			if result.Code != tt.expectedCode {
				t.Errorf("错误码不匹配：期望 %d，实际 %d", tt.expectedCode, result.Code)
			}

			// 验证错误消息
			if result.Msg != tt.expectedMsg {
				t.Errorf("错误消息不匹配：期望 '%s'，实际 '%s'", tt.expectedMsg, result.Msg)
			}

			// 对于CustomErr透传的情况，验证是否确实进行了透传
			if tt.shouldPassthrough {
				if originalErr, ok := tt.inputErr.(*lib_error.CustomErr); ok {
					if result != originalErr {
						t.Error("期望透传原始CustomErr对象，但创建了新对象")
					}
				}
			}
		})
	}
}

func TestNewCustomErr_ErrorMessageMapping(t *testing.T) {
	// 测试错误码到消息的拼接行为
	tests := []struct {
		name        string
		errorCode   int
		expectedMsg string
	}{
		{"SQL插入错误", errcode.SQLInsertError, "test error"},
		{"SQL更新错误", errcode.SQLUpdateError, "test error"},
		{"SQL删除错误", errcode.SQLDeleteError, "test error"},
		{"SQL连接错误", errcode.SQLConnectionError, "test error"},
		{"文件读取错误", errcode.SysFileReadError, "test error"},
		{"文件创建错误", errcode.SysFileCreateError, "test error"},
		{"目录创建错误", errcode.SysDirCreateError, "test error"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := errcode.NewCustomErr(context.Background(), errors.New("test error"), tt.errorCode)

			if result.Msg != tt.expectedMsg {
				t.Errorf("错误消息不匹配：期望 '%s'，实际 '%s'", tt.expectedMsg, result.Msg)
			}
		})
	}
}

func TestNewCustomErr_FilePathLogging(t *testing.T) {
	// 测试文件路径在日志中的显示
	result := errcode.NewCustomErr(context.Background(), errors.New("test error"), errcode.SQLInsertError, "测试文件路径显示")

	// 验证基本功能正常（主要是确保不会panic，实际的日志路径需要查看日志输出）
	if result == nil {
		t.Fatal("期望返回非空错误")
	}
	if result.Code != errcode.SQLInsertError {
		t.Errorf("错误码不匹配：期望 %d，实际 %d", errcode.SQLInsertError, result.Code)
	}

	// 注意：实际的文件路径显示需要查看日志输出，应该显示类似：
	// location: library/errcode/messages_test.go:行号
	// 而不是仅仅显示: messages_test.go:行号
}

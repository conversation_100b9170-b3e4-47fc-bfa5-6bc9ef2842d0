package errcode

const (
	ProjectBase = 33000

	SysBase = 0

	// Database Operations (1-20)
	SQLSelectError      = 1 + SysBase + ProjectBase
	SQLSelectCountError = 2 + SysBase + ProjectBase
	SQLUpdateError      = 3 + SysBase + ProjectBase
	SQLInsertError      = 4 + SysBase + ProjectBase
	SQLDeleteError      = 5 + SysBase + ProjectBase
	SQLConnectionError  = 6 + SysBase + ProjectBase
	SQLTransactionError = 7 + SysBase + ProjectBase
	SQLConstraintError  = 8 + SysBase + ProjectBase
	SQLTimeoutError     = 9 + SysBase + ProjectBase

	// File Operations (21-40) - Moved to new range for better organization
	SysFileReadError       = 21 + SysBase + ProjectBase
	SysFileOpenError       = 22 + SysBase + ProjectBase
	SysFileRemoveError     = 23 + SysBase + ProjectBase
	SysFileCreateError     = 24 + SysBase + ProjectBase
	SysDirCreateError      = 25 + SysBase + ProjectBase
	SysFileNotInit         = 26 + SysBase + ProjectBase
	SysFilePermissionError = 27 + SysBase + ProjectBase

	// Input Validation (41-60) - Enhanced validation error categorization
	InvalidInput         = 41 + SysBase + ProjectBase
	UserInputError       = 42 + SysBase + ProjectBase
	RequiredFieldMissing = 43 + SysBase + ProjectBase
	InvalidFieldFormat   = 44 + SysBase + ProjectBase
	InvalidFieldValue    = 45 + SysBase + ProjectBase
	InvalidFieldLength   = 46 + SysBase + ProjectBase
	DuplicateFieldValue  = 47 + SysBase + ProjectBase

	// Configuration/System (61-80)
	BRCCGetError       = 61 + SysBase + ProjectBase
	ConfigurationError = 62 + SysBase + ProjectBase
	SystemInitError    = 63 + SysBase + ProjectBase

	AuthBase               = 50
	AuthTokenMissing       = 1 + AuthBase + ProjectBase
	AuthTokenInvalid       = 2 + AuthBase + ProjectBase
	AuthTokenExpired       = 3 + AuthBase + ProjectBase
	AuthPermissionDenied   = 4 + AuthBase + ProjectBase
	AuthUserNotFound       = 5 + AuthBase + ProjectBase
	AuthCredentialsInvalid = 6 + AuthBase + ProjectBase

	McpServerBase       = 100
	McpServerInitFailed = 1 + McpServerBase + ProjectBase
	McpServerNotFound   = 2 + McpServerBase + ProjectBase

	EnvironmentBase       = 200
	EnvironmentInitFailed = 1 + EnvironmentBase + ProjectBase
	EnvironmentNotFound   = 2 + EnvironmentBase + ProjectBase

	BosBase              = 300
	BosUploadObjectError = 1 + BosBase + ProjectBase
	BosGetObjectError    = 2 + BosBase + ProjectBase
	BosDeleteObjectError = 3 + BosBase + ProjectBase
	BosGetFileMetaError  = 4 + BosBase + ProjectBase

	K8sContainerBase        = 400
	K8sContainerCreateError = 1 + K8sContainerBase + ProjectBase
	K8sContainerStartError  = 2 + K8sContainerBase + ProjectBase
	K8sContainerStopError   = 3 + K8sContainerBase + ProjectBase
	K8sContainerDeleteError = 4 + K8sContainerBase + ProjectBase
	K8sContainerGetError    = 5 + K8sContainerBase + ProjectBase
	K8sContainerListError   = 6 + K8sContainerBase + ProjectBase
	K8sRateLimitError       = 7 + K8sContainerBase + ProjectBase
	K8sJobLimitError        = 8 + K8sContainerBase + ProjectBase
	K8sExecCommandError     = 9 + K8sContainerBase + ProjectBase

	ToolCallTaskBase         = 500
	ToolCallTaskExecuteError = 1 + ToolCallTaskBase + ProjectBase
	ToolCallTaskTimeoutError = 2 + ToolCallTaskBase + ProjectBase
	ToolCallIDDuplicate      = 3 + ToolCallTaskBase + ProjectBase
	ToolCallTaskNotFound     = 4 + ToolCallTaskBase + ProjectBase

	SessionBase          = 600
	SessionInitFailed    = 1 + SessionBase + ProjectBase
	SessionStopFailed    = 2 + SessionBase + ProjectBase
	SessionTimeoutFailed = 3 + SessionBase + ProjectBase
	SessionExecFailed    = 4 + SessionBase + ProjectBase
	SessionNotFound      = 5 + SessionBase + ProjectBase
	SesssionIsStopped    = 6 + SessionBase + ProjectBase

	ImageBase     = 700
	ImageNotFound = 1 + ImageBase + ProjectBase

	RewardBase            = 800
	RewardCommandNotFound = 1 + RewardBase + ProjectBase
	RewardFailed          = 2 + RewardBase + ProjectBase
	RewardCalculatorError = 3 + RewardBase + ProjectBase
)

package reward_calculator

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	rcc "icode.baidu.com/baidu/smartprogram/rcc2-go-sdk"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
	dao_rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// RewardCalculator 奖励计算器
type RewardCalculator struct{}

// NewRewardCalculator 创建奖励计算器实例
func NewRewardCalculator() *RewardCalculator {
	return &RewardCalculator{}
}

// CalculateReward 计算奖励值
// 这是一个同步方法，会执行实际的奖励计算逻辑
// 返回值：BOS下载链接（如果数据较大）或原始数据（如果数据较小或BOS上传失败）
func (rc *RewardCalculator) CalculateReward(ctx context.Context,
	session *dao_session.ObjSession, cmd string, timeoutSecs int) (*dao_session.RewardData, error) {
	if cmd == "" {
		reward_cmd, err := getRewardCmd(ctx, session)
		if err != nil {
			return nil, errcode.NewCustomErr(ctx, err, errcode.RewardCalculatorError)
		}
		cmd = reward_cmd
	}
	if cmd == "" {
		return nil, errcode.NewCustomErr(ctx, fmt.Errorf("未找到reward命令"), errcode.RewardCommandNotFound)
	}
	//TODO 确认下使用哪种
	// cmd 按照空格切分成数组
	// cmdArr := strings.Split(cmd, " ")
	cmdArr := []string{"sh", "-c", cmd}
	// 计算奖励
	cmdReq := dao_rpc_k8s_proxy.ExecCommandRequest{
		SourceType: rcc.GetValue("k8s_proxy.init.source_type", "mcp"),
		Command:    cmdArr,
		JobName:    session.JobID,
		Timeout:    timeoutSecs,
	}
	rewardRet, err := dao_rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, &cmdReq)
	if err != nil {
		return nil, errcode.NewCustomErr(ctx, err, errcode.K8sExecCommandError)
	}

	// 构建奖励数据结构
	rewardData := &dao_session.RewardData{
		Code:    rewardRet.Code,
		Message: rewardRet.Message,
	}

	// 生成时间戳用于文件命名
	timestamp := time.Now().Format("20060102_150405")

	// 上传stderr到BOS（如果有内容）
	if rewardRet.Stderr != "" {
		stderrKey := fmt.Sprintf("calculate/reward/%s_%s_stderr.log", session.SessionCode, timestamp)
		err = dao_rpc_bos.UploadObjectFromString(ctx, stderrKey, rewardRet.Stderr, "text/plain; charset=utf-8")
		if err != nil {
			return nil, errcode.NewCustomErr(ctx, err, errcode.BosUploadObjectError)
		}
		rewardData.StderrURL = dao_rpc_bos.GenerateVisitURL(ctx, stderrKey, -1)
	}

	// 上传stdout到BOS（如果有内容）
	if rewardRet.Stdout != "" {
		stdoutKey := fmt.Sprintf("calculate/reward/%s_%s_stdout.log", session.SessionCode, timestamp)
		err = dao_rpc_bos.UploadObjectFromString(ctx, stdoutKey, rewardRet.Stdout, "text/plain; charset=utf-8")
		if err != nil {
			return nil, errcode.NewCustomErr(ctx, err, errcode.BosUploadObjectError)
		}
		rewardData.StdoutURL = dao_rpc_bos.GenerateVisitURL(ctx, stdoutKey, -1)
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("奖励数据计算完成: session_code=%s, code=%d", session.SessionCode, rewardRet.Code))
	return rewardData, nil
}

func getRewardCmd(ctx context.Context, session *dao_session.ObjSession) (string, error) {

	// 获取环境配置
	env, err := dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, session.EnvID)
	if err != nil {
		return "", err
	}

	// 从JSONData中提取environment_dependency
	envDependencyData, ok := env.EnvDependency["environment_dependency"]
	if !ok {
		return "", errcode.NewCustomErr(ctx, fmt.Errorf("环境依赖配置中没有environment_dependency字段"), errcode.RewardCommandNotFound)
	}

	// 将interface{}转换为环境依赖列表
	var envDeps []struct {
		Path    string `json:"path"`
		Type    string `json:"type"`
		Name    string `json:"name"`
		Content string `json:"content"`
	}
	// 先转换为JSON字节，再解析
	envDepsBytes, err := json.Marshal(envDependencyData)
	if err != nil {
		return "", errcode.NewCustomErr(ctx, err, errcode.RewardCommandNotFound)
	}
	err = json.Unmarshal(envDepsBytes, &envDeps)
	if err != nil {
		return "", errcode.NewCustomErr(ctx, err, errcode.RewardCommandNotFound)
	}

	reward_cmd := ""
	for _, item := range envDeps {
		if item.Type == "delay_cmd" && item.Name == "reward" {
			reward_cmd = item.Content
			break
		}
	}
	return reward_cmd, nil
}

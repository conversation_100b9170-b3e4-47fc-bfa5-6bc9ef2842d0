package metrics_helper

import (
	"time"
)

// MetricsRecorder 定义metrics记录接口，避免循环依赖
type MetricsRecorder interface {
	RecordSessionStatusChange(status string)
	RecordToolCallStatusChange(status string)
	RecordSessionStatusTransition(fromStatus, toStatus string, transitionTime time.Duration, sessionStartTime *time.Time)
	RecordToolCallStatusTransition(fromStatus, toStatus, toolName string, transitionTime time.Duration)
}

// globalRecorder 全局metrics记录器
var globalRecorder MetricsRecorder

// SetMetricsRecorder 设置全局metrics记录器
func SetMetricsRecorder(recorder MetricsRecorder) {
	globalRecorder = recorder
}

// RecordSessionCreated 记录session创建
func RecordSessionCreated() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("init")
	}
}

// RecordSessionPending 记录session变为pending状态
func RecordSessionPending() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("pending")
	}
}

// RecordSessionRunning 记录session变为running状态
func RecordSessionRunning() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("running")
	}
}

// RecordSessionRewarding 记录session变为rewarding状态
func RecordSessionRewarding() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("rewarding")
	}
}

// RecordSessionStopping 记录session变为stopping状态
func RecordSessionStopping() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("stopping")
	}
}

// RecordSessionStopped 记录session停止
func RecordSessionStopped() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("stopped")
	}
}

// RecordSessionTimeout 记录session超时
func RecordSessionTimeout() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("timeout")
	}
}

// RecordSessionFailed 记录session失败
func RecordSessionFailed() {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusChange("failed")
	}
}

// RecordToolCallCreated 记录tool call创建
func RecordToolCallCreated() {
	if globalRecorder != nil {
		globalRecorder.RecordToolCallStatusChange("pending")
	}
}

// RecordToolCallRunning 记录tool call变为running状态
func RecordToolCallRunning() {
	if globalRecorder != nil {
		globalRecorder.RecordToolCallStatusChange("running")
	}
}

// RecordToolCallSuccess 记录tool call成功
func RecordToolCallSuccess() {
	if globalRecorder != nil {
		globalRecorder.RecordToolCallStatusChange("success")
	}
}

// RecordToolCallFailed 记录tool call失败
func RecordToolCallFailed() {
	if globalRecorder != nil {
		globalRecorder.RecordToolCallStatusChange("failed")
	}
}

// RecordToolCallTimeout 记录tool call超时
func RecordToolCallTimeout() {
	if globalRecorder != nil {
		globalRecorder.RecordToolCallStatusChange("timeout")
	}
}

// RecordSessionStatusTransition 记录session状态转换
func RecordSessionStatusTransition(fromStatus, toStatus string, transitionTime time.Duration, sessionStartTime *time.Time) {
	if globalRecorder != nil {
		globalRecorder.RecordSessionStatusTransition(fromStatus, toStatus, transitionTime, sessionStartTime)
	}
}

// RecordToolCallStatusTransition 记录tool call状态转换
func RecordToolCallStatusTransition(fromStatus, toStatus, toolName string, transitionTime time.Duration) {
	if globalRecorder != nil {
		globalRecorder.RecordToolCallStatusTransition(fromStatus, toStatus, toolName, transitionTime)
	}
}

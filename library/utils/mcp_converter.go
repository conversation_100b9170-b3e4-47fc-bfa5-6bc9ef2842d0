package utils

import (
	"fmt"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

// ConvertMCPToolsToJSONData 将MCPTool列表转换为JSONData格式
// 这是一个通用工具函数，可以在多个服务中复用
func ConvertMCPToolsToJSONData(mcpTools []types.MCPTool) (*dao_base.JSONData, error) {
	// 将MCPTool列表序列化为interface{}，然后包装到JSONData中
	toolsInterface := make([]interface{}, len(mcpTools))
	for i, tool := range mcpTools {
		toolMap := map[string]interface{}{
			"name":        tool.Name,
			"description": tool.Description,
			"parameters":  tool.Parameters,
		}
		toolsInterface[i] = toolMap
	}

	// 创建JSONData并包含工具列表
	jsonData := dao_base.JSONData{
		"tools": toolsInterface,
	}

	return &jsonData, nil
}

// ConvertJSONDataToMCPTools 将JSONData转换为MCPTool列表
// 这是一个通用工具函数，可以在多个服务中复用
func ConvertJSONDataToMCPTools(jsonData *dao_base.JSONData) ([]types.MCPTool, error) {
	if jsonData == nil {
		return []types.MCPTool{}, nil
	}

	// 从JSONData中提取tools字段
	toolsData, exists := (*jsonData)["tools"]
	if !exists {
		return []types.MCPTool{}, nil
	}

	// 将tools字段转换为接口数组
	toolsArray, ok := toolsData.([]interface{})
	if !ok {
		return nil, fmt.Errorf("tools字段不是数组格式")
	}

	// 解析每个工具
	mcpTools := make([]types.MCPTool, 0, len(toolsArray))
	for _, toolData := range toolsArray {
		toolMap, ok := toolData.(map[string]interface{})
		if !ok {
			continue // 跳过不是map格式的项
		}

		tool := types.MCPTool{}

		if name, ok := toolMap["name"].(string); ok {
			tool.Name = name
		}
		if desc, ok := toolMap["description"].(string); ok {
			tool.Description = desc
		}
		if params, ok := toolMap["parameters"].(map[string]interface{}); ok {
			tool.Parameters = params
		}

		mcpTools = append(mcpTools, tool)
	}

	return mcpTools, nil
}

// ValidateMCPTool 验证MCPTool数据的完整性
func ValidateMCPTool(tool types.MCPTool) error {
	if tool.Name == "" {
		return fmt.Errorf("工具名称不能为空")
	}
	if tool.Description == "" {
		return fmt.Errorf("工具描述不能为空")
	}
	return nil
}

// ValidateMCPTools 批量验证MCPTool列表
func ValidateMCPTools(tools []types.MCPTool) error {
	for i, tool := range tools {
		if err := ValidateMCPTool(tool); err != nil {
			return fmt.Errorf("工具[%d]验证失败: %v", i, err)
		}
	}
	return nil
}

package utils

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestSafeRemoveFile 测试安全删除文件功能
func TestSafeRemoveFile(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "test_safe_remove_")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 测试删除存在的文件
	t.Run("删除存在的文件", func(t *testing.T) {
		// 创建测试文件
		testFile := filepath.Join(tempDir, "test_file.txt")
		if err := os.WriteFile(testFile, []byte("测试内容"), 0644); err != nil {
			t.Fatalf("创建测试文件失败: %v", err)
		}

		// 验证文件存在
		if _, err := os.Stat(testFile); os.IsNotExist(err) {
			t.Fatalf("测试文件不存在")
		}

		// 安全删除文件
		SafeRemoveFile(testFile)

		// 验证文件已被删除
		if _, err := os.Stat(testFile); !os.IsNotExist(err) {
			t.Errorf("文件应该被删除")
		}
	})

	// 测试删除不存在的文件
	t.Run("删除不存在的文件", func(t *testing.T) {
		nonExistentFile := filepath.Join(tempDir, "non_existent_file.txt")

		// 确保文件不存在
		if _, err := os.Stat(nonExistentFile); !os.IsNotExist(err) {
			t.Fatalf("文件应该不存在")
		}

		// 安全删除不存在的文件 - 这应该不会panic或返回错误
		SafeRemoveFile(nonExistentFile)

		// 这里没有断言，因为SafeRemoveFile应该静默处理不存在的文件
	})

	// 测试删除只读文件
	t.Run("删除只读文件", func(t *testing.T) {
		// 创建只读文件
		readOnlyFile := filepath.Join(tempDir, "readonly_file.txt")
		if err := os.WriteFile(readOnlyFile, []byte("只读内容"), 0444); err != nil {
			t.Fatalf("创建只读文件失败: %v", err)
		}

		// 安全删除只读文件
		SafeRemoveFile(readOnlyFile)

		// 验证文件已被删除
		if _, err := os.Stat(readOnlyFile); !os.IsNotExist(err) {
			t.Errorf("只读文件应该被删除")
		}
	})
}

// TestSafeRemoveFileWithPermissionError 测试权限错误情况
func TestSafeRemoveFileWithPermissionError(t *testing.T) {
	// 跳过在Windows上的权限测试
	if os.Getenv("GOOS") == "windows" {
		t.Skip("跳过Windows上的权限测试")
	}

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "test_permission_")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建文件
	testFile := filepath.Join(tempDir, "test_file.txt")
	if err := os.WriteFile(testFile, []byte("测试内容"), 0644); err != nil {
		t.Fatalf("创建测试文件失败: %v", err)
	}

	// 移除父目录的写权限
	if err := os.Chmod(tempDir, 0555); err != nil {
		t.Fatalf("修改目录权限失败: %v", err)
	}

	// 尝试删除文件 - 应该会失败但不会panic
	SafeRemoveFile(testFile)

	// 恢复权限以便清理
	if err := os.Chmod(tempDir, 0755); err != nil {
		t.Fatalf("恢复目录权限失败: %v", err)
	}
}

// TestExtractFileNameFromURL 测试从URL提取文件名
func TestExtractFileNameFromURL(t *testing.T) {
	testCases := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "简单文件名",
			url:      "https://example.com/server.py",
			expected: "server.py",
		},
		{
			name:     "压缩文件",
			url:      "https://example.com/path/to/server.zip",
			expected: "server.zip",
		},
		{
			name:     "带查询参数的URL",
			url:      "https://example.com/server.tar.gz?version=1.0&token=abc123",
			expected: "server.tar.gz",
		},
		{
			name:     "带片段的URL",
			url:      "https://example.com/server.sh#section1",
			expected: "server.sh",
		},
		{
			name:     "复杂URL",
			url:      "https://example.com/path/to/my-server-v1.2.3.tar.gz?download=true&format=compressed#readme",
			expected: "my-server-v1.2.3.tar.gz",
		},
		{
			name:     "无文件名的URL",
			url:      "https://example.com/path/to/",
			expected: "",
		},
		{
			name:     "根路径",
			url:      "https://example.com/",
			expected: "",
		},
		{
			name:     "无扩展名文件",
			url:      "https://example.com/server",
			expected: "server",
		},
		{
			name:     "带端口的URL",
			url:      "https://example.com:8080/server.py",
			expected: "server.py",
		},
		{
			name:     "本地文件路径",
			url:      "file:///home/<USER>/server.py",
			expected: "server.py",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := ExtractFileNameFromURL(tc.url)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsCompressedFile 测试压缩文件检测
func TestIsCompressedFile(t *testing.T) {
	testCases := []struct {
		name       string
		fileName   string
		compressed bool
	}{
		{
			name:       "ZIP文件",
			fileName:   "server.zip",
			compressed: true,
		},
		{
			name:       "TAR.GZ文件",
			fileName:   "server.tar.gz",
			compressed: true,
		},
		{
			name:       "TGZ文件",
			fileName:   "server.tgz",
			compressed: true,
		},
		{
			name:       "TAR文件",
			fileName:   "server.tar",
			compressed: true,
		},
		{
			name:       "GZ文件",
			fileName:   "server.gz",
			compressed: true,
		},
		{
			name:       "RAR文件",
			fileName:   "server.rar",
			compressed: true,
		},
		{
			name:       "7Z文件",
			fileName:   "server.7z",
			compressed: true,
		},
		{
			name:       "Python脚本",
			fileName:   "server.py",
			compressed: false,
		},
		{
			name:       "Shell脚本",
			fileName:   "server.sh",
			compressed: false,
		},
		{
			name:       "二进制文件",
			fileName:   "server",
			compressed: false,
		},
		{
			name:       "文本文件",
			fileName:   "server.txt",
			compressed: false,
		},
		{
			name:       "JavaScript文件",
			fileName:   "server.js",
			compressed: false,
		},
		{
			name:       "大写扩展名",
			fileName:   "SERVER.ZIP",
			compressed: true,
		},
		{
			name:       "混合大小写",
			fileName:   "Server.Tar.Gz",
			compressed: true,
		},
		{
			name:       "空文件名",
			fileName:   "",
			compressed: false,
		},
		{
			name:       "只有扩展名",
			fileName:   ".zip",
			compressed: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := IsCompressedFile(tc.fileName)
			assert.Equal(t, tc.compressed, result)
		})
	}
}

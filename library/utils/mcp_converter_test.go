package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

// TestConvertMCPToolsToJSONData 测试MCPTool到JSONData的转换
func TestConvertMCPToolsToJSONData(t *testing.T) {
	// 测试空工具列表
	tools := []types.MCPTool{}
	jsonData, err := ConvertMCPToolsToJSONData(tools)
	assert.NoError(t, err)
	assert.NotNil(t, jsonData)

	// 测试有效工具列表
	tools = []types.MCPTool{
		{
			Name:        "file_operations",
			Description: "文件操作工具",
			Parameters: map[string]interface{}{
				"path": map[string]interface{}{
					"type":        "string",
					"description": "文件路径",
					"required":    true,
				},
			},
		},
		{
			Name:        "web_search",
			Description: "网络搜索工具",
			Parameters: map[string]interface{}{
				"query": map[string]interface{}{
					"type":        "string",
					"description": "搜索关键词",
					"required":    true,
				},
			},
		},
	}

	jsonData, err = ConvertMCPToolsToJSONData(tools)
	assert.NoError(t, err)
	assert.NotNil(t, jsonData)

	// 验证转换后的数据包含工具信息
	assert.NotEmpty(t, *jsonData)

	// 验证tools字段存在
	toolsData, exists := (*jsonData)["tools"]
	assert.True(t, exists)
	assert.NotNil(t, toolsData)

	// 验证工具数量
	toolsArray, ok := toolsData.([]interface{})
	assert.True(t, ok)
	assert.Len(t, toolsArray, 2)
}

// TestConvertJSONDataToMCPTools 测试JSONData到MCPTool的转换
func TestConvertJSONDataToMCPTools(t *testing.T) {
	// 测试空数据
	tools, err := ConvertJSONDataToMCPTools(nil)
	assert.NoError(t, err)
	assert.Empty(t, tools)

	// 测试有效数据
	jsonData := &dao_base.JSONData{
		"tools": []interface{}{
			map[string]interface{}{
				"name":        "file_operations",
				"description": "文件操作工具",
				"parameters": map[string]interface{}{
					"path": map[string]interface{}{
						"type":        "string",
						"description": "文件路径",
						"required":    true,
					},
				},
			},
			map[string]interface{}{
				"name":        "web_search",
				"description": "网络搜索工具",
				"parameters": map[string]interface{}{
					"query": map[string]interface{}{
						"type":        "string",
						"description": "搜索关键词",
						"required":    true,
					},
				},
			},
		},
	}

	tools, err = ConvertJSONDataToMCPTools(jsonData)
	assert.NoError(t, err)
	assert.Len(t, tools, 2)

	// 验证第一个工具
	assert.Equal(t, "file_operations", tools[0].Name)
	assert.Equal(t, "文件操作工具", tools[0].Description)
	assert.NotNil(t, tools[0].Parameters)

	// 验证第二个工具
	assert.Equal(t, "web_search", tools[1].Name)
	assert.Equal(t, "网络搜索工具", tools[1].Description)
	assert.NotNil(t, tools[1].Parameters)
}

// TestConvertRoundTrip 测试往返转换
func TestConvertRoundTrip(t *testing.T) {
	// 原始工具列表
	originalTools := []types.MCPTool{
		{
			Name:        "test_tool",
			Description: "测试工具",
			Parameters: map[string]interface{}{
				"param1": "value1",
				"param2": map[string]interface{}{
					"nested": "value",
				},
			},
		},
	}

	// 转换为JSONData
	jsonData, err := ConvertMCPToolsToJSONData(originalTools)
	assert.NoError(t, err)

	// 再转换回MCPTool
	convertedTools, err := ConvertJSONDataToMCPTools(jsonData)
	assert.NoError(t, err)

	// 验证往返转换的一致性
	assert.Len(t, convertedTools, 1)
	assert.Equal(t, originalTools[0].Name, convertedTools[0].Name)
	assert.Equal(t, originalTools[0].Description, convertedTools[0].Description)
	assert.NotNil(t, convertedTools[0].Parameters)
}

// TestValidateMCPTool 测试单个工具验证
func TestValidateMCPTool(t *testing.T) {
	// 测试有效工具
	validTool := types.MCPTool{
		Name:        "valid_tool",
		Description: "有效工具",
		Parameters:  map[string]interface{}{},
	}
	err := ValidateMCPTool(validTool)
	assert.NoError(t, err)

	// 测试名称为空的工具
	invalidTool1 := types.MCPTool{
		Name:        "",
		Description: "描述",
		Parameters:  map[string]interface{}{},
	}
	err = ValidateMCPTool(invalidTool1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "工具名称不能为空")

	// 测试描述为空的工具
	invalidTool2 := types.MCPTool{
		Name:        "工具",
		Description: "",
		Parameters:  map[string]interface{}{},
	}
	err = ValidateMCPTool(invalidTool2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "工具描述不能为空")
}

// TestValidateMCPTools 测试工具列表验证
func TestValidateMCPTools(t *testing.T) {
	// 测试有效工具列表
	validTools := []types.MCPTool{
		{Name: "tool1", Description: "工具1"},
		{Name: "tool2", Description: "工具2"},
	}
	err := ValidateMCPTools(validTools)
	assert.NoError(t, err)

	// 测试包含无效工具的列表
	invalidTools := []types.MCPTool{
		{Name: "tool1", Description: "工具1"},
		{Name: "", Description: "工具2"}, // 名称为空
	}
	err = ValidateMCPTools(invalidTools)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "工具[1]验证失败")

	// 测试空列表
	emptyTools := []types.MCPTool{}
	err = ValidateMCPTools(emptyTools)
	assert.NoError(t, err)
}

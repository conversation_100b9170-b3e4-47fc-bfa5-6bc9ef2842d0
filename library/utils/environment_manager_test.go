package utils

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
)

func TestEnvManagerPackaging(t *testing.T) {
	// 创建临时工作目录
	workingDir := "./test_env_" + time.Now().Format("20060102150405")
	err := os.MkdirAll(workingDir, 0755)
	if err != nil {
		t.Fatalf("创建工作目录失败: %v", err)
	}
	defer os.RemoveAll(workingDir)

	// 创建一些测试文件
	testFiles := map[string]string{
		"test1.txt":        "这是测试文件1的内容",
		"test2.txt":        "这是测试文件2的内容",
		"subdir/test3.txt": "这是子目录中的测试文件",
	}

	for filename, content := range testFiles {
		filePath := workingDir + "/" + filename
		// 确保目录存在
		if dir := filepath.Dir(filePath); dir != workingDir {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatalf("创建子目录失败: %v", err)
			}
		}
		if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
			t.Fatalf("创建测试文件失败: %v", err)
		}
	}

	// 测试打包功能（不上传到BOS，因为可能没有真实的BOS配置）
	bosConfig := &config.BosConfig{
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "https://test.endpoint.com",
		Bucket:    "test-bucket",
	}

	// 由于没有真实的BOS配置，这个测试主要验证代码结构
	envMgr := NewEnvManager(workingDir, nil, bosConfig)
	if envMgr == nil {
		t.Error("环境管理器创建失败")
	}

	// 测试打包环境（但不上传到BOS）
	tempTarFile := workingDir + "/test_env.tar.gz"
	err = envMgr.packageEnvironment(context.Background(), tempTarFile)
	if err != nil {
		t.Logf("打包环境测试: %v （这可能是预期的，如果tar命令不可用）", err)
	} else {
		// 验证tar文件是否被创建
		if _, err := os.Stat(tempTarFile); err != nil {
			t.Error("tar文件未被创建")
		} else {
			t.Log("环境打包测试成功")
			os.Remove(tempTarFile)
		}
	}
}

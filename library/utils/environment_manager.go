package utils

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/charmbracelet/log"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
)

// EnvManager 环境管理器
type EnvManager struct {
	workingDir string
	bosClient  *bos.Client
	config     *config.BosConfig
}

// NewEnvManager 创建环境管理器
func NewEnvManager(workingDir string, bosClient *bos.Client, bosConfig *config.BosConfig) *EnvManager {
	return &EnvManager{
		workingDir: workingDir,
		bosClient:  bosClient,
		config:     bosConfig,
	}
}

// CreateSnapshot 创建环境快照并上传到BOS
func (e *EnvManager) CreateSnapshot(ctx context.Context) (string, string, error) {
	log.Info("开始创建环境快照")

	// 1. 创建临时tar包
	tempTarFile := filepath.Join(e.workingDir, "env.tar.gz")
	if err := e.packageEnvironment(ctx, tempTarFile); err != nil {
		return "", "", fmt.Errorf("打包环境失败: %w", err)
	}
	defer SafeRemoveFile(tempTarFile) // 安全清理临时文件

	// 2. 计算MD5
	md5Hash, fileSize, err := e.calculateFileMD5AndSize(tempTarFile)
	if err != nil {
		return "", "", fmt.Errorf("计算MD5失败: %w", err)
	}

	// 3. 重命名文件
	finalTarFile := filepath.Join(e.workingDir, fmt.Sprintf("%s.tar.gz", md5Hash))
	if err := os.Rename(tempTarFile, finalTarFile); err != nil {
		return "", "", fmt.Errorf("重命名文件失败: %w", err)
	}
	defer SafeRemoveFile(finalTarFile) // 安全清理

	// 4. 上传到BOS
	bosURL, err := e.uploadToBOS(ctx, finalTarFile, md5Hash, fileSize)
	if err != nil {
		return "", "", fmt.Errorf("上传到BOS失败: %w", err)
	}

	log.Info("环境快照创建完成", "md5", md5Hash, "bos_url", bosURL)
	return md5Hash, bosURL, nil
}

// packageEnvironment 打包环境目录
func (e *EnvManager) packageEnvironment(ctx context.Context, tarFile string) error {
	log.Info("开始打包环境", "working_dir", e.workingDir, "tar_file", tarFile)

	// 获取绝对路径
	absTarFile, err := filepath.Abs(tarFile)
	if err != nil {
		return fmt.Errorf("获取tar文件绝对路径失败: %w", err)
	}

	// 创建临时目录用于存放tar文件，避免在工作目录中创建
	tempDir, err := os.MkdirTemp("", "env_package_")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 在临时目录中创建tar文件
	tempTarFile := filepath.Join(tempDir, "env_temp.tar.gz")

	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(absTarFile), 0755); err != nil {
		return fmt.Errorf("创建目标目录失败: %w", err)
	}

	// 扩展的排除文件列表，包含更多可能在执行过程中被修改的文件
	excludeFiles := []string{
		"mcp_runtime",
		"mcp_runtime.go",
	}

	// 验证工作目录存在且可访问
	if _, err := os.Stat(e.workingDir); os.IsNotExist(err) {
		return fmt.Errorf("工作目录不存在: %s", e.workingDir)
	}

	// 检查工作目录是否可读
	if file, err := os.Open(e.workingDir); err != nil {
		return fmt.Errorf("无法访问工作目录: %s, 错误: %w", e.workingDir, err)
	} else {
		file.Close()
	}

	// 第一步：创建未压缩的tar文件，使用确定性参数
	tempTarOnlyFile := filepath.Join(tempDir, "env_temp.tar")
	args := []string{
		"-cf", tempTarOnlyFile, // 创建未压缩的tar文件
		"--owner=root",    // 统一所有者
		"--group=root",    // 统一用户组
		"--numeric-owner", // 使用数字UID/GID
	}

	// 检测tar是否支持--warning选项（主要是GNU tar支持，BSD tar不支持）
	if e.supportsTarWarningOption() {
		args = append(args, "--warning=no-file-changed")   // 忽略文件变化警告，但仍然会在严重错误时失败
		args = append(args, "--sort=name")                 // 按名称排序，确保文件顺序一致
		args = append(args, "--mtime=2025-01-01 00:00:00") // 设置固定的修改时间
	}

	for _, file := range excludeFiles {
		args = append(args, "--exclude="+file)
	}
	args = append(args, "-C", e.workingDir, ".")

	// 执行tar命令
	log.Info("执行tar命令", "args", args, "working_dir", e.workingDir)
	cmd := exec.CommandContext(ctx, "tar", args...)
	output, err := cmd.CombinedOutput()
	outputStr := string(output)

	if err != nil {
		log.Warn("tar命令执行出现错误", "error", err, "output", outputStr, "exit_code", cmd.ProcessState)

		// 检查具体的错误类型
		if strings.Contains(outputStr, "file changed as we read it") {
			log.Warn("检测到文件在打包过程中被修改，但继续处理", "output", outputStr)
			// 对于文件变化警告，我们继续处理，因为这通常不是致命错误
		} else if strings.Contains(outputStr, "Permission denied") {
			return fmt.Errorf("tar命令执行失败，权限不足: %w, 输出: %s", err, outputStr)
		} else if strings.Contains(outputStr, "No space left on device") {
			return fmt.Errorf("tar命令执行失败，磁盘空间不足: %w, 输出: %s", err, outputStr)
		} else if strings.Contains(outputStr, "Cannot stat") {
			return fmt.Errorf("tar命令执行失败，文件不存在或无法访问: %w, 输出: %s", err, outputStr)
		} else {
			// 检查工作目录是否存在
			if _, statErr := os.Stat(e.workingDir); os.IsNotExist(statErr) {
				return fmt.Errorf("tar命令执行失败，工作目录不存在: %s, 原始错误: %w, 输出: %s", e.workingDir, err, outputStr)
			}
			return fmt.Errorf("tar命令执行失败: %w, 输出: %s", err, outputStr)
		}
	} else {
		log.Info("tar命令执行成功", "output_length", len(outputStr))
	}

	// 第二步：使用gzip -n压缩tar文件，避免包含时间戳
	log.Info("开始压缩tar文件", "input", tempTarOnlyFile)
	gzipCmd := exec.CommandContext(ctx, "gzip", "-n", tempTarOnlyFile)
	gzipOutput, err := gzipCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("gzip压缩失败: %w, 输出: %s", err, string(gzipOutput))
	}

	// gzip会将.tar重命名为.tar.gz
	compressedFile := tempTarOnlyFile + ".gz"
	if _, err := os.Stat(compressedFile); os.IsNotExist(err) {
		return fmt.Errorf("gzip压缩后的文件不存在: %s", compressedFile)
	}

	// 将压缩后的文件重命名为预期的文件名
	if err := os.Rename(compressedFile, tempTarFile); err != nil {
		return fmt.Errorf("重命名压缩文件失败: %w", err)
	}

	// 将临时tar文件移动到最终位置
	if err := os.Rename(tempTarFile, absTarFile); err != nil {
		return fmt.Errorf("移动tar文件到最终位置失败: %w", err)
	}

	log.Info("环境打包完成", "output", string(output))
	return nil
}

// supportsTarWarningOption 检测tar是否支持--warning选项
func (e *EnvManager) supportsTarWarningOption() bool {
	// 运行tar --help检查是否支持--warning选项
	cmd := exec.Command("tar", "--help")
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 如果--help失败，假设不支持高级选项
		return false
	}

	// 检查输出中是否包含--warning选项
	return strings.Contains(string(output), "--warning")
}

// calculateFileMD5AndSize 计算文件的MD5和大小
func (e *EnvManager) calculateFileMD5AndSize(filePath string) (string, int64, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", 0, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 获取文件大小
	stat, err := file.Stat()
	if err != nil {
		return "", 0, fmt.Errorf("获取文件信息失败: %w", err)
	}
	fileSize := stat.Size()

	// 计算MD5
	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", 0, fmt.Errorf("读取文件计算MD5失败: %w", err)
	}

	md5Hash := fmt.Sprintf("%x", hash.Sum(nil))
	return md5Hash, fileSize, nil
}

// uploadToBOS 上传文件到BOS
func (e *EnvManager) uploadToBOS(ctx context.Context, filePath, md5Hash string, fileSize int64) (string, error) {
	// 生成BOS键
	bosKey := e.generateBosKey(md5Hash)

	// 先检查是否已存在
	if exists, existingURL, err := e.checkBOSObjectExists(ctx, bosKey); err != nil {
		return "", fmt.Errorf("检查BOS对象存在性失败: %w", err)
	} else if exists {
		log.Info("BOS对象已存在，跳过上传", "bos_key", bosKey, "url", existingURL)
		return existingURL, nil
	}

	// 上传文件
	log.Info("开始上传文件到BOS", "bos_key", bosKey, "file_size", fileSize)

	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 上传到BOS
	_, err = e.bosClient.PutObjectFromStream(e.config.Bucket, bosKey, file, nil)
	if err != nil {
		return "", fmt.Errorf("上传到BOS失败: %w", err)
	}

	// 生成访问URL
	bosURL := e.bosClient.BasicGeneratePresignedUrl(e.config.Bucket, bosKey, -1)

	log.Info("文件上传到BOS成功", "bos_key", bosKey, "url", bosURL)
	return bosURL, nil
}

// checkBOSObjectExists 检查BOS对象是否存在
func (e *EnvManager) checkBOSObjectExists(ctx context.Context, bosKey string) (bool, string, error) {
	// 尝试获取对象元数据
	_, err := e.bosClient.GetObjectMeta(e.config.Bucket, bosKey)
	if err != nil {
		// 如果是404错误，表示对象不存在
		if strings.Contains(err.Error(), "404") || strings.Contains(err.Error(), "NoSuchKey") {
			return false, "", nil
		}
		return false, "", fmt.Errorf("检查BOS对象元数据失败: %w", err)
	}

	// 对象存在，生成访问URL
	bosURL := e.bosClient.BasicGeneratePresignedUrl(e.config.Bucket, bosKey, -1)
	return true, bosURL, nil
}

// generateBosKey 根据MD5生成BOS对象键
func (e *EnvManager) generateBosKey(md5Hash string) string {
	// 使用时间戳和MD5生成唯一的BOS键
	return fmt.Sprintf("mcp_env/%s.tar.gz", md5Hash)
}

// RestoreEnvironment 从BOS URL恢复环境
func (e *EnvManager) RestoreEnvironment(ctx context.Context, bosURL string) error {
	log.Info("开始恢复环境", "bos_url", bosURL)

	// 1. 下载环境文件
	tempTarFile := filepath.Join(e.workingDir, "temp_env.tar.gz")
	if err := e.downloadFromBOS(ctx, bosURL, tempTarFile); err != nil {
		return fmt.Errorf("下载环境文件失败: %w", err)
	}
	defer SafeRemoveFile(tempTarFile) // 安全清理临时文件

	// 2. 解压环境文件
	if err := ExtractTarGz(tempTarFile, e.workingDir); err != nil {
		return fmt.Errorf("解压环境文件失败: %w", err)
	}

	log.Info("环境恢复完成")
	return nil
}

// downloadFromBOS 从BOS下载文件
func (e *EnvManager) downloadFromBOS(ctx context.Context, bosURL, localFile string) error {
	// 这里可以直接使用HTTP客户端下载，因为我们有完整的URL
	return DownloadFile(bosURL, localFile)
}

# sandbox环境
RLE_TOOL_SERVER= "http://10.11.154.204:8271"
ENV_MGR="http://10.11.154.204:8281"

import json
import requests
from datetime import datetime
import re

# 全局配置
ENV_ID = 30  # 环境ID

def extract_docker_image(data):
    """提取docker_image字段"""
    agent_env_info = data.get('agent_env_runtime_info', {})
    return agent_env_info.get('docker_image', '')

def convert_image_path(original_image):
    """转换镜像路径"""
    # 将"dockerhub"和前面的所有内容替换成swe-agent/dockerhub
    pattern = r'^.*?dockerhub/'
    converted_image = re.sub(pattern, 'swe-agent/dockerhub/', original_image)
    return converted_image

def generate_image_id(original_image):
    """生成新的image_id
    输入：swe-agent/dockerhub/namanjain12/orange3_final:d61803f7181e7ad525d4bb1dd71ca500bb41617e
    输出：namanjain12-orange3_final-20250801175808
    """
    # 提取原始镜像名称部分
    parts = original_image.split('/')
    if len(parts) >= 2:
        base_name = '-'.join(parts[-2:]) # 将namanjain12/orange3_final转为namanjain12-orange3_final
    else:
        base_name = "default"
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{base_name}-{timestamp}"

def register_image(image_id, image_path):
    """注册镜像到ENV_MGR"""
    url = f"{ENV_MGR}/api/v1/mcp/image/register"
    
    payload = {
        "image_id": image_id,
        "image_path": image_path,
        "image_description": image_path,
        "container_command": "tail",
        "container_args": ["-f"],
        "container_resources": {"cpu": 2, "memory": {"type": "G", "value": 4}},
        "root_user": True
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        print(f"成功注册镜像: {image_id}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"注册镜像失败: {e}")
        return False

def add_meta_info(data, image_id):
    """添加meta信息到数据中"""
    meta_info = {
        "tool_call_info": {
            "sweagent": {
                "docker_image_id": image_id,
                "env_id": ENV_ID
            }
        }
    }
    data["meta"] = meta_info
    return data

def process_jsonl(input_file, output_file):
    """处理JSONL文件"""
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        for line in infile:
            line = line.strip()
            if not line:
                continue
                
            try:
                # 解析JSON数据
                data = json.loads(line)
                
                # 1. 提取docker_image
                original_image = extract_docker_image(data)
                if not original_image:
                    print(f"警告: 未找到docker_image字段，跳过此行")
                    continue
                
                # 2. 转换镜像路径
                converted_image = convert_image_path(original_image)
                
                # 3. 生成image_id
                image_id = generate_image_id(original_image)
                
                # 4. 注册镜像
                if register_image(image_id, converted_image):
                    # 5. 添加meta信息
                    updated_data = add_meta_info(data, image_id)
                    
                    # 写入输出文件
                    outfile.write(json.dumps(updated_data, ensure_ascii=False) + '\n')
                    print(f"处理完成: {image_id} -> {converted_image}")
                else:
                    print(f"跳过此行，镜像注册失败: {original_image}")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                continue
            except Exception as e:
                print(f"处理错误: {e}")
                continue

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 3:
        print("使用方法: python convert.py <输入文件> <输出文件>")
        print("示例: python convert.py test.jsonl converted_test.jsonl")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    print(f"开始处理文件: {input_file}")
    process_jsonl(input_file, output_file)
    print(f"处理完成，输出文件: {output_file}")

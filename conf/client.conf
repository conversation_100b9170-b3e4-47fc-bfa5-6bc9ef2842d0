[Client]
UseTrash            : 1     # 0:Direct Delete File 1:Move File to Trash
UseBvar             : 1
ReqTimeout          : 3000  # ms
TotalTimeout        : 180000  # ms
LeaseInterval       : 5000  # ms
RefreshMasterInterval : 5000 # ms
AdaptorTrackerInterval : 60000 # ms
RefreshMasterTimeout : 30000 # ms
PushStatInterval     : 180000   # ms
PrintKylinLog        : 1
OpenAuditLog         : 0
AsyncWorkerThreads   : 4 # async callback worker
EnableMonitor        : 0 # enable monitor
BvarWindowSize       : 60  # 60s, the bvar unit is second
UserReportInterval   : 60  # 60s, user statistic data report interval
ReadUncommitted      : 0 # 0: disable read uncommitted in same filesystem instance
ClientThreads        : 3 # StateTracker=1, AFSClient=1 AuditWriter=1
LoginMethod          : 0  # 0: ugi login, 3: combineed giano login with ugi, 4: giano login
SeparateAfsLog       : 0

[.BlockService]
ReadBufferSize    : 4194304 # 4M
WriteBufferSize   : 4194304 # 4M
WriteBufferTimeout  : 2000 # ms, 2s

MetaRetryTimes    : 10
MetaReqTimeout    : 3000 # ms, 3s
MetaTotalTimeout  : 180000 # ms, 180s
DataRetryTimes    : 2
DataReqTimeout    : 10000 # ms, 10s
DataTotalTimeout  : 180000 # ms, 180s

PingTimeout       : 2000 # ms, 2s
PingWithData      : 1
ProbePacketTimeout : 600000
MaxAppendReqs     : 5
MaxReadReqs       : 5
SlowNodeTimeout   : 20000        # ms, 20s
MinRTO            : 100          # ms, for offline: 100, for online: 50
MaxRTO            : 16000        # ms, for reader
EnablePReadAhead  : 0            # 0: disable pread ahead, 1: enable pread ahead, use cache
SequentialReqBeforePReadAhead: 2 # number of sequential pread requests before converting it into pread ahead
ReadLocal          : 1

RecoverySpeed      : 1 # MB/s
RecoveryRetry      : 3

CompressThreads    : 2 # must update ClientThreads
DataThreads        : 4 # must update ClientThreads
MessageThreads     : 4 # must update ClientThreads

ReadPacketSize     : 128 # KB
#WriteSpeed        : 0   # KB  not support reload
#ReadSpeed         : 0   # KB  not support reload

#MaxSealCount      : 3
#SealCountPeriod   : 10  # minute, 10min

[.Metric]
EnableSflMetric            : 1
EnableHttpMetricCollector  : 0
CollectPeriodSec           : 60
DefaultLabels              : src=native
EnableAssert               : 0
HttpCollectPort            : 21274
CollectPortBegin           : 21274
CollectPortEnd             : 29999
RandomCollectPort          : 1
ReportBufSizeKb            : 1024

[.ESP]
Port                : 0
PortType            : 1 # 0:Real TCP prot, listening port as server, 1:Fake port, single-way connection
TranBufNum          : 131072
TranBufSize         : 8192      # Bytes
BigTranBufNum       : 1
LowMark             : 0.85
HighMark            : 0.95
# Default order: eth1, eth0, xgbe0, xgbe1
#Interface           : eth1
#MaxFlowMB           : 1000
# ip tos conf, default 0 disable tos
#   IPTOS_LOWCOST (0x02)
#   IPTOS_RELIABILITY (0x04)
#   IPTOS_THROUGHPUT (0x08)
#   IPTOS_LOWDELAY (0x10)
#IPTOS              : 0

NetThreads          : 4

[.Kylin]
LogLevel            : 0
LogPath             : ./log/run.log

[.Comlog]
level               : 8
procname            : afs_client
selfdefine          : AFS_FATAL,AFS_WARNING,AFS_NOTICE,AFS_TRACE,AFS_DEBUG
[..@device]
type                : AFILE
path                : log
split_type          : 1
log_size            : 2048
file                : afs_client.log
open                : 1
layout              : %L: %A: %P * %T %R
syslevel            : 0
selflevel           : DEBUG,TRACE,NOTICE,AFS_NOTICE,AFS_TRACE,AFS_DEBUG
[..@device]
type                : AFILE
path                : log
split_type          : 1
log_size            : 2048
file                : afs_client.log.wf
open                : 1
layout              : %L: %A: %P * %T %R
syslevel            : 0
selflevel           : WARNING,FATAL,AFS_FATAL,AFS_WARNING

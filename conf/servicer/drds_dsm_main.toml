# Service的名字，必选
Name = "drds_dsm_main"

# 注意：
# 由于目前 MySQL 服务提供的 BNS 里并没有配置 超时时间等 config
# 所以，使用的 BNS 时也需要配置下列几项超时时间(若有 config 则可以不配置)

# 连接超时，ms，若不配置，使用默认值 5s
ConnTimeOut = 500

# 写数据超时，ms，若不配置，使用默认值 5s
WriteTimeOut = 500

# 读数据超时，ms，若不配置，使用默认值 5s
ReadTimeOut = 500

# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin: 依次轮询
# Random 随机
[Strategy]
Name = "Random"

# 资源定位配置
[Resource.Manual]
[[Resource.Manual.default]]
# Host = "*************"
# Port = 15004
Host = "localhost"
Port = 8036

# MySQL 特有配置部分 (必须有)
[MySQL]
# Username = "mcponlineserver"
# Password = "NefQqQJ:COUZ"
# DBName = "mcp_online_server_dev"
# DBDriver = "mysql"

Username     = "root"
Password     = "dataeng_test"
DBName       = "mcp_online_server"
DBDriver     = "mysql"

# MaxOpenPerIP 每个 ip 最多连接数，若过小，会出现查询排队等待的情况
# 若为0-则不限制
MaxOpenPerIP = 10

# MaxIdlePerIP 每个 ip 最多连接空闲数，最大可以设置成 和 MaxOpenPerIP 一样
# 若为0-使用 sql 标准库的默认值2
MaxIdlePerIP = 1

# ConnMaxLifeTime 连接复用最长时间，单位 ms
# 若为0-则不限制，连接不会关闭
# 可以根据线上实际情况，设置一个比较大的合适的时间，以减少创建新连接带来的不稳定性
ConnMaxLifeTime = 300000 # 单位 ms

SQLLogLen = -1        # 打印sql内容，为0不打印，-1 为全部
SQLArgsLogLen = -1    # 打印sql参数内容，为0不打印，-1 为全部
LogIDTransport = true # 是否sql注释传递logid

# DSNParams 可选参数，请按需配置，请参考 https://github.com/go-sql-driver/mysql
# 若是 DDBS(DRDS) 或者 数据库不支持 prepare，需配置上 interpolateParams=true （详见下列6.2）
DSNParams = "charset=utf8mb4&timeout=90s&collation=utf8mb4_unicode_ci&parseTime=true&loc=Asia%2FShanghai"
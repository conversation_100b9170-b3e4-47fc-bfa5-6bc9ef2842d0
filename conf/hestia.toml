# 保持子进程一直存在
Keep = true

# grace shutdown 的超时时间，毫秒
StopTimeout = 10000

# 状态文件目录
StatusDir = "./var/"

# Workers 为子进程 / 工作 worker 的配置，可以配置多组，最少一组
[Workers.default]

# 分组名称
Name = "default"

# 可选，执行应用程序的根目录
# Cmd、Watches,VersionFile 对应的文件路径都是相对于此目录的
HomeDir="../"

# 子进程启动的命令
Cmd = "./bin/start.sh"

# 当前进程监听的端口，可以是 >=0 个
# 格式为: network@ip:port
# network 可以是：tcp、tcp4、tcp6
# https://pkg.go.dev/net#Listen
# 若期望只监听 tcp4 地址，应将 network 配置为 tcp4 而不是 tcp
Listen = [
    "tcp4@0.0.0.0:{env.LISTEN_PORT|NoPort}"
]

# VersionFile 检查程序进程版本信息的文件，可选
# 若有值，该文件发生变化将触发进程的 reload
# 若无值，通过检查 Cmd 是否发生变化，来判断是否需要 reload Cmd 进程
# VersionFile = "./var/app_version.txt"

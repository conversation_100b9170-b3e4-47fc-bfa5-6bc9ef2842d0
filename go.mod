module icode.baidu.com/baidu/dataeng/mcp-online-server

go 1.23.0

require (
	github.com/stretchr/testify v1.10.0
	icode.baidu.com/baidu/dataeng/data-gdp-library v0.0.4
	icode.baidu.com/baidu/gdp/automaxprocs v1.1.1
	icode.baidu.com/baidu/gdp/codec v1.26.0
	icode.baidu.com/baidu/gdp/env v1.23.0
	icode.baidu.com/baidu/gdp/extension v1.30.0
	icode.baidu.com/baidu/gdp/ghttp v1.32.6
	icode.baidu.com/baidu/gdp/hestia v0.4.1
	icode.baidu.com/baidu/gdp/logit v1.28.1
	icode.baidu.com/baidu/gdp/metrics v0.11.3
	icode.baidu.com/baidu/gdp/mysql v1.28.0
	icode.baidu.com/baidu/gdp/net v1.36.3
	icode.baidu.com/baidu/gdp/paas v0.2.1-0.20240701025705-2db8acda844f
	icode.baidu.com/baidu/gdp/panel v0.9.0
	icode.baidu.com/baidu/health/kylin/v2 v2.1.6
	icode.baidu.com/baidu/smartprogram/rcc2-go-sdk v1.2.4
)

require (
	github.com/BurntSushi/toml v1.4.0
	github.com/agiledragon/gomonkey/v2 v2.13.0
	github.com/baidubce/bce-sdk-go v0.9.233
	github.com/bytedance/sonic v1.13.3
	github.com/charmbracelet/log v0.4.2
	github.com/google/uuid v1.6.0
	github.com/mark3labs/mcp-go v0.32.0
	github.com/prometheus/client_golang v1.19.1
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/gorm v1.30.0
	icode.baidu.com/baidu/gdp/conf v1.24.0
	icode.baidu.com/baidu/gdp/gorm_adapter v1.21.2
	modernc.org/sqlite v1.38.0
)

require (
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/fsnotify/fsnotify v1.5.4 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/klauspost/cpuid/v2 v2.0.9 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/ncruces/go-strftime v0.1.9 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	golang.org/x/arch v0.11.0 // indirect
	golang.org/x/exp v0.0.0-20250408133849-7e4ce0ab07d0 // indirect
	golang.org/x/image v0.9.0 // indirect
	icode.baidu.com/baidu/gdp/es v0.4.1 // indirect
	icode.baidu.com/baidu/gdp/mcp v0.0.14 // indirect
	modernc.org/libc v1.65.10 // indirect
	modernc.org/mathutil v1.7.1 // indirect
	modernc.org/memory v1.11.0 // indirect
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.2 // indirect
	github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a // indirect
	github.com/alicebob/miniredis/v2 v2.33.0 // indirect
	github.com/andybalholm/brotli v1.0.6 // indirect
	github.com/arl/statsviz v0.6.0 // indirect
	github.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/charmbracelet/colorprofile v0.2.3-0.************15-f60798e515dc // indirect
	github.com/charmbracelet/lipgloss v1.1.0 // indirect
	github.com/charmbracelet/x/ansi v0.8.0 // indirect
	github.com/charmbracelet/x/cellbuf v0.0.13-0.20250311204145-2c3ea96c31dd // indirect
	github.com/charmbracelet/x/term v0.2.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/didi/gendry v1.8.2 // indirect
	github.com/envoyproxy/protoc-gen-validate v1.0.4 // indirect
	github.com/go-logfmt/logfmt v0.6.0 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.23.0 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gorilla/websocket v1.5.0 // indirect
	github.com/houseme/mobiledetect v1.2.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.16.6 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lucasb-eyer/go-colorful v1.2.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/muesli/termenv v0.16.0 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/openzipkin/zipkin-go v0.4.2 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.55.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/shirou/gopsutil/v3 v3.24.5 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect
	github.com/xuri/efp v0.0.0-20220603152613-6918739fd470 // indirect
	github.com/xuri/excelize/v2 v2.7.1 // indirect
	github.com/xuri/nfp v0.0.0-20220409054826-5e722a1d9e22 // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.opentelemetry.io/contrib/propagators/b3 v1.25.0 // indirect
	go.opentelemetry.io/contrib/propagators/jaeger v1.25.0 // indirect
	go.opentelemetry.io/contrib/propagators/ot v1.25.0 // indirect
	go.opentelemetry.io/contrib/samplers/jaegerremote v0.19.0 // indirect
	go.opentelemetry.io/otel v1.25.0 // indirect
	go.opentelemetry.io/otel/exporters/jaeger v1.17.0 // indirect
	go.opentelemetry.io/otel/exporters/stdout/stdouttrace v1.25.0 // indirect
	go.opentelemetry.io/otel/exporters/zipkin v1.25.0 // indirect
	go.opentelemetry.io/otel/metric v1.25.0 // indirect
	go.opentelemetry.io/otel/sdk v1.25.0 // indirect
	go.opentelemetry.io/otel/trace v1.25.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/crypto v0.25.0 // indirect
	golang.org/x/mod v0.24.0 // indirect
	golang.org/x/net v0.27.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.20.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	google.golang.org/genproto v0.0.0-20240123012728-ef4313101c80 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240123012728-ef4313101c80 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240123012728-ef4313101c80 // indirect
	google.golang.org/grpc v1.62.1 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
	gorm.io/driver/mysql v1.4.4 // indirect
	icode.baidu.com/baidu/gdp/bns v1.29.0 // indirect
	icode.baidu.com/baidu/gdp/crypto v1.2.0 // indirect
	icode.baidu.com/baidu/gdp/excache v1.0.4 // indirect
	icode.baidu.com/baidu/gdp/exjson v1.3.0 // indirect
	icode.baidu.com/baidu/gdp/mcpack v1.26.0 // indirect
	icode.baidu.com/baidu/gdp/nshead v1.26.0 // indirect
	icode.baidu.com/baidu/gdp/passport v1.22.0 // indirect
	icode.baidu.com/baidu/gdp/pbrpc v1.33.7 // indirect
	icode.baidu.com/baidu/gdp/redis v1.32.0 // indirect
	icode.baidu.com/baidu/gdp/xds v0.5.0 // indirect
	icode.baidu.com/baidu/mapx/maptelemetry v0.0.5 // indirect
	icode.baidu.com/baidu/ps-se-go/exgraph v0.3.9 // indirect
	icode.baidu.com/baidu/third-party/go-control-plane v0.8.7-0.20220531025328-39658c5e033e // indirect
	istio.io/gogo-genproto v0.0.0-20190731221249-06e20ada0df2 // indirect
)

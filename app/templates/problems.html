<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Problem Agent Success Matrix</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th,
        td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .failure {
            background-color: #f8d7da;
            color: #721c24;
        }

        .na {
            background-color: #e2e3e5;
            color: #6c757d;
        }

        /* Optional: style the links */
        a {
            text-decoration: none;
            color: inherit;
            margin: 0 4px;
        }
    </style>
</head>

<body>
    <h1>Problem Agent Success Matrix</h1>

    {# Build a unique (and sorted) list of agent names from all problems #}
    {% set agents = [] %}
    {% for problem, results in problems.items() %}
    {% for agent_name in results.keys() %}
    {% if agent_name not in agents %}
    {% set _ = agents.append(agent_name) %}
    {% endif %}
    {% endfor %}
    {% endfor %}
    {% set agents = agents|sort %}

    <table>
        <thead>
            <tr>
                <th>Problem (Docker Image)</th>
                {% for agent in agents %}
                <th>{{ agent }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for problem, results in problems.items() %}
            <tr>
                <td>{{ problem.split('.')[-1] }} </td>
                {% for agent in agents %}
                {% if agent in results %}
                {% set data = results[agent] %}
                <td class="{% if data.success %}success{% else %}failure{% endif %}">
                    <!-- Display text indicator of status -->
                    {% if data.trajectory_url %}

                    <a href="{{ data.trajectory_url }}">
                        {% if data.success %}
                        Success
                        {% else %}
                        Failure
                        {% endif %}
                    </a>
                    {% else %}
                    {% if data.success %}
                    Success
                    {% else %}
                    Failure
                    {% endif %}
                    {% endif %}
                </td>
                {% else %}
                <td class="na">N/A</td>
                {% endif %}
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>

</html>
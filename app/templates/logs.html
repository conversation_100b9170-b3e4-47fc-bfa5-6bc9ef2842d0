{% extends "base.html" %}
{% block content %}
<h2 class="mb-4">Logs in {{ filename }}</h2>
{% for docker_image, logs in grouped_logs.items() %}
<div class="card mb-3">
    <div class="card-header bg-secondary text-white">
        Docker Image: {{ docker_image }}
    </div>
    <ul class="list-group list-group-flush">
        {% for log in logs %}
        <li class="list-group-item d-flex justify-content-between align-items-center">
            <div>
                <strong>Docker Image:</strong> {{ log.ds.docker_image }}<br>
                <strong>Log ID:</strong> {{ log._id }}<br>
                <strong>Success:</strong> {{ log.reward == 1 }}<br>
            </div>
            <a href="{{ url_for('trajectory', filename=filename, log_id=log._id) }}" class="btn btn-info btn-sm">
                <i class="fas fa-eye"></i> View Trajectory
            </a>
        </li>
        {% endfor %}
    </ul>
</div>
{% endfor %}
{% endblock %}
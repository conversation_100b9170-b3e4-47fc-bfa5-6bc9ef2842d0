<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>{{ title if title else "Log Viewer" }}</title>
    <!-- Bootstrap CSS via CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Highlight.js GitHub Theme CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <!-- Font Awesome CSS via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 60px;
        }

        .container {
            max-width: 960px;
        }

        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            /* Allow wrapping */
            word-wrap: break-word;
            /* Break long words */
            overflow-x: auto;
        }

        .thought-bg {
            background-color: #e8f5e9;
            /* Light green */
            padding: 10px;
            border-radius: 5px;
        }

        .action-bg {
            background-color: #e3f2fd;
            /* Light blue */
            padding: 10px;
            border-radius: 5px;
        }

        .observation-bg {
            background-color: #fff3e0;
            /* Light orange */
            padding: 10px;
            border-radius: 5px;
        }

        /* Markdown Content Styling */
        .markdown-content {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        /* Pagination Centering */
        .pagination {
            justify-content: center;
        }


        .passed {
            background-color: #e6ffe6 !important;
            /* light green */
            color: black !important;
        }

        .failed {
            background-color: #ffe6e6 !important;
            /* light red */
            color: black !important;
        }
    </style>
</head>

<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">Log Viewer</a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        {% for message in messages %}
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS via CDN (requires Popper.js) -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.7/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
    <!-- Highlight.js JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script>hljs.highlightAll();</script>
</body>

</html>
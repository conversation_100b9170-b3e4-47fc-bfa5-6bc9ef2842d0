{% extends "base.html" %}
{% block content %}
<!-- Navigation Buttons -->
<nav aria-label="Trajectory Navigation" class="mb-4">
    <ul class="pagination justify-content-center">
        <!-- Previous Log Button -->
        <li class="page-item {% if current_log_id == 0 %}disabled{% endif %}">
            <a class="page-link"
                href="{% if current_log_id > 0 %}{{ url_for('trajectory', filename=filename, log_id=current_log_id - 1) }}{% else %}#{% endif %}"
                tabindex="-1">
                <i class="fas fa-arrow-left"></i> Previous
            </a>
        </li>

        <!-- Current Log Indicator -->
        <li class="page-item disabled">
            <span class="page-link">
                Log {{ current_log_id + 1 }} of {{ total_logs }}
            </span>
        </li>

        <!-- Next Log Button -->
        <li class="page-item {% if current_log_id + 1 >= total_logs %}disabled{% endif %}">
            <a class="page-link"
                href="{% if current_log_id + 1 < total_logs %}{{ url_for('trajectory', filename=filename, log_id=current_log_id + 1) }}{% else %}#{% endif %}">
                Next <i class="fas fa-arrow-right"></i>
            </a>
        </li>
    </ul>
</nav>

<!-- Back to Logs Button -->
<div class="mb-4">
    <a href="{{ url_for('logs', filename=filename) }}" class="btn btn-secondary">
        <i class="fas fa-list"></i> Back to Logs
    </a>
</div>

<h2 class="mb-4">Trajectory for Log ID: {{ log._id }}</h2>

<!-- Display Log Metadata -->
<div class="mb-4">
    <h4>Metadata</h4>
    <p><strong>Experiment Name:</strong> {{ log.exp_name }}</p>
    <p><strong>Docker Image / Git URL:</strong> <a href="{{ url }}"> {{ log.docker_image }} </a>
    <p>
        <strong>Success: </strong> {{ log.reward == 1 }}
    </p>

    <!-- Display llm_name and command_files Outside AgentArgs Toggle -->
    <p><strong>LLM Name:</strong> {{ log.agent_args.llm_name }}</p>

    <!-- Agent Arguments Collapsible -->
    <p>
        <strong>Agent Arguments:</strong>
        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse"
            data-bs-target="#agentArgsCollapse" aria-expanded="false" aria-controls="agentArgsCollapse">
            Toggle
        </button>
    </p>
    <div class="collapse" id="agentArgsCollapse">
        <div class="card card-body">
            <ul class="list-group list-group-flush">
                {% for key, value in log.agent_args.items() %}
                {% if key == 'llm_name' or key == 'command_files' %}
                {# Already displayed outside the toggle #}
                {% endif %}
                {% if key != 'llm_name' and key != 'command_files' %}
                {% if key == 'command_files' and value %}
                <li class="list-group-item"><strong>{{ key | replace('_', ' ') | capitalize }}:</strong>
                    <ul>
                        {% for file in value %}
                        <li>{{ file }}</li>
                        {% endfor %}
                    </ul>
                </li>
                {% else %}
                <li class="list-group-item"><strong>{{ key | replace('_', ' ') | capitalize }}:</strong> {{ value }}
                </li>
                {% endif %}
                {% endif %}
                {% endfor %}
            </ul>

            <p><strong>Command Files:</strong></p>
            <ul class="list-group mb-3">
                {% for file in log.agent_args.command_files %}
                <li class="list-group-item">{{ file }}</li>
                {% endfor %}
            </ul>


        </div>
    </div>


    <p><strong>Ground Truth Files:</strong></p>
    <ul class="list-group mb-3">
        {% for file in log.ds.gt_files %}
        <li class="list-group-item">{{ file }}</li>
        {% endfor %}
    </ul>

    <!-- Predicted Files -->
    <p><strong>Predicted Files:</strong></p>
    <ul class="list-group mb-3">
        {% for file in log.ds.pred_files %}
        <li class="list-group-item">{{ file }}</li>
        {% endfor %}
    </ul>

    <!-- Ground Truth Non-Test Files -->
    <p><strong>Ground Truth Non-Test Files:</strong></p>
    <ul class="list-group mb-3">
        {% for file in log.ds.gt_nontest_files %}
        <li class="list-group-item">{{ file }}</li>
        {% endfor %}
    </ul>

    <p>
        <strong>Problem Statement</strong>
    <pre class="{{ 'passed' if log.reward == 1 else 'failed' }}">
{{ log.ds.problem_statement }}
        </pre>
    </p>



    <!-- Render System Prompt as Markdown -->
    {% if log.system_prompt_html %}
    <div class=" mb-3">
        <h5>System Prompt:</h5>
        <div class="markdown-content">
            {{ log.system_prompt_html | safe }}
        </div>
    </div>
    {% endif %}

    <!-- Render Instance Prompt as Markdown -->
    {% if log.instance_prompt_html %}
    <div class="mb-3">
        <h5>Instance Prompt:</h5>
        <div class="markdown-content">
            {{ log.instance_prompt_html | safe }}
        </div>
    </div>
    {% endif %}
</div>

<!-- Final Patch -->
<p>
    <strong>Final Patch:</strong>
    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse"
        data-bs-target="#finaloutputCollapse" aria-expanded="false" aria-controls="finaloutputCollapse">
        Toggle
    </button>
</p>
<div class="collapse" id="finaloutputCollapse">
    <div class="card card-body">
        <div class="mt-4">
            <h4>Final Patch</h4>
            <pre><code class="python">{{ true_output_patch }}</code></pre>
        </div>
    </div>
</div>

<p>
    <strong>GT Patch:</strong>
    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse"
        data-bs-target="#gtpatchCollapse" aria-expanded="false" aria-controls="gtpatchCollapse">
        Toggle
    </button>
</p>
<div class="collapse" id="gtpatchCollapse">
    <div class="card card-body">
        <div class="mt-4">
            <h4>GT Patch</h4>
            <pre><code class="python">{{ gt_patch }}</code></pre>
        </div>
    </div>
</div>

<!-- Test Exec -->
<!-- <pre><code class="python">{{ log.test_output }}</code></pre> -->
<p>
    <strong>Test Exec:</strong>
    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse"
        data-bs-target="#testexecCollapse" aria-expanded="false" aria-controls="testexecCollapse">
        Toggle
    </button>
</p>
<div class="collapse" id="testexecCollapse">
    <div class="card card-body">
        <div class="mt-4">
            <h4>Test Exec</h4>
            <pre><code class="python">{{ log.report }}</code></pre>
        </div>
    </div>
</div>

<!-- Test Output -->
<p>
    <strong>Test Output:</strong>
    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse"
        data-bs-target="#testoutputCollapse" aria-expanded="false" aria-controls="testoutputCollapse">
        Toggle
    </button>
</p>
<div class="collapse" id="testoutputCollapse">
    <div class="card card-body">
        <div class="mt-4">
            <h4>Test Output</h4>
            <pre><code class="python">{{ log.test_output }}</code></pre>
        </div>
    </div>
</div>

<!-- Display Trajectory -->
<div>
    <h4>Trajectory ({{ total_steps }} steps)</h4>
    {% if trajectory %}
    {% for step in trajectory %}
    <div class="card mb-3">
        <div class="card-header" id="heading{{ loop.index }}">
            <h5 class="mb-0">
                <button class="btn btn-link" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapse{{ loop.index }}" aria-expanded="true"
                    aria-controls="collapse{{ loop.index }}">
                    Step {{ loop.index }}
                </button>
            </h5>
        </div>
        <div id="collapse{{ loop.index }}" class="collapse show" aria-labelledby="heading{{ loop.index }}">
            <div class="card-body">
                <div class="thought-bg mb-3">
                    <p><strong>Thought:</strong></p>
                    <pre><code class="python">{{ step.thought }}</code></pre>
                </div>
                <div class="action-bg mb-3">
                    <p><strong>Action:</strong></p>
                    <pre><code class="python">{{ step.action }}</code></pre>
                </div>
                <div class="observation-bg mb-3">
                    <p><strong>Observation:</strong></p>
                    <pre><code class="python">{{ step.observation }}</code></pre>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <p>No trajectory data available for this log.</p>
    {% endif %}
</div>



<!-- Back to Logs Button -->
<div class="mt-4">
    <a href="{{ url_for('logs', filename=filename) }}" class="btn btn-secondary">
        <i class="fas fa-list"></i> Back to Logs
    </a>
</div>
{% endblock %}
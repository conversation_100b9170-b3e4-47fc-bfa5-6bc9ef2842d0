// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package main

import (
	"context"
	"flag"
	"log"

	_ "icode.baidu.com/baidu/gdp/automaxprocs"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
)

// 应该的主配置文件，可以通过修改此参数来切换运行环境
// 比如可以有多套环境 conf(开发环境配置)、conf_qa (测试环境配置)、conf_online（线上环境配置）
// 在编译的时候，会将 conf_online 打包为 conf，其他的配置不会打包
var appConfig = flag.String("conf", "./conf/app.toml", "app config file")

func main() {
	flag.Parse()

	config := bootstrap.MustLoadAppConfig(*appConfig)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bootstrap.MustInit(ctx)

	log.Println("server exit:", bootstrap.StartServers(ctx, config))
}

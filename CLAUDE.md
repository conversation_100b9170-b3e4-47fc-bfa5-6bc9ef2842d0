# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Go-based microservice built on the GDP (Go Development Platform) framework. The service manages MCP (Model Compute Platform) sessions, environments, and tool executions. It provides HTTP APIs for session lifecycle management, environment initialization, and tool calling functionality.

## Common Commands

### Building
```bash
# Build the application
make compile

# Build for Linux (Docker deployment)
make go-build-linux

# Full build and package
make all
```

### Running
```bash
# Run with default configuration
go run main.go

# Run with specific configuration
go run main.go -conf conf/app.toml
```

### Testing
```bash
# Run all tests with coverage
make test

# Run tests manually
go test -race -v -cover ./...
```

### Docker Build
```bash
# Build Docker image for MCP runtime
make docker-build

# Push Docker image
make docker-push
```

### Stress Testing
```bash
# Run small load test
make stress-test-small

# Run full initialization test
make stress-test-full
```

## Code Architecture

### High-Level Structure
- `main.go` - Application entry point
- `bootstrap/` - Application initialization and component setup
- `httpserver/` - HTTP server implementation with controllers and routing
- `model/` - Business logic and data models
- `library/` - Shared utilities and resources
- `conf/` - Configuration files (TOML format)
- `cmd/` - Command-line tools and binaries

### Key Components
1. **Session Management** - Handles MCP session lifecycle (init, stop, status)
2. **Environment Initialization** - Sets up execution environments for MCP tools
3. **Tool Execution** - Manages tool calling and execution
4. **Background Services** - Task polling and session monitoring services
5. **Metrics & Monitoring** - Built-in metrics collection and reporting

### HTTP API Endpoints
- `/api/v1/mcp/session/*` - Session management APIs
- `/api/v1/mcp/tool/*` - Tool execution APIs
- `/api/v1/mcp/env/*` - Environment management APIs
- `/api/v1/mcp/image/*` - Image management APIs
- `/api/v1/mcp/internal/*` - Internal APIs

### Configuration
- Uses TOML configuration files in `conf/` directory
- Supports multiple environments (development, test, production)
- Configuration files organized by component (servicer, logit, business, etc.)

### Data Access
- Uses MySQL for persistent storage
- DAO (Data Access Object) pattern in `model/dao/`
- GDP MySQL client with connection pooling

### Dependencies
- GDP framework (Baidu's internal Go development platform)
- MySQL for data storage
- BOS (Baidu Object Storage) for file storage
- BRCC (Baidu Remote Configuration Center) for dynamic configuration
### model
model_name_or_path: Qwen/Qwen2.5-Coder-32B-instruct
trust_remote_code: true

### method
stage: sft
do_train: true
finetuning_type: full
deepspeed: examples/deepspeed/ds_z3_offload_config.json
# deepspeed: examples/deepspeed/ds_z3_config.json  # choices: [ds_z0_config.json, ds_z2_config.json, ds_z3_config.json]

### dataset
dataset: R2E-Gym/R2EGym-SFT-Trajectories
template: qwen
cutoff_len: 20480
max_samples: 100000
overwrite_cache: true
preprocessing_num_workers: 16

### output
output_dir: saves/R2EGym-32B-Agent
logging_steps: 10
save_steps: 10000
plot_loss: true
overwrite_output_dir: true

### train
flash_attn: fa2
enable_liger_kernel: true
use_unsloth_gc: true
per_device_train_batch_size: 1
gradient_accumulation_steps: 1
learning_rate: 1.0e-5
num_train_epochs: 2.0
lr_scheduler_type: cosine
warmup_ratio: 0.05
bf16: true
ddp_timeout: 180000000

### wandb
report_to: wandb
run_name: R2<PERSON>ym-32B-Agent
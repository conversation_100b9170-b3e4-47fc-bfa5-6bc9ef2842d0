package main

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
)

// TestMCPRuntimeManager_UseServerPrefix 测试工具命名前缀功能
func TestMCPRuntimeManager_UseServerPrefix(t *testing.T) {
	tests := []struct {
		name            string
		useServerPrefix bool
		expectedPrefix  bool
	}{
		{
			name:            "使用服务器前缀",
			useServerPrefix: true,
			expectedPrefix:  true,
		},
		{
			name:            "不使用服务器前缀",
			useServerPrefix: false,
			expectedPrefix:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 MCPRuntimeManager
			manager := NewMCPRuntimeManager(tt.useServerPrefix)

			// 验证配置是否正确设置
			if manager.useServerPrefix != tt.useServerPrefix {
				t.Errorf("useServerPrefix = %v, want %v", manager.useServerPrefix, tt.useServerPrefix)
			}

			// 验证缓存映射是否初始化
			if manager.toolToClientMap == nil {
				t.Error("toolToClientMap should be initialized")
			}

			// 验证缓存状态
			if manager.cacheInitialized {
				t.Error("cacheInitialized should be false initially")
			}
		})
	}
}

// TestMCPRuntimeManager_CacheInitialization 测试缓存初始化
func TestMCPRuntimeManager_CacheInitialization(t *testing.T) {
	manager := NewMCPRuntimeManager(false)

	// 模拟设置一些缓存数据
	manager.toolsCache = []types.MCPTool{
		{
			Name:        "test_tool",
			Description: "Test tool",
			Parameters:  make(map[string]interface{}),
		},
	}
	manager.toolToClientMap["test_tool"] = "test_server"
	manager.cacheInitialized = true

	// 测试缓存是否正常工作
	ctx := context.Background()
	tools, err := manager.GetAvailableTools(ctx)
	if err != nil {
		t.Fatalf("GetAvailableTools() error = %v", err)
	}

	if len(tools) != 1 {
		t.Errorf("Expected 1 tool, got %d", len(tools))
	}

	if tools[0].Name != "test_tool" {
		t.Errorf("Expected tool name 'test_tool', got '%s'", tools[0].Name)
	}

	// 验证映射是否正确
	if serverName, exists := manager.toolToClientMap["test_tool"]; !exists || serverName != "test_server" {
		t.Errorf("Expected mapping test_tool -> test_server, got %s (exists: %v)", serverName, exists)
	}
}

// TestParseFlags 测试命令行参数解析
func TestParseFlags(t *testing.T) {
	// 注意：这个测试需要小心，因为 flag.Parse() 会影响全局状态
	// 在实际项目中，可能需要使用更复杂的测试方法来隔离 flag 解析

	// 这里只是验证 RuntimeConfig 结构体包含了新字段
	config := &RuntimeConfig{}

	// 验证字段存在
	config.UseServerPrefix = true
	if !config.UseServerPrefix {
		t.Error("UseServerPrefix field should be settable")
	}

	config.UseServerPrefix = false
	if config.UseServerPrefix {
		t.Error("UseServerPrefix field should be settable to false")
	}
}

// TestDownloadServerCode 测试下载server代码功能
func TestDownloadServerCode(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "test_server_download_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建MCPRuntimeManager实例
	_ = NewMCPRuntimeManager(false)

	t.Run("测试文件格式检测", func(t *testing.T) {
		testCases := []struct {
			name        string
			url         string
			expectedExt string
		}{
			{"ZIP文件", "https://example.com/server.zip", ".zip"},
			{"TAR.GZ文件", "https://example.com/server.tar.gz", ".tar.gz"},
			{"TGZ文件", "https://example.com/server.tgz", ".tgz"},
			{"未知格式", "https://example.com/server", ".zip"}, // 默认为zip
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// 测试URL格式检测逻辑
				url := tc.url
				var detectedFormat string

				if strings.HasSuffix(strings.ToLower(url), ".zip") {
					detectedFormat = "zip"
				} else if strings.HasSuffix(strings.ToLower(url), ".tar.gz") ||
					strings.HasSuffix(strings.ToLower(url), ".tgz") {
					detectedFormat = "tar.gz"
				} else {
					detectedFormat = "zip" // 默认
				}

				// 验证格式检测逻辑
				if tc.expectedExt == ".zip" {
					assert.Equal(t, "zip", detectedFormat)
				} else if tc.expectedExt == ".tar.gz" || tc.expectedExt == ".tgz" {
					assert.Equal(t, "tar.gz", detectedFormat)
				}
			})
		}
	})

	t.Run("测试目录路径构建", func(t *testing.T) {
		serverName := "test_server"
		expectedPath := filepath.Join("/home/<USER>/servers", serverName)

		// 验证路径构建逻辑
		actualPath := filepath.Join("/home/<USER>/servers", serverName)
		assert.Equal(t, expectedPath, actualPath)
	})
}

// TestLoadMCPServersWithServerCode 测试加载MCP服务器时的代码下载
func TestLoadMCPServersWithServerCode(t *testing.T) {
	// 创建MCPRuntimeManager实例
	_ = NewMCPRuntimeManager(false)

	// 创建测试server数据
	servers := []*dao_register_mcp_server.ObjRegisterMcpServer{
		{
			ServerName:       "test_server_1",
			ServerCodeBosUrl: "", // 空URL，应该跳过下载
			Command:          "node",
			Args:             []string{"server.js"},
		},
		{
			ServerName:       "test_server_2",
			ServerCodeBosUrl: "https://example.com/server.zip", // 有URL
			Command:          "python",
			Args:             []string{"-m", "server"},
		},
	}

	// 测试下载逻辑的调用
	for _, server := range servers {
		if server.ServerCodeBosUrl != "" {
			// 验证server有正确的字段
			assert.NotEmpty(t, server.ServerName)
			assert.NotEmpty(t, server.ServerCodeBosUrl)
		}
	}
}

// TestExtractFileNameFromURL 测试从URL提取文件名
func TestExtractFileNameFromURL(t *testing.T) {
	testCases := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "简单文件名",
			url:      "https://example.com/server.py",
			expected: "server.py",
		},
		{
			name:     "压缩文件",
			url:      "https://example.com/path/to/server.zip",
			expected: "server.zip",
		},
		{
			name:     "带查询参数的URL",
			url:      "https://example.com/server.tar.gz?version=1.0&token=abc123",
			expected: "server.tar.gz",
		},
		{
			name:     "带片段的URL",
			url:      "https://example.com/server.sh#section1",
			expected: "server.sh",
		},
		{
			name:     "复杂URL",
			url:      "https://example.com/path/to/my-server-v1.2.3.tar.gz?download=true&format=compressed#readme",
			expected: "my-server-v1.2.3.tar.gz",
		},
		{
			name:     "无文件名的URL",
			url:      "https://example.com/path/to/",
			expected: "",
		},
		{
			name:     "根路径",
			url:      "https://example.com/",
			expected: "",
		},
		{
			name:     "无扩展名文件",
			url:      "https://example.com/server",
			expected: "server",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := utils.ExtractFileNameFromURL(tc.url)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsCompressedFile 测试压缩文件检测
func TestIsCompressedFile(t *testing.T) {
	testCases := []struct {
		name       string
		fileName   string
		compressed bool
	}{
		{
			name:       "ZIP文件",
			fileName:   "server.zip",
			compressed: true,
		},
		{
			name:       "TAR.GZ文件",
			fileName:   "server.tar.gz",
			compressed: true,
		},
		{
			name:       "TGZ文件",
			fileName:   "server.tgz",
			compressed: true,
		},
		{
			name:       "TAR文件",
			fileName:   "server.tar",
			compressed: true,
		},
		{
			name:       "GZ文件",
			fileName:   "server.gz",
			compressed: true,
		},
		{
			name:       "RAR文件",
			fileName:   "server.rar",
			compressed: true,
		},
		{
			name:       "7Z文件",
			fileName:   "server.7z",
			compressed: true,
		},
		{
			name:       "Python脚本",
			fileName:   "server.py",
			compressed: false,
		},
		{
			name:       "Shell脚本",
			fileName:   "server.sh",
			compressed: false,
		},
		{
			name:       "二进制文件",
			fileName:   "server",
			compressed: false,
		},
		{
			name:       "文本文件",
			fileName:   "server.txt",
			compressed: false,
		},
		{
			name:       "JavaScript文件",
			fileName:   "server.js",
			compressed: false,
		},
		{
			name:       "大写扩展名",
			fileName:   "SERVER.ZIP",
			compressed: true,
		},
		{
			name:       "混合大小写",
			fileName:   "Server.Tar.Gz",
			compressed: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := utils.IsCompressedFile(tc.fileName)
			assert.Equal(t, tc.compressed, result)
		})
	}
}

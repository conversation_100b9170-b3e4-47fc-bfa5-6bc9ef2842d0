package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/bytedance/sonic"
	_ "github.com/charmbracelet/log"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/apiclient"
	bosconfig "icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/mcphost"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	SessionID       int64
	MCPAPIBase      string
	WorkingDir      string
	PollInterval    time.Duration
	Timeout         time.Duration
	UseServerPrefix bool // 是否在工具名称中使用server_name前缀，默认true
	// BOS配置
	BosAccessKey string
	BosSecretKey string
	BosEndpoint  string
	BosBucket    string
}

// EnvironmentManager 环境管理器
type EnvironmentManager struct {
	workingDir string
	client     *apiclient.Client
	envMgr     *utils.EnvManager
}

// MCPRuntimeManager MCP运行时管理器
type MCPRuntimeManager struct {
	toolManager      *mcphost.MCPToolManager
	config           *mcphost.Config
	useServerPrefix  bool              // 是否使用server_name前缀
	toolsCache       []types.MCPTool   // 缓存的工具列表
	toolToClientMap  map[string]string // tool_name到server_name的映射
	cacheInitialized bool              // 缓存是否已初始化
}

// TaskPoller 任务轮询器
type TaskPoller struct {
	client       *apiclient.Client
	sessionID    int64
	pollInterval time.Duration
}

// ToolExecutor 工具执行器
type ToolExecutor struct {
	runtime *MCPRuntimeManager
	envMgr  *EnvironmentManager
	client  *apiclient.Client
}

// Main 主MCP运行时容器进程
type MCPRuntime struct {
	config       *RuntimeConfig
	apiClient    *apiclient.Client
	envManager   *EnvironmentManager
	mcpRuntime   *MCPRuntimeManager
	taskPoller   *TaskPoller
	toolExecutor *ToolExecutor
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewEnvironmentManager creates a new environment manager
func NewEnvironmentManager(workingDir string, client *apiclient.Client, bosConfig *bosconfig.BosConfig) *EnvironmentManager {
	// 创建BOS客户端
	bosClient, err := bos.NewClient(bosConfig.AccessKey, bosConfig.SecretKey, bosConfig.Endpoint)
	if err != nil {
		log.Printf("Failed to create BOS client: %v", err)
		return nil
	}

	// 创建环境管理器
	envMgr := utils.NewEnvManager(workingDir, bosClient, bosConfig)

	return &EnvironmentManager{
		workingDir: workingDir,
		client:     client,
		envMgr:     envMgr,
	}
}

// InitializeEnvironment initializes the environment from BOS
func (e *EnvironmentManager) InitializeEnvironment(ctx context.Context, env *dao_mcp_env.ObjMcpEnv) error {
	log.Printf("Initializing environment from BOS URL: %s", env.BosURL)

	// 使用新的环境管理器来恢复环境
	if err := e.envMgr.RestoreEnvironment(ctx, env.BosURL); err != nil {
		return fmt.Errorf("restore environment: %w", err)
	}

	log.Printf("Environment initialized successfully")
	return nil
}

// CreateSnapshot creates a snapshot of the current environment
func (e *EnvironmentManager) CreateSnapshot(ctx context.Context) (string, string, error) {
	// 使用新的环境管理器来创建快照
	md5Hash, bosURL, err := e.envMgr.CreateSnapshot(ctx)
	if err != nil {
		return "", "", fmt.Errorf("create environment snapshot: %w", err)
	}

	log.Printf("Environment snapshot created: md5=%s, bos_url=%s", md5Hash, bosURL)
	return md5Hash, bosURL, nil
}

// NewMCPRuntimeManager creates a new MCP runtime manager
func NewMCPRuntimeManager(useServerPrefix bool) *MCPRuntimeManager {
	return &MCPRuntimeManager{
		toolManager:      mcphost.NewMCPToolManager(),
		useServerPrefix:  useServerPrefix,
		toolToClientMap:  make(map[string]string),
		cacheInitialized: false,
	}
}

// LoadMCPServers 加载MCP服务器
func (m *MCPRuntimeManager) LoadMCPServers(ctx context.Context, servers []*dao_register_mcp_server.ObjRegisterMcpServer) error {
	mcpConfig := &mcphost.Config{
		MCPServers: make(map[string]mcphost.MCPServerConfig),
	}

	for _, server := range servers {
		if server.ServerCodeBosUrl != "" {
			// 下载server代码到指定目录
			if err := m.downloadServerCode(ctx, server); err != nil {
				log.Printf("下载server代码失败: server_name=%s, error=%v", server.ServerName, err)
				return fmt.Errorf("下载server代码失败 %s: %w", server.ServerName, err)
			}
		}
		serverConfig := mcphost.MCPServerConfig{
			Type:        string(server.Type),
			Command:     []string{server.Command},
			Args:        server.Args,
			URL:         server.Url,
			Environment: server.Env,
			Env:         server.Env,
			Headers:     server.Headers,
		}

		mcpConfig.MCPServers[server.ServerName] = serverConfig
	}

	// 首先使用 serviceManager 加载 MCP 服务
	if err := m.toolManager.LoadTools(ctx, mcpConfig); err != nil {
		return fmt.Errorf("load MCP tools with toolManager: %w", err)
	}

	m.config = mcpConfig
	return nil
}

// downloadServerCode 下载server代码到指定目录
func (m *MCPRuntimeManager) downloadServerCode(ctx context.Context, server *dao_register_mcp_server.ObjRegisterMcpServer) error {
	// 构建目标目录路径: /home/<USER>/servers/server_name/
	serverDir := filepath.Join("/home/<USER>/servers", server.ServerName)

	log.Printf("开始下载server代码: server_name=%s, bos_url=%s, target_dir=%s",
		server.ServerName, server.ServerCodeBosUrl, serverDir)

	// 创建目标目录
	if err := os.MkdirAll(serverDir, 0755); err != nil {
		return fmt.Errorf("创建server目录失败: %w", err)
	}

	// 检查目录是否已经存在内容，如果存在则跳过下载
	entries, err := os.ReadDir(serverDir)
	if err != nil {
		return fmt.Errorf("读取server目录失败: %w", err)
	}

	// 如果目录不为空，跳过下载
	if len(entries) > 0 {
		log.Printf("Server目录已存在内容，跳过下载: %s", serverDir)
		return nil
	}

	// 从URL中提取文件名，如果无法提取则使用默认名称
	fileName := utils.ExtractFileNameFromURL(server.ServerCodeBosUrl)
	if fileName == "" {
		fileName = "server_code"
	}

	// 构建目标文件路径
	targetFilePath := filepath.Join(serverDir, fileName)

	// 下载文件到目标位置
	if err := utils.DownloadFile(server.ServerCodeBosUrl, targetFilePath); err != nil {
		return fmt.Errorf("下载server代码文件失败: %w", err)
	}

	// 检查文件是否为压缩包，如果是则解压缩
	if utils.IsCompressedFile(fileName) {
		log.Printf("检测到压缩文件，开始解压: %s", fileName)

		var extractFunc func(string, string) error
		if strings.HasSuffix(strings.ToLower(fileName), ".zip") {
			extractFunc = utils.ExtractZip
		} else if strings.HasSuffix(strings.ToLower(fileName), ".tar.gz") ||
			strings.HasSuffix(strings.ToLower(fileName), ".tgz") {
			extractFunc = utils.ExtractTarGz
		} else {
			// 对于其他可能的压缩格式，尝试zip解压
			extractFunc = utils.ExtractZip
			log.Printf("未知压缩格式，尝试使用zip解压: %s", fileName)
		}

		// 解压文件
		if err := extractFunc(targetFilePath, serverDir); err != nil {
			log.Printf("解压失败，保留原文件: %v", err)
			// 解压失败时不返回错误，保留原文件
		} else {
			// 解压成功，删除压缩文件
			os.Remove(targetFilePath)
			log.Printf("✅Server代码下载解压完成: server_name=%s, target_dir=%s", server.ServerName, serverDir)
			return nil
		}
	}

	log.Printf("✅Server代码下载完成: server_name=%s, file=%s", server.ServerName, targetFilePath)
	return nil
}

// GetAvailableTools 获取可用的工具
func (m *MCPRuntimeManager) GetAvailableTools(ctx context.Context) ([]types.MCPTool, error) {
	// 如果缓存已初始化，直接返回缓存结果
	if m.cacheInitialized {
		return m.toolsCache, nil
	}

	var mcpTools []types.MCPTool

	toolMap, err := m.toolManager.GetAvailableTools(ctx)
	if err != nil {
		return nil, fmt.Errorf("get available tools: %w", err)
	}

	for serverName, tools := range toolMap {
		for _, tool := range tools {
			var toolName string

			// 建立工具名称到服务器名称的映射
			if m.useServerPrefix {
				toolName = serverName + "__" + tool.Name
				if existingServer, exists := m.toolToClientMap[tool.Name]; exists {
					log.Printf("⚠️Tool name conflict detected. Tool '%s' from server '%s' will override the same tool from server '%s'",
						toolName, serverName, existingServer)
				}
				m.toolToClientMap[tool.Name] = serverName
			} else {
				// 直接使用原始工具名称
				toolName = tool.Name
				// 检查工具名称冲突
				if existingServer, exists := m.toolToClientMap[toolName]; exists {
					log.Printf("⚠️Tool name conflict detected. Tool '%s' from server '%s' will override the same tool from server '%s'",
						toolName, serverName, existingServer)
				}
				// 建立工具名称到服务器名称的映射
				m.toolToClientMap[toolName] = serverName
			}

			mcpTool := types.MCPTool{
				Name:        toolName,
				Description: tool.Description,
				Parameters:  make(map[string]interface{}),
			}

			// Convert parameters schema if available
			var schemaMap map[string]interface{}
			schemaBytes, err := sonic.Marshal(tool.InputSchema)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal input schema: %w", err)
			}
			if unmarshalErr := sonic.Unmarshal(schemaBytes, &schemaMap); unmarshalErr == nil {
				mcpTool.Parameters = schemaMap
			}
			mcpTools = append(mcpTools, mcpTool)
		}
	}

	// 缓存结果
	m.toolsCache = mcpTools
	m.cacheInitialized = true

	return mcpTools, nil
}

// Close 关闭所有MCP客户端
func (m *MCPRuntimeManager) Close() error {
	if m.toolManager != nil {
		return m.toolManager.Close()
	}
	return nil
}

// NewTaskPoller 创建新的任务轮询器
func NewTaskPoller(client *apiclient.Client, sessionID int64, pollInterval time.Duration) *TaskPoller {
	return &TaskPoller{
		client:       client,
		sessionID:    sessionID,
		pollInterval: pollInterval,
	}
}

// Start 启动任务轮询循环
func (p *TaskPoller) Start(ctx context.Context, taskChan chan<- *dao_tool_call_task.ObjToolCallTask) {
	ticker := time.NewTicker(p.pollInterval)
	defer ticker.Stop()

	log.Printf("Starting task poller with interval %v", p.pollInterval)

	for {
		select {
		case <-ctx.Done():
			log.Printf("Task poller stopped")
			return
		case <-ticker.C:
			task, err := p.client.GetPendingTask(p.sessionID)
			if err != nil {
				log.Printf("Error getting pending task: %v", err)
				continue
			}
			if task != nil {
				select {
				case taskChan <- task:
					log.Printf("📥Received task. call_id: %s, tool_name: %s, arguments: %s, session_id: %d", task.CallID, task.ToolName, task.Arguments, task.SessionID)
				case <-ctx.Done():
					return
				}
			}
		}
	}
}

// NewToolExecutor 创建新的工具执行器
func NewToolExecutor(runtime *MCPRuntimeManager, envMgr *EnvironmentManager, client *apiclient.Client) *ToolExecutor {
	return &ToolExecutor{
		runtime: runtime,
		envMgr:  envMgr,
		client:  client,
	}
}

// ExecuteTask 执行任务
func (e *ToolExecutor) ExecuteTask(ctx context.Context, task *dao_tool_call_task.ObjToolCallTask) {
	startedAt := time.Now()
	var taskClaimed bool

	select {
	case <-ctx.Done():
		log.Printf("Task %s cancelled before execution due to timeout or cancellation", task.CallID)
		return
	default:
	}

	defer func() {
		if r := recover(); r != nil {
			log.Printf("🚨Task %s panicked: %v", task.CallID, r)
			if taskClaimed {
				if err := e.client.CompleteTask(task.CallID, task.SessionID,
					string(dao_tool_call_task.ToolCallTaskStatusFailed),
					nil, "", "", "", "", startedAt, fmt.Sprintf("Task panicked: %v", r)); err != nil {
					log.Printf("🚨Error completing panicked task %s: %v", task.CallID, err)
				}
			}
		}
	}()

	log.Printf("📌Attempting to claim task. call_id: %s", task.CallID)
	if err := e.client.ClaimTask(task.CallID, task.SessionID); err != nil {
		log.Printf("🚨CRITICAL: Failed to claim task %s for session %d: %v", task.CallID, task.SessionID, err)
		// 任务认领失败，不需要完成，因为没有被认领(可能是重复消费或者已经被其他实例执行过了？)
		return
	}
	taskClaimed = true

	select {
	case <-ctx.Done():
		log.Printf("🚨Task %s cancelled after claiming due to timeout or cancellation", task.CallID)
		if err := e.client.CompleteTask(task.CallID, task.SessionID,
			string(dao_tool_call_task.ToolCallTaskStatusFailed),
			nil, "", "", "", "", startedAt, fmt.Sprintf("Task cancelled due to timeout: %v", ctx.Err())); err != nil {
			log.Printf("🚨Error completing cancelled task. call_id: %s, error: %v", task.CallID, err)
		}
		return
	default:
	}

	log.Printf("🚀Successfully claimed and executing task. call_id: %s", task.CallID)

	var oldEnvMD5, oldEnvURL, newEnvMD5, newEnvURL string
	var result interface{}
	var toolErr error
	var status string
	var errorMessage string

	oldEnvMD5, oldEnvURL, err := e.envMgr.envMgr.CreateSnapshot(ctx)
	if err != nil {
		log.Printf("🚨Error creating environment snapshot before execution: %v", err)
		if ctx.Err() != nil {
			log.Printf("🚨Task %s cancelled during snapshot creation due to timeout", task.CallID)
			errorMessage = fmt.Sprintf("Task cancelled during snapshot creation: %v", ctx.Err())
		} else {
			errorMessage = fmt.Sprintf("🚨Failed to create environment snapshot before execution: %v", err)
		}
		status = string(dao_tool_call_task.ToolCallTaskStatusFailed)
		oldEnvMD5 = ""
		oldEnvURL = ""
		newEnvMD5 = ""
		newEnvURL = ""
	} else {
		select {
		case <-ctx.Done():
			log.Printf("🚨Task %s cancelled before tool execution due to timeout", task.CallID)
			status = string(dao_tool_call_task.ToolCallTaskStatusFailed)
			errorMessage = fmt.Sprintf("🚨Task cancelled before tool execution: %v", ctx.Err())
			newEnvMD5 = oldEnvMD5
			newEnvURL = oldEnvURL
		default:
			argumentsStr := task.Arguments
			var serverName, toolName string
			if strings.Contains(task.ToolName, "__") {
				splits := strings.Split(task.ToolName, "__")
				serverName = splits[0]
				toolName = splits[1]
			} else {
				var exists bool
				serverName, exists = e.runtime.toolToClientMap[task.ToolName]
				if !exists {
					panic(fmt.Sprintf("tool name '%s' not found in tool-to-client mapping, current server-tool list: %v", task.ToolName, e.runtime.toolToClientMap))
				}
				toolName = task.ToolName
			}

			result, toolErr = e.runtime.toolManager.InvokableRun(ctx, serverName, toolName, argumentsStr)

			if ctx.Err() != nil {
				log.Printf("🚨Task %s cancelled during tool execution due to timeout", task.CallID)
				status = string(dao_tool_call_task.ToolCallTaskStatusFailed)
				errorMessage = fmt.Sprintf("🚨Task cancelled during tool execution: %v", ctx.Err())
				newEnvMD5 = oldEnvMD5
				newEnvURL = oldEnvURL
			} else {
				var err2 error
				newEnvMD5, newEnvURL, err2 = e.envMgr.envMgr.CreateSnapshot(ctx)
				if err2 != nil {
					log.Printf("🚨Error creating environment snapshot after execution: %v", err2)
					newEnvMD5 = oldEnvMD5
					newEnvURL = oldEnvURL
				}

				if toolErr != nil {
					status = string(dao_tool_call_task.ToolCallTaskStatusFailed)
					errorMessage = toolErr.Error()
					log.Printf("🚨Task %s failed: %v", task.CallID, toolErr)
				} else {
					status = string(dao_tool_call_task.ToolCallTaskStatusSuccess)
					log.Printf("✅Task %s completed successfully", task.CallID)
				}
			}
		}
	}

	// Always complete the task to prevent it from being retried
	log.Printf("Attempting to complete task %s with status: %s", task.CallID, status)
	if err := e.client.CompleteTask(task.CallID, task.SessionID, status, result, oldEnvMD5, newEnvMD5, oldEnvURL, newEnvURL, startedAt, errorMessage); err != nil {
		log.Printf("CRITICAL: Failed to complete task %s with status %s: %v", task.CallID, status, err)
		log.Printf("Task completion details - ErrorMessage: %s, OldEnvMD5: %s, NewEnvMD5: %s", errorMessage, oldEnvMD5, newEnvMD5)
		// Even if completion fails, we've done our best to update the status
	} else {
		log.Printf("✅Successfully committed task. call_id: %s, status: %s", task.CallID, status)
	}
}

// NewMCPRuntime creates a new MCP runtime
func NewMCPRuntime(config *RuntimeConfig, ctx context.Context) *MCPRuntime {
	ctx, cancel := context.WithCancel(ctx)

	return &MCPRuntime{
		config:    config,
		apiClient: apiclient.NewClient(config.MCPAPIBase),
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Initialize initializes the MCP runtime
func (r *MCPRuntime) Initialize() error {
	log.Printf("Initializing MCP Runtime for session: %d", r.config.SessionID)

	// Get session information
	session, servers, env, err := r.apiClient.GetSessionInfo(r.config.SessionID)
	if err != nil {
		return fmt.Errorf("get session info: %w", err)
	}

	log.Printf("Session info: container_status=%s, env_id=%d, servers=%d",
		session.ContainerStatus, session.EnvID, len(servers))

	// Initialize environment manager
	r.envManager = NewEnvironmentManager(r.config.WorkingDir, r.apiClient, &bosconfig.BosConfig{
		AccessKey: r.config.BosAccessKey,
		SecretKey: r.config.BosSecretKey,
		Endpoint:  r.config.BosEndpoint,
		Bucket:    r.config.BosBucket,
	})

	// Initialize environment
	if err := r.envManager.InitializeEnvironment(r.ctx, env); err != nil {
		return fmt.Errorf("initialize environment: %w", err)
	}

	// Initialize MCP runtime
	r.mcpRuntime = NewMCPRuntimeManager(r.config.UseServerPrefix)
	if err := r.mcpRuntime.LoadMCPServers(r.ctx, servers); err != nil {
		return fmt.Errorf("load MCP servers: %w", err)
	}

	// Report session ready
	tools, err := r.mcpRuntime.GetAvailableTools(r.ctx)
	if err != nil {
		return fmt.Errorf("get available tools: %w", err)
	}
	if err := r.apiClient.ReportSessionReady(r.config.SessionID, tools); err != nil {
		return fmt.Errorf("report session ready: %w", err)
	}

	// Initialize task poller
	r.taskPoller = NewTaskPoller(r.apiClient, r.config.SessionID, r.config.PollInterval)

	// Initialize tool executor
	r.toolExecutor = NewToolExecutor(r.mcpRuntime, r.envManager, r.apiClient)

	log.Printf("MCP Runtime initialized successfully with %d tools", len(tools))
	return nil
}

// Start starts the MCP runtime
func (r *MCPRuntime) Start() error {
	log.Printf("Starting MCP Runtime...")

	// Create task channel
	taskChan := make(chan *dao_tool_call_task.ObjToolCallTask, 10)

	// Start task poller
	go r.taskPoller.Start(r.ctx, taskChan)

	// Start task executor
	go r.runTaskExecutor(taskChan)

	// Wait for termination signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-sigChan:
		log.Printf("Received termination signal, shutting down...")
	case <-r.ctx.Done():
		log.Printf("Context cancelled, shutting down...")
	}

	return nil
}

// runTaskExecutor runs the task executor loop
func (r *MCPRuntime) runTaskExecutor(taskChan <-chan *dao_tool_call_task.ObjToolCallTask) {
	for {
		select {
		case <-r.ctx.Done():
			return
		case task := <-taskChan:
			go r.toolExecutor.ExecuteTask(r.ctx, task)
		}
	}
}

// Stop stops the MCP runtime
func (r *MCPRuntime) Stop(message string) error {
	log.Printf("Stopping MCP Runtime...")

	// 调用session stop API进行资源清理
	if r.apiClient != nil && r.config != nil {
		log.Printf("Calling session stop API for session: %d", r.config.SessionID)
		if err := r.apiClient.StopSession(r.config.SessionID, message); err != nil {
			// 记录错误但不阻止关闭流程
			log.Printf("Failed to call session stop API: %v", err)
		} else {
			log.Printf("Session stop API called successfully")
		}
	}

	r.cancel()

	if r.mcpRuntime != nil {
		if err := r.mcpRuntime.Close(); err != nil {
			log.Printf("Error closing MCP runtime: %v", err)
		}
	}

	return nil
}

// parseFlags parses command line flags
func parseFlags() *RuntimeConfig {
	var config RuntimeConfig

	flag.Int64Var(&config.SessionID, "session-id", 0, "Session ID")
	flag.StringVar(&config.MCPAPIBase, "mcp-api-base", "http://localhost:8080", "MCP API base URL")
	flag.StringVar(&config.WorkingDir, "working-dir", "./", "Working directory")
	flag.DurationVar(&config.PollInterval, "poll-interval", 1*time.Second, "Task polling interval")
	flag.DurationVar(&config.Timeout, "timeout", 30*time.Minute, "Task timeout")
	flag.BoolVar(&config.UseServerPrefix, "use-server-prefix", false, "Whether to use server_name prefix in tool names (default: true)")

	// BOS配置参数
	flag.StringVar(&config.BosAccessKey, "bos-access-key", "", "BOS Access Key")
	flag.StringVar(&config.BosSecretKey, "bos-secret-key", "", "BOS Secret Key")
	flag.StringVar(&config.BosEndpoint, "bos-endpoint", "", "BOS Endpoint")
	flag.StringVar(&config.BosBucket, "bos-bucket", "", "BOS Bucket")

	flag.Parse()

	if config.SessionID == 0 {
		log.Fatal("session-id is required")
	}

	if config.BosAccessKey == "" || config.BosSecretKey == "" || config.BosEndpoint == "" || config.BosBucket == "" {
		log.Fatal("BOS configuration is required: bos-access-key, bos-secret-key, bos-endpoint, bos-bucket")
	}

	return &config
}

// main is the entry point
func main() {
	config := parseFlags()

	log.Printf("Starting MCP Runtime with config: %+v", config)

	// Create working directory
	if err := os.MkdirAll(config.WorkingDir, 0755); err != nil {
		log.Fatalf("Failed to create working directory: %v", err)
	}

	// Create a context with timeout for the entire program
	ctx, cancel := context.WithTimeout(context.Background(), config.Timeout)
	defer cancel()

	// Create and initialize MCP runtime
	runtime := NewMCPRuntime(config, ctx)

	if err := runtime.Initialize(); err != nil {
		log.Fatalf("Failed to initialize MCP runtime: %v", err)
	}

	// Start the runtime in a goroutine
	errChan := make(chan error, 1)
	go func() {
		errChan <- runtime.Start()
	}()

	// Wait for either completion, timeout, or error
	stopMessage := ""
	select {
	case err := <-errChan:
		if err != nil {
			stopMessage = "Error: MCP Runtime error: " + err.Error()
			log.Printf("%s", stopMessage)
		}
	case <-ctx.Done():
		switch ctx.Err() {
		case context.DeadlineExceeded:
			stopMessage = "Normal: MCP Runtime timed out after " + config.Timeout.String()
			log.Printf("%s Shutting down gracefully...", stopMessage)
		case context.Canceled:
			stopMessage = "Normal: MCP Runtime was cancelled."
			log.Printf("%s Shutting down gracefully...", stopMessage)
		}
	}

	// Stop the runtime
	if err := runtime.Stop(stopMessage); err != nil {
		log.Printf("Error stopping MCP runtime: %v", err)
	}

	log.Printf("MCP Runtime stopped successfully")
}

# 使用Python基础镜像作为运行环境
FROM python:3.11-slim

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONPATH=/home/<USER>
ENV PYTHONUNBUFFERED=1
ENV TZ=Asia/Shanghai

# 安装系统依赖，添加重试机制和错误处理
RUN apt-get update --fix-missing && \
    apt-get install -y --fix-missing --no-install-recommends \
    curl \
    git \
    wget \
    ca-certificates \
    gnupg \
    bsdmainutils \
    lsb-release \
    sqlite3 \
    tzdata \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 配置时区为北京时间 (Asia/Shanghai)
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装Node.js（使用NodeSource官方源，更稳定）
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# ------ 在 root 用户下安装 Playwright 浏览器系统依赖 ------
ARG PLAYWRIGHT_MCP_VERSION=0.0.27

# 配置npm镜像源（在root用户下）
RUN echo "registry=https://registry.npmmirror.com" > /root/.npmrc

# 安装 Playwright 系统依赖（需要 root 权限）
# RUN npx -y playwright-core install-deps chromium

# 创建work用户和组（符合公司规范）
RUN (groupdel work 2>/dev/null || true) && \
    (userdel work 2>/dev/null || true) && \
    groupadd -g 1000 work && \
    useradd -u 1000 -g work -m -d /home/<USER>/bin/sh work && \
    mkdir -p /home/<USER>
    chown work:work /home/<USER>
    chmod 755 /home/<USER>

# 切换到work用户进行后续操作
USER work
WORKDIR /home/<USER>

# 配置npm镜像源（work用户）
RUN echo "registry=https://registry.npmmirror.com" > ~/.npmrc

# ------ 预安装 MCP 服务器依赖 ------
ARG FILESYSTEM_MCP_VERSION=2025.3.28
ARG FIRECRAWL_MCP_VERSION=1.11.0

# 预下载和缓存 MCP 包依赖，这样运行时 npx 可以直接使用缓存
RUN echo "预安装 MCP 服务器依赖..." && \
    npx -y @modelcontextprotocol/server-filesystem@${FILESYSTEM_MCP_VERSION} --help > /dev/null 2>&1 || true && \
    npx -y firecrawl-mcp@${FIRECRAWL_MCP_VERSION} --help > /dev/null 2>&1 || true && \
    # npx -y @playwright/mcp@${PLAYWRIGHT_MCP_VERSION} --help > /dev/null 2>&1 || true && \
    echo "MCP 依赖预安装完成"

# ------ 重要修复：正确设置 Playwright 浏览器路径 ------
# 设置 Playwright 浏览器路径环境变量，确保运行时和构建时一致
ENV PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/ms-playwright

# 安装 Playwright 浏览器到指定路径（在 work 用户下）
# RUN npx -y playwright-core install chromium && \
    # echo "Playwright 浏览器安装完成到: $PLAYWRIGHT_BROWSERS_PATH"

# 验证浏览器安装（调试用）
# RUN echo "验证浏览器安装路径:" && \
    # ls -la $PLAYWRIGHT_BROWSERS_PATH/ && \
    # find $PLAYWRIGHT_BROWSERS_PATH -name "chrome" -type f | head -5

# 设置机器时间和环境
ENV LC_ALL=en_US.utf8
RUN echo 'alias ll="ls -lah --color=auto"' >> ~/.bashrc

# 安装 uv 包管理器
RUN pip install --no-cache-dir --user uv

# 添加 ~/.local/bin 到 PATH（在安装uv之后）
ENV PATH=/home/<USER>/.local/bin:$PATH

# 配置 uv 镜像源
RUN mkdir -p ~/.config/uv && \
    echo '[[index]]' > ~/.config/uv/uv.toml && \
    echo 'url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"' >> ~/.config/uv/uv.toml && \
    echo 'default = true' >> ~/.config/uv/uv.toml

# 安装Python依赖
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
RUN pip install --no-cache-dir --user dspy-ai mcp fastmcp asyncio aiofiles litellm beautifulsoup4 lxml openpyxl python-docx xlwings pandas xlcalculator pdfplumber fpdf

# --- 预缓存 uvx 工具 (sqlite) ---
# 使用 uvx 预先运行一次，让它创建环境和缓存
RUN mkdir -p /home/<USER>/tmp/test-sqlite && \
    cd /home/<USER>/tmp/test-sqlite && \
    echo '{}' > demo.db && \
    timeout 10s uvx mcp-server-sqlite==2025.4.25 --db-path ./test.db --help || true && \
    cd /home/<USER>
    rm -rf /home/<USER>/tmp/test-sqlite

# 创建必要的目录
RUN mkdir -p /home/<USER>/data /home/<USER>/logs /home/<USER>/mcp /home/<USER>/servers

# 设置工作目录为 /home/<USER>
WORKDIR /home/<USER>/mcp
# 复制编译好的Go二进制文件
COPY --chmod=755 cmd/mcp_runtime ./mcp_runtime


# 使用 ENTRYPOINT 启动脚本（通过绝对路径）
ENTRYPOINT ["./mcp_runtime"]
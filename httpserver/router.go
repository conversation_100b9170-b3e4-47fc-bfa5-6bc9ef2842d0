// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package httpserver

import (
	"icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver/module"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
)

// httpRouter 获取web路由
//
// HTTP Server 和 Router 的文档：
// http://gdp.baidu-int.com/gdp2/docs/examples/server/20_http_server/
func httpRouter(ser *ghttp.DefaultServer) ghttp.Router {
	logger := resource.LoggerService

	// 若 HTTP Server 内部出现异常，将通过此 logger 打印日志
	ghttp.DefaultLogger = logger

	router := ghttp.NewRouter()
	router.SetLogger(logger)

	// 注册采集 HTTP Server 接口指标的中间件
	// 详见 https://gdp.baidu-int.com/api/baidu/gdp/metrics/gmetrics/
	router.Use(gmetrics.Default().CollectHTTPServer())

	// trace中间件 注意位置需要放到日志中间件之前， 这样日志中能够打印出相应的trace信息
	// router.Use(gtrace.Default().TraceHTTPServer())
	//router.Use((&plugin.HTTPServerTracePlugin{}).Trace)

	if ser.WriteTimeout > 0 {
		// 若 server 有设置 WriteTimeout，则整体加上超时控制
		// 这样：
		// 1.日志中可以打印出由于 server 超时导致的504异常
		// 2.业务逻辑可以更及时的终止运行
		router.Use(ghttp.NewTimeoutMiddleWareFunc(ser.WriteTimeout, nil))
	}

	// 注册日志中间件，初始化日志功能，打印访问日志( access_log )
	//  当前 logger 是使用配置文件 conf/logit/service.toml 初始化生成的
	//  日志文件打印到了 {gdp.LogDir}/service/service.log
	//  若需要对日志字段进行调整，请修改这里：
	//  使用自定义的LogFields，不要使用 ghttp.DefaultServerLogFields
	router.Use(ghttp.NewLogMiddleWareFunc(logger, ghttp.DefaultServerLogFields))

	// 注册 panic Recover 中间件，可以处理 handlerFunc 里 出现的 panic
	// 避免程序整体 panic
	// 需要注意的是，若是使用 go xxx() 自己新开启的协程，是不能 recover 的
	router.Use(ghttp.NewRecoverMiddleWareFunc(logger, nil))

	// 注册 Panel UI
	// registerPanel 注册 /debug/panel/，提供了可视化的 UI 让我们可以更方便的查看、观察应用的状态信息
	// see http://gdp.baidu-int.com/gdp2/docs/examples/server/26_observer_server/
	module.RegisterPanel(router)

	// 注册 Metrics Api
	module.RegisterMetrics(router)

	// 注册 面向外部用户 API
	module.RegisterAPI(router)

	return router
}

package module

import (
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/metrics"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
)

func RegisterMetrics(router ghttp.Router) {
	router.Use(gmetrics.Default().CollectHTTPServer())
	// 注册路径为 /metrics，包括两个子路径，用于给 prometheus 采集：
	// /metrics/process  :  进程的指标信息
	// /metrics/service  :  RPC Server 和 RPC Client 的指标信息

	registerDaemonMetrics()
	gmetrics.Default().RegisterExporterTo(router)
}

func registerDaemonMetrics() {
	// Initialize MCP custom metrics
	metrics.InitMetrics()

	// Start metrics collection
	err := metrics.StartMetricsCollection()
	if err != nil {
		// Log error but continue
		// The server will still function without metrics collection
	}
}

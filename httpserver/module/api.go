package module

import (
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	controller "icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver/controller/api"
)

func RegisterAPI(router ghttp.Router) {
	modRouter := libRouter.Router{Prefix: "/api/v1/mcp", Router: router}
	controller.RegisteInternalRouter(&modRouter)
	controller.RegisteMcpRouter(&modRouter)
	controller.RegisteEnvRouter(&modRouter)
	controller.RegisteSessionRouter(&modRouter)
	controller.RegisteToolRouter(&modRouter)
	controller.RegisteImageRouter(&modRouter)
	controller.RegisteCalculateRewardRouter(&modRouter)
}

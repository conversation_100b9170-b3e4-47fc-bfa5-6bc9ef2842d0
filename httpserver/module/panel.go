package module

import (
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/panel"
)

func RegisterPanel(router ghttp.Router) {
	// 会使用配置文件 /conf/gdp/panel/panel.toml
	ps := panel.NewDefaultServer(panel.MustAutoLoadConfig())

	// 注册业务Business
	//ps.RegisterCollector(businessPanel.TaskTraceOpt)

	// 默认已限制了不能通过外网访问
	// 请注意该页面不可通过其他方式被外网访问（如其他内网代理）
	ps.RegisterToRouter(router, "/debug/panel/")
}

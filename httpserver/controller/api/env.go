package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	env_service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/env"
)

func InitEnv(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &env_service.InitEnv{})
}

func RegisteEnvRouter(router *libRouter.Router) {
	router.Post("/env/init", InitEnv)
}

package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	calcreward_service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/calcreward"
)

func CalculateReward(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &calcreward_service.CalculateRewardSever{})
}

func RegisteCalculateRewardRouter(router *libRouter.Router) {
	router.Post("/calc_reward", CalculateReward)
}

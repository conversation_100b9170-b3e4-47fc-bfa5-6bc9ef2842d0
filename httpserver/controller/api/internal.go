package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/intern"
)

// ToolPending 处理获取待处理任务请求（内部API）
func ToolPending(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &intern.ToolPending{})
}

// ToolClaim 处理认领任务请求（内部API）
func ToolClaim(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &intern.ToolClaim{})
}

// ToolComplete 处理完成任务请求（内部API）
func ToolComplete(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &intern.ToolComplete{})
}

// SessionReady 处理Session就绪通知请求
func SessionReady(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &intern.SessionReady{})
}

// SessionInfo 处理获取Session信息请求
func SessionInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &intern.SessionInfo{})
}

// SessionStopInternal 处理Session停止请求（内部API）
func SessionStopInternal(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &intern.SessionStop{})
}

func RegisteInternalRouter(router *libRouter.Router) {

	// 内部API路由 - 容器调用的接口
	router.Get("/internal/tool/pending", ToolPending)    // 获取待处理任务
	router.Post("/internal/tool/claim", ToolClaim)       // 认领任务
	router.Post("/internal/tool/complete", ToolComplete) // 完成任务

	// Session就绪通知接口
	router.Post("/internal/session/ready", SessionReady)

	// Session信息查询接口
	router.Get("/internal/session/info", SessionInfo)

	// Session停止接口（内部API）
	router.Post("/internal/session/stop", SessionStopInternal)
}

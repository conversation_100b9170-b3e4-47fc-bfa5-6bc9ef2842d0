package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/session"
)

// SessionInit 处理Session初始化请求
func SessionInit(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &session.SessionInit{})
}

// SessionStop 处理Session停止请求
func SessionStop(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &session.SessionStop{})
}

// SessionStatus 处理Session状态查询请求
func SessionStatus(ctx context.Context, req ghttp.Request) ghttp.Response {
	// TODO: 实现SessionStatus服务
	// 这里先返回一个简单的响应
	return ghttp.NewJSONResponse(200, map[string]interface{}{
		"code":    0,
		"message": "Session状态查询功能待实现",
		"data":    nil,
	})
}

// SessionExec 处理Session命令执行请求
func SessionExec(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &session.SessionExec{})
}

// SessionCopyin 处理Session文件复制请求
func SessionCopyin(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &session.SessionCopyin{})
}

// RegisterSessionRouter 注册Session相关的路由
func RegisteSessionRouter(router *libRouter.Router) {
	// Session初始化接口
	router.Post("/session/init", SessionInit)

	// Session停止接口
	router.Post("/session/stop", SessionStop)

	// Session状态查询接口
	router.Get("/session/status", SessionStatus)

	// Session命令执行接口
	router.Post("/session/exec", SessionExec)

	// Session文件复制接口
	router.Post("/session/copyin", SessionCopyin)
}

package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	mcp_service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/mcpservice"
)

func RegisterMcpServer(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &mcp_service.RegisterMcpSever{})
}

func RegisteMcpRouter(router *libRouter.Router) {
	router.Post("/server/register", RegisterMcpServer)
}

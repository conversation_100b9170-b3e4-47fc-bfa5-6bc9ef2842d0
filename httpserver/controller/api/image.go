package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	image_service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/image"
)

// ImageRegister 处理镜像初始化请求
func ImageRegister(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, &image_service.ImageRegister{})
}

// RegisteImageRouter 注册镜像相关的路由
func RegisteImageRouter(router *libRouter.Router) {
	// 镜像初始化接口
	router.Post("/image/register", ImageRegister)
}

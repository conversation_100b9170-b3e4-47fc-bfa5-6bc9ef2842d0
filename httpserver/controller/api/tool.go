package controller

import (
	"context"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	libRouter "icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/router"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool"
)

// ToolCallSync 处理同步工具调用请求
func ToolCallSync(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, tool.NewToolCallSync())
}

// ToolCallSync 处理同步工具调用请求
func ToolList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return controller.CommonExecute(ctx, req, tool.NewToolList())
}

// RegisterToolRouter 注册工具调用相关的路由
func RegisteToolRouter(router *libRouter.Router) {
	// 同步工具调用接口 - 外部API
	router.Post("/tool/call", ToolCallSync)
	// 查询工具列表 - 外部API
	router.Post("/tool/list", ToolList)

}

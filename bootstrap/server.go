// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package bootstrap

import (
	"context"

	"icode.baidu.com/baidu/gdp/hestia/starter"
	"icode.baidu.com/baidu/gdp/net/gserver"
	"icode.baidu.com/baidu/gdp/net/gserver/protocols"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver"
)

// StartServers 启动服务
func StartServers(ctx context.Context, cfg *Config) error {
	// 初始化 Server 的管理容器，采用 Hestia 和 非 Hestia 的方式都支持 Graceful Shutdown
	// 若 EnableHestia=true，意味着使用热重启功能，上线速度更快，目前在 潘多拉上运行良好，其他 Pass 平台未验证
	// Hestia 的文档 http://wiki.baidu.com/pages/viewpage.action?pageId=1370968292
	container, err := starter.NewWithConfFile(cfg.HestiaConfPath(), cfg.EnableHestia)
	if err != nil {
		return err
	}

	// 从配置中取到 default Wroker 的第 0 个 Listen 地址，后面的多协议 server 会用到
	addr, err := container.ServerAddr("default", 0)
	if err != nil {
		return err
	}

	// 使用同端口多协议能力，即可以在同一个端口，如 8080，上支持 HTTP、 NSHEAD、 PBRPC 等协议
	// http://gdp.baidu-int.com/gdp2/docs/examples/server/25_multi_protocol_server/
	mp := &gserver.MultiProtocolServer{}

	// HTTP 协议的 Server
	// 目前公司内应用的稳定性指标已在逐步迁移到使用 prometheus 的方式、同时 GDP 提供的应用面板功能
	// 都是需要 HTTP Server 的，所以建议所有应用都保留 HTTP Server
	serverHTTP := httpserver.NewServer(ctx, cfg.HTTPServer)
	mp.Bind(protocols.HTTPProtocol, addr, serverHTTP)

	// 若需要其他的协议的 Server，可以参考上述方式注册即可

	// 未识别的协议的处理
	// mp.BindDefault(addr,otherServer)

	if err = container.SetServer("default", 0, mp); err != nil {
		return err
	}

	return container.StartGraceful(ctx)
}

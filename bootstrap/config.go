// Copyright(C) 2022 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2022-06-22, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package bootstrap

import (
	"errors"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver"
)

// Config app的配置
// 默认对应 conf/app.toml
type Config struct {
	// AppName 应用名称
	AppName string

	// IDC 所在逻辑 idc
	IDC string

	// RunMode 运行模式，可配置值：
	// debug    : 调试，    对应常量 env.RunModeDebug
	// test     : 测试，    对应常量 env.RunModeTest
	// release  : 线上发布， 对应常量 env.RunModeRelease
	RunMode string

	// EnableHestia 是否使用 baidu/gdp/hestia 提供的热重启功能
	EnableHestia bool

	// HestiaDirName 热重启模块的目录名称，可选，若为空，使用默认值 hestia
	HestiaDirName string

	Env env.AppEnv

	// HTTPServer HTTP server 的 配置
	HTTPServer httpserver.Config
}

// Check 检查配置是否正确
func (c *Config) Check() error {
	if c.AppName == "" {
		return errors.New("config.AppName is empty")
	}
	if c.IDC == "" {
		return errors.New("config.IDC is empty")
	}
	if c.RunMode == "" {
		return errors.New("config.RunMode is empty")
	}
	// 自定义
	return nil
}

// HestiaConfPath 获取 hestia 配置文件的 路径
func (c *Config) HestiaConfPath() string {
	return filepath.Join(env.RootDir(), "conf", "hestia.toml")
}

// ParserAppConfig 解析应用配置
func ParserAppConfig(filePath string) (*Config, error) {
	confPath, err := filepath.Abs(filePath)
	if err != nil {
		return nil, err
	}
	var c *Config
	if err := conf.Parse(confPath, &c); err != nil {
		return nil, err
	}
	if err := c.Check(); err != nil {
		return nil, err
	}
	// 解析并设置全局应用环境信息
	rootDir := filepath.Dir(filepath.Dir(confPath))
	// 先将程序进程切换到应该的根目录下，这样程序内部可以正常使用相对路径
	if err = os.Chdir(rootDir); err != nil {
		return nil, err
	}
	idc := c.IDC
	if c.RunMode == "release" {
		idcName := osEnvDefault("APPSPACE_IDC_NAME", idc)
		envName := osEnvDefault("APPSPACE_ENV", "prod")
		idc = envName + "_" + idcName
	}
	opt := env.Option{
		AppName: c.AppName,
		IDC:     idc,
		RunMode: c.RunMode,
		RootDir: rootDir,
		DataDir: filepath.Join(rootDir, "data"),
		LogDir:  filepath.Join(rootDir, "log"),
		ConfDir: filepath.Join(rootDir, filepath.Base(filepath.Dir(confPath))),
	}
	c.Env = env.New(opt)
	return c, nil
}

// MustLoadAppConfig 加载 app.toml ,若失败，会 panic
func MustLoadAppConfig(appConfigPath string) *Config {
	config, err := ParserAppConfig(appConfigPath)
	if err != nil {
		panic(err.Error())
	}
	env.Default = config.Env
	return config
}

func osEnvDefault(key string, def string) string {
	v := os.Getenv(key)
	if len(v) == 0 {
		return def
	}
	return v
}

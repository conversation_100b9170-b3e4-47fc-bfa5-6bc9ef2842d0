package bootstrap

import (
	"context"
	"log"
	"path/filepath"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/extension/observer"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
	"icode.baidu.com/baidu/gdp/metrics/gtrace"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"
	"icode.baidu.com/baidu/gdp/paas"
	"icode.baidu.com/baidu/health/kylin/v2/kylin"
	rcc "icode.baidu.com/baidu/smartprogram/rcc2-go-sdk"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/background"
)

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile | log.Lmicroseconds)
}

// MustInit 组件初始化，若失败，会panic
func MustInit(ctx context.Context) {
	initMetrics()
	initTrace(ctx)
	initForPaas()
	initLoggers(ctx)
	initRal(ctx)
	loadServicer(ctx)
	initMySQL(ctx)
	initTaskPollingService(ctx)
	initSessionMonitoringService(ctx)
	initObserver()
	initBos()
	initBrcc(ctx)
	// hookStd()
}

func MustInitTest(ctx context.Context) {
	initLoggers(ctx)
	initRal(ctx)
	loadServicer(ctx)
	initMySQL(ctx)
	initBos()
	initBrcc(ctx)
}

// 重定向 stderr 和 stdout 到指定文件
// stderr 输出到 log/std/stderr.log
// stdout 输出到 log/std/stdout.log
// 会使用配置文件:  conf/logit/stderr.toml 和 conf/logit/stdout.toml
// 若配置文件不存在会使用默认配置
func hookStd() {
	sh := &logit.StdHooker{}
	sh.HookStd()
}

func initMetrics() {
	// 初始化指标统计组件
	// 会自动的给 ral、mysql、redis 等 rpc client 注册 用于采集指标的 interceptor
	gmetrics.Default().Init()

	// Note: MCP custom metrics are initialized in httpserver/module/metrics.go
	// when the HTTP server starts, to ensure they're registered after gmetrics.Init()
}

func initForPaas() {
	// 加载潘多拉的 port.conf 配置文件 http://wiki.baidu.com/pages/viewpage.action?pageId=1134316246
	// 会将端口信息写入环境变量，方便后续逻辑使用
	// 若应用部署在 其他平台，可以将该逻辑注释掉
	paas.MustExportPandoraPortConfToOsEnv(filepath.Join(env.ConfDir(), "port.conf"))
}

// initObserver 组件状态的观察、状态信息的导出(问题排查使用)
//
//	每 30 秒会将注册到 servicer.DefaultMapper 里的所有信息导出到 data/observer_dump/ 目录
//	比如一个 servicer 的可用机器列表，使用的 BNS 配置，最终生效的配置等信息
//	都可以从 dump 出的文件里查看到
func initObserver() {
	observer.Add("servicer_mapper", servicer.DefaultMapper)
	df := &observer.FileDumper{
		Duration: 30 * time.Second,
		Dir:      filepath.Join(env.DataDir(), "observer_dump"),
	}
	df.MustStart()
}

// initLoggers 初始化logger
// http://gdp.baidu-int.com/gdp2/docs/examples/foundation/12_log/
func initLoggers(ctx context.Context) {
	base.TraceLogEnable = true
	{
		// Service 日志
		webLogger, errWebLogger := logit.NewLogger(ctx, logit.OptConfigFile("logit/service.toml"))
		if errWebLogger != nil {
			panic(errWebLogger.Error())
		}
		resource.LoggerService = webLogger
		base.LoggerService = webLogger
		gtask.BeforeShutdown(webLogger) // 注册 closer,这样可以保证程序进程退出前，日志全部落盘

		// Background 日志
		bgLogger, errWebLogger := logit.NewLogger(ctx, logit.OptConfigFile("logit/background.toml"))
		if errWebLogger != nil {
			panic(errWebLogger.Error())
		}
		resource.LoggerBg = bgLogger
		gtask.BeforeShutdown(bgLogger) // 注册 closer,这样可以保证程序进程退出前，日志全部落盘
	}
}

// initRal 初始化 RAL 组件
// http://gdp.baidu-int.com/gdp2/docs/examples/client/31_ral/
func initRal(ctx context.Context) {
	_ = ral.InitDefault(ctx)
}

// loadServicer 加载服务配置
// http://gdp.baidu-int.com/gdp2/docs/examples/client/30_servicer/
func loadServicer(ctx context.Context) {
	pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
}

// initMySQL 初始化 MySQL Client
// http://gdp.baidu-int.com/gdp2/docs/examples/client/32_mysql/
func initMySQL(_ context.Context) {
	// client 初始化为单例，放到 resource 里去
	resource.MySQLClientMain = mustInitOneMySQL("drds_dsm_main")
}

func mustInitOneMySQL(name any) mysql.Client {
	opts := []mysql.ClientOption{
		mysql.OptInterceptor(kylin.GetKylinMySQLInterceptor().Interceptor()),

		// MySQL Client 在创建时，默认包含了 LogObserver
		// 定义在 mysql.DefaultObserverFuncs
	}
	client, err := mysql.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}

// InvokeBeforeShutdown 退出前执行，资源清理、日志落盘等
func InvokeBeforeShutdown() {
	gtask.InvokeBeforeShutdown()
}

func initTrace(ctx context.Context) {
	// 初始化Trace埋点；给 RAL、MySQL、Redis 等注册拦截器, 需要在servicer 加载后使用
	gtrace.Default().Init()
	fn := func() error {
		return gtrace.Default().Shutdown(ctx)
	}
	gtask.BeforeShutdown(fn)
}

// initBos 初始化百度对象存储（BOS）客户端
func initBos() {
	// 从配置文件加载 BOS 配置
	bosConfig, err := config.LoadBosConfig()
	if err != nil {
		log.Panicf("load bos config failed: %s", err.Error())
	}
	config.DataStorageBosConfigGlobal = bosConfig.DataStorageBos // 初始化 storage bos client
	storageBosClient, err := bos.NewClient(
		config.DataStorageBosConfigGlobal.AccessKey,
		config.DataStorageBosConfigGlobal.SecretKey,
		config.DataStorageBosConfigGlobal.Endpoint)
	if err != nil {
		log.Panicf("init storage bos client failed: %s", err.Error())
	}
	resource.StorageBosClient = storageBosClient

}

func initBrcc(ctx context.Context) {
	brccConfPath := env.ConfDir() + "/business/brcc.toml"
	// 使用toml配置文件初始化BRCC客户端, name为配置文件路径
	err := rcc.StartWithConfFile(ctx, brccConfPath)
	if err != nil {
		log.Panicf("init brcc error: %s", err.Error())
	}
	rcc.Watch(func(ce *rcc.ChangeEvent) {
		// 建议defer捕获协程panic
		defer func() {
			if r := recover(); r != nil {
				log.Println("watch update callback panic")
			}
		}()

		for key, change := range ce.Changes {
			if change.ChangeType == rcc.ADD || change.ChangeType == rcc.MODIFY || change.ChangeType == rcc.DELETE {
				log.Println("changed item", key, change.NewValue, change.OldValue)
			}
		}
	})
}

// initTaskPollingService 初始化任务轮询服务
func initTaskPollingService(ctx context.Context) {
	// 配置轮询间隔，默认2秒
	config := background.TaskPollingConfig{
		PollInterval: 1 * time.Second,
	}

	// 初始化服务
	background.InitTaskPollingService(config)

	// 启动服务
	pollingService := background.GetTaskPollingService()
	if pollingService != nil {
		err := pollingService.Start()
		if err != nil {
			log.Panicf("init task polling service error: %s", err.Error())
		}

		// 注册关闭处理器
		gtask.BeforeShutdown(func() error {
			return pollingService.Stop()
		})

		log.Println("Task polling service initialized successfully")
	} else {
		log.Panicf("failed to get task polling service instance")
	}
}

// initSessionMonitoringService 初始化会话监控服务
func initSessionMonitoringService(ctx context.Context) {
	// 配置监控间隔（无状态模式，适用于K8s等动态部署）
	config := background.SessionMonitoringConfig{
		PollInterval: 2 * time.Second, // 轮询间隔2秒
	}

	// 初始化服务
	background.InitSessionMonitor(config)

	// 启动服务
	monitoringService := background.GetSessionMonitor()
	if monitoringService != nil {
		err := monitoringService.Start()
		if err != nil {
			log.Panicf("init session monitoring service error: %s", err.Error())
		}

		// 注册关闭处理器
		gtask.BeforeShutdown(func() error {
			return monitoringService.Stop()
		})

		log.Println("Session monitoring service initialized successfully")
	} else {
		log.Panicf("failed to get session monitoring service instance")
	}
}

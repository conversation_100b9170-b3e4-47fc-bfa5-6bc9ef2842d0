package background

import (
	"context"
	"fmt"
	"sync"
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// SessionMonitor 提供会话状态监控功能
// 监控运行中的会话，检测异常停止和超时情况
type SessionMonitor struct {
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	pollInterval time.Duration
	running      bool
}

type SessionMonitoringConfig struct {
	PollInterval time.Duration // 轮询间隔，默认2秒
}

var (
	sessionMonitor *SessionMonitor
)

// GetSessionMonitor 获取全局会话监控服务实例
func GetSessionMonitor() *SessionMonitor {
	return sessionMonitor
}

// InitSessionMonitor 初始化全局会话监控服务
func InitSessionMonitor(config SessionMonitoringConfig) {
	ctx, cancel := context.WithCancel(context.Background())

	sessionMonitor = &SessionMonitor{
		ctx:          ctx,
		cancel:       cancel,
		pollInterval: config.PollInterval,
		running:      false,
	}
}

// Start 启动后台监控协程
func (s *SessionMonitor) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("session monitor is already running")
	}

	s.running = true
	go s.monitorLoop()

	resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Session monitor started with interval %v", s.pollInterval))
	return nil
}

// Stop 停止监控服务
func (s *SessionMonitor) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	s.cancel()
	s.running = false

	resource.LoggerBg.Notice(s.ctx, "Session monitor stopped")
	return nil
}

// IsRunning 检查服务是否正在运行
func (s *SessionMonitor) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// monitorLoop 主监控循环
func (s *SessionMonitor) monitorLoop() {
	ticker := time.NewTicker(s.pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			resource.LoggerBg.Notice(s.ctx, "Session monitoring loop stopped")
			return
		case <-ticker.C:
			s.checkSessions()
		}
	}
}

// checkSessions 检查运行中的会话
// 监控 running、rewarding 和 stopping 状态的会话，pending 状态由 session_init 处理
func (s *SessionMonitor) checkSessions() {
	// 查询需要检查的会话：状态为running、rewarding或stopping且updated_at在10秒前
	sessions, err := s.getSessionsToCheck()
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to get sessions to check: %v", err))
		return
	}

	if len(sessions) == 0 {
		return
	}

	// 检查每个会话
	for _, session := range sessions {
		s.checkSingleSession(session)
	}
}

// getSessionsToCheck 获取需要检查的会话
// 查询状态为running、rewarding或stopping且updated_at在10秒前的会话
func (s *SessionMonitor) getSessionsToCheck() ([]*dao_session.ObjSession, error) {
	// 计算10秒前的时间
	tenSecondsAgo := time.Now().Add(-10 * time.Second)

	var sessions []*dao_session.ObjSession
	result := dao.GetDb(s.ctx).Model(&dao_session.ObjSession{}).
		Where("container_status IN (?) AND is_delete = ? AND (updated_at IS NULL OR updated_at < ?)",
			[]dao_session.ContainerStatus{dao_session.ContainerStatusRunning, dao_session.ContainerStatusStopping},
			false, tenSecondsAgo).
		Find(&sessions)

	if result.Error != nil {
		return nil, result.Error
	}

	return sessions, nil
}

// checkSingleSession 检查单个会话
func (s *SessionMonitor) checkSingleSession(session *dao_session.ObjSession) {
	// 1. 检查超时
	if s.isSessionTimeout(session) {
		s.handleTimeoutSession(session)
		return
	}

	// 2. 检查K8s任务状态（适用于所有状态）
	s.checkK8sTaskStatus(session)
	// 5. 更新updated_at字段，标记已检查
	s.updateSessionCheckedTime(session.SessionID)
}

// handleTimeoutSession 处理超时会话
func (s *SessionMonitor) handleTimeoutSession(session *dao_session.ObjSession) {
	errMsg := fmt.Sprintf("会话超时: 运行时间超过%d秒", session.TimeoutSeconds)

	err := dao_session.SessionBusinessIns.UpdateStatusWithError(s.ctx, session.SessionID,
		dao_session.ContainerStatusTimeout, errMsg)
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to update timeout session %d: %v",
			session.SessionID, err))
		return
	}

	resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Session %d marked as timeout: %s",
		session.SessionID, errMsg))

	// 尝试删除K8s任务（如果存在）
	if session.JobID != "" {
		s.cleanupK8sTask(session.JobID, session.SessionID)
	}
}

// checkK8sTaskStatus 检查K8s任务状态（适用于所有session状态）
func (s *SessionMonitor) checkK8sTaskStatus(session *dao_session.ObjSession) {
	if session.JobID == "" {
		// 没有JobID的session，根据当前状态决定处理方式
		switch session.ContainerStatus {
		case dao_session.ContainerStatusStopping:
			// stopping或rewarding状态但没有JobID，直接更新为stopped
			dao_session.SessionBusinessIns.UpdateMap(s.ctx, session.SessionID, map[string]any{
				"container_status": dao_session.ContainerStatusStopped,
			})
		case dao_session.ContainerStatusRunning:
			// running状态但没有JobID，标记为失败
			dao_session.SessionBusinessIns.UpdateStatusWithError(s.ctx, session.SessionID,
				dao_session.ContainerStatusFailed, "容器任务不存在")
		}
		return
	}
	taskState, err := rpc_k8s_proxy.K8sProxyClientIns.GetTaskState(s.ctx, session.JobID)
	if err != nil {
		// K8s API调用失败，记录警告但不更新会话状态
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to get task state for session %d (job %s): %v",
			session.SessionID, session.JobID, err))
		return
	}
	// 根据K8s任务状态更新会话状态
	s.handleK8sTaskStatus(session, taskState.State)
}

// handleK8sTaskStatus 根据K8s任务状态处理会话
func (s *SessionMonitor) handleK8sTaskStatus(session *dao_session.ObjSession, k8sState string) {
	var newStatus dao_session.ContainerStatus
	var errMsg string

	switch k8sState {
	case "failed":
		newStatus = dao_session.ContainerStatusFailed
		errMsg = "容器任务执行失败"
	case "success":
		// 任务成功完成，更新为stopped状态
		newStatus = dao_session.ContainerStatusStopped
		errMsg = "容器任务已完成"
	case "deleted":
		// 任务已被删除，更新为stopped状态
		newStatus = dao_session.ContainerStatusStopped
		errMsg = "容器任务已被删除"
	case "running":
		// K8s任务正在运行
		if session.ContainerStatus == dao_session.ContainerStatusStopping {
			// 如果session状态是stopping，但K8s任务还在运行，无需更新
			return
		}
		// 对于running状态的session，K8s任务也在运行，状态一致，无需更新
		return
	case "pending":
		// K8s任务还在等待，无需更新session状态
		return
	default:
		// 未知状态，记录警告但不更新
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Unknown k8s task state '%s' for session %d",
			k8sState, session.SessionID))
		return
	}

	// 更新会话状态
	err := dao_session.SessionBusinessIns.UpdateStatusWithError(s.ctx, session.SessionID, newStatus, errMsg)
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to update session %d status to %s: %v",
			session.SessionID, newStatus, err))
		return
	}
	// 日志记录
	err = dao_session.SessionBusinessIns.SessionLogRecord(s.ctx, session.SessionID, session.JobID)
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to record session %d log: %v",
			session.SessionID, err))
	}

	resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Session %d status updated from %s to %s (k8s: %s)",
		session.SessionID, session.ContainerStatus, newStatus, k8sState))
}

// updateSessionCheckedTime 更新会话的检查时间
func (s *SessionMonitor) updateSessionCheckedTime(sessionID int64) {
	err := dao_session.SessionBusinessIns.UpdateMap(s.ctx, sessionID, map[string]any{
		"updated_at": time.Now(),
	})
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to update session %d checked time: %v",
			sessionID, err))
	}
}

// isSessionTimeout 检查会话是否超时
func (s *SessionMonitor) isSessionTimeout(session *dao_session.ObjSession) bool {
	// 如果没有设置超时时间，则不检查超时
	if session.TimeoutSeconds <= 0 {
		return false
	}

	// 如果没有创建时间，无法判断超时
	if session.CreatedAt == nil {
		return false
	}

	// 计算是否超时, 用户设置超时时间+300秒
	timeoutDuration := time.Duration(session.TimeoutSeconds+300) * time.Second
	return time.Since(*session.CreatedAt) > timeoutDuration
}

// cleanupK8sTask 清理K8s任务
func (s *SessionMonitor) cleanupK8sTask(jobID string, sessionID int64) {
	err := rpc_k8s_proxy.K8sProxyClientIns.DeleteJob(s.ctx, jobID)
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to delete k8s job %s for session %d: %v",
			jobID, sessionID, err))
	} else {
		resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("K8s job %s deleted for session %d", jobID, sessionID))
	}
}

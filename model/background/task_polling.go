package background

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

type TaskDataProvider interface {
	SelectPendingTasks(ctx context.Context) ([]*dao_tool_call_task.ObjToolCallTask, error)
	SelectTimeoutTasks(ctx context.Context, timeoutDuration time.Duration) ([]*dao_tool_call_task.ObjToolCallTask, error)
	UpdateStatus(ctx context.Context, taskID int64, status dao_tool_call_task.ToolCallTaskStatus, errorMessage string) error
}

// TaskPollingService 提供待处理任务的长轮询功能
// 采用无状态设计，适用于多节点部署架构，数据库作为唯一真实状态源
type TaskPollingService struct {
	mu              sync.RWMutex
	waitingRequests map[int64][]chan *dao_tool_call_task.ObjToolCallTask
	ctx             context.Context
	cancel          context.CancelFunc
	pollInterval    time.Duration
	running         bool
	dataProvider    TaskDataProvider // 用于依赖注入的数据提供者
}

type TaskPollingConfig struct {
	PollInterval time.Duration
	DataProvider TaskDataProvider
}

var (
	taskPollingService *TaskPollingService
	taskPollingOnce    sync.Once
)

func NewTaskPollingService(config TaskPollingConfig) *TaskPollingService {
	ctx, cancel := context.WithCancel(context.Background())

	dataProvider := config.DataProvider
	if dataProvider == nil {
		dataProvider = dao_tool_call_task.ToolCallTaskBusinessIns
	}

	return &TaskPollingService{
		waitingRequests: make(map[int64][]chan *dao_tool_call_task.ObjToolCallTask),
		ctx:             ctx,
		cancel:          cancel,
		pollInterval:    config.PollInterval,
		running:         false,
		dataProvider:    dataProvider,
	}
}

// GetTaskPollingService returns the global task polling service instance
func GetTaskPollingService() *TaskPollingService {
	return taskPollingService
}

// InitTaskPollingService initializes the global task polling service
func InitTaskPollingService(config TaskPollingConfig) {
	taskPollingOnce.Do(func() {
		taskPollingService = NewTaskPollingService(config)
	})
}

// Start starts the background polling goroutine
func (s *TaskPollingService) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("task polling service is already running")
	}

	s.running = true
	go s.pollLoop()

	resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Task polling service started with interval %v", s.pollInterval))
	return nil
}

// Stop stops the polling service and cleans up resources
func (s *TaskPollingService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	s.running = false
	s.cancel()

	// Close all waiting channels
	for sessionID, ch := range s.waitingRequests {
		for _, ch := range ch {
			close(ch)
		}
		delete(s.waitingRequests, sessionID)
	}

	resource.LoggerBg.Notice(s.ctx, "Task polling service stopped")
	return nil
}

// WaitForTasks waits for tasks for a specific session with timeout
func (s *TaskPollingService) WaitForTask(sessionID int64, timeout time.Duration) (*dao_tool_call_task.ObjToolCallTask, error) {
	// Create a buffered channel to receive tasks
	taskChan := make(chan *dao_tool_call_task.ObjToolCallTask, 1)

	// Register the channel for this session
	s.registerWaitingRequest(sessionID, taskChan)

	// Ensure cleanup
	defer func() {
		s.unregisterWaitingRequest(sessionID, taskChan)
		close(taskChan)
	}()

	// Wait for tasks or timeout
	select {
	case task := <-taskChan:
		return task, nil
	case <-time.After(timeout):
		return nil, nil // Return empty slice on timeout
	case <-s.ctx.Done():
		return nil, fmt.Errorf("service is shutting down")
	}
}

// registerWaitingRequest registers a channel to wait for tasks for a specific session
func (s *TaskPollingService) registerWaitingRequest(sessionID int64, taskChan chan *dao_tool_call_task.ObjToolCallTask) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.waitingRequests[sessionID] == nil {
		s.waitingRequests[sessionID] = make([]chan *dao_tool_call_task.ObjToolCallTask, 0)
	}
	s.waitingRequests[sessionID] = append(s.waitingRequests[sessionID], taskChan)
}

// unregisterWaitingRequest removes a channel from the waiting list
func (s *TaskPollingService) unregisterWaitingRequest(sessionID int64, taskChan chan *dao_tool_call_task.ObjToolCallTask) {
	s.mu.Lock()
	defer s.mu.Unlock()

	channels := s.waitingRequests[sessionID]
	for i, ch := range channels {
		if ch == taskChan {
			// Remove the channel from the slice
			s.waitingRequests[sessionID] = append(channels[:i], channels[i+1:]...)
			break
		}
	}

	// Clean up empty session entries
	if len(s.waitingRequests[sessionID]) == 0 {
		delete(s.waitingRequests, sessionID)
	}

}

// pollLoop is the main background polling loop
func (s *TaskPollingService) pollLoop() {
	ticker := time.NewTicker(s.pollInterval)
	defer ticker.Stop()

	// Create a separate ticker for timeout cleanup (every 30 seconds)
	timeoutTicker := time.NewTicker(30 * time.Second)
	defer timeoutTicker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			if resource.LoggerBg != nil {
				resource.LoggerBg.Notice(s.ctx, "Task polling loop stopped")
			}
			return
		case <-ticker.C:
			s.pollAndNotify()
		case <-timeoutTicker.C:
			s.cleanupTimeoutTasks()
		}
	}
}

// pollAndNotify 轮询数据库并通知等待的请求
// 无状态设计：每次轮询都将数据库作为唯一真实状态源，不依赖本地状态比较
// 优化为单任务处理：每个会话只返回一个最早的待处理任务
func (s *TaskPollingService) pollAndNotify() {
	// Query all pending tasks using the data provider
	allTasks, err := s.dataProvider.SelectPendingTasks(s.ctx)
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to query pending tasks: %v", err))
		return
	}

	// 单任务处理：为每个会话选择最早的一个任务
	// 按创建时间排序，确保FIFO处理
	tasksBySession := make(map[int64]*dao_tool_call_task.ObjToolCallTask)
	for _, task := range allTasks {
		sessionID := task.SessionID
		existing := tasksBySession[sessionID]

		// 如果该会话还没有任务，或者当前任务更早创建，则使用当前任务
		if existing == nil || (task.CreatedAt != nil && existing.CreatedAt != nil && task.CreatedAt.Before(*existing.CreatedAt)) {
			tasksBySession[sessionID] = task
		}
	}

	// 无状态通知逻辑：只通知有任务的会话
	// 没有任务的会话让等待者继续等待直到超时
	s.mu.RLock()

	// 通知有任务的会话的等待者
	for sessionID, task := range tasksBySession {
		if len(s.waitingRequests[sessionID]) > 0 {
			s.notifyWaiters(sessionID, task)
			resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Notified session %d with task %s", sessionID, task.CallID))
		}
	}
	s.mu.RUnlock()
}

// cleanupTimeoutTasks 清理超时任务，将运行时间过长的任务标记为超时
func (s *TaskPollingService) cleanupTimeoutTasks() {
	// 设置任务超时时间为5分钟
	const taskTimeout = 5 * time.Minute

	// 查询超时任务
	timeoutTasks, err := s.dataProvider.SelectTimeoutTasks(s.ctx, taskTimeout)
	if err != nil {
		resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to query timeout tasks: %v", err))
		return
	}

	if len(timeoutTasks) == 0 {
		return
	}

	if resource.LoggerBg != nil {
		resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Found %d timeout tasks, marking as timeout", len(timeoutTasks)))
	}

	// 将超时任务标记为超时状态
	for _, task := range timeoutTasks {
		if err := s.dataProvider.UpdateStatus(s.ctx, task.TaskID, dao_tool_call_task.ToolCallTaskStatusTimeout, "Task execution timeout"); err != nil {
			resource.LoggerBg.Warning(s.ctx, fmt.Sprintf("Failed to mark task %s as timeout: %v", task.CallID, err))
		} else {
			// Record tool call timeout metrics
			metrics_helper.RecordToolCallTimeout()
			resource.LoggerBg.Notice(s.ctx, fmt.Sprintf("Marked task %s as timeout after running for more than %v", task.CallID, taskTimeout))
		}
	}
}

// notifyWaiters notifies all waiting requests for a specific session
func (s *TaskPollingService) notifyWaiters(sessionID int64, task *dao_tool_call_task.ObjToolCallTask) {
	waiters := s.waitingRequests[sessionID]
	if len(waiters) == 0 {
		return
	}
	// 通知所有等待者（他们将处理自己的清理）
	for _, waiter := range waiters {
		select {
		case waiter <- task:
			// Successfully notified
		default:
			// Channel is full or closed, skip
			log.Println("Failed to notify waiter for session", sessionID)
		}
	}
}

package background_test

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/background"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

var setupOnce sync.Once

func setup() {
	setupOnce.Do(func() {
		bootstrap.MustInitTest(context.Background())
	})
}

// MockTaskDataProvider is a mock implementation for testing
type MockTaskDataProvider struct {
	mock.Mock
}

func (m *MockTaskDataProvider) SelectPendingTasks(ctx context.Context) ([]*dao_tool_call_task.ObjToolCallTask, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*dao_tool_call_task.ObjToolCallTask), args.Error(1)
}

func (m *MockTaskDataProvider) SelectTimeoutTasks(ctx context.Context, timeoutDuration time.Duration) ([]*dao_tool_call_task.ObjToolCallTask, error) {
	args := m.Called(ctx, timeoutDuration)
	return args.Get(0).([]*dao_tool_call_task.ObjToolCallTask), args.Error(1)
}

func (m *MockTaskDataProvider) UpdateStatus(ctx context.Context, taskID int64, status dao_tool_call_task.ToolCallTaskStatus, errorMessage string) error {
	args := m.Called(ctx, taskID, status, errorMessage)
	return args.Error(0)
}

func TestTaskPollingService_BasicFunctionality(t *testing.T) {
	setup()
	// Create a mock data provider
	mockProvider := &MockTaskDataProvider{}
	mockProvider.On("SelectPendingTasks", mock.Anything).Return([]*dao_tool_call_task.ObjToolCallTask{}, nil)

	// Create a test service
	config := background.TaskPollingConfig{
		PollInterval: 100 * time.Millisecond, // Fast polling for testing
		DataProvider: mockProvider,
	}
	service := background.NewTaskPollingService(config)

	// Start the service
	err := service.Start()
	assert.NoError(t, err)
	defer service.Stop()

}

func TestTaskPollingService_WaitForTasks_Timeout(t *testing.T) {
	setup()
	// Create a mock data provider
	mockProvider := &MockTaskDataProvider{}
	mockProvider.On("SelectPendingTasks", mock.Anything).Return([]*dao_tool_call_task.ObjToolCallTask{}, nil)

	// Create a test service with slow polling
	config := background.TaskPollingConfig{
		PollInterval: 1 * time.Second,
		DataProvider: mockProvider,
	}
	service := background.NewTaskPollingService(config)

	// Start the service
	err := service.Start()
	assert.NoError(t, err)
	defer service.Stop()

	// Wait for tasks with short timeout
	start := time.Now()
	task, err := service.WaitForTask(1, 200*time.Millisecond)
	duration := time.Since(start)

	// Should timeout and return nil task
	assert.NoError(t, err)
	assert.Nil(t, task)
	assert.True(t, duration >= 200*time.Millisecond)
	assert.True(t, duration < 300*time.Millisecond) // Should not wait much longer
}

func TestTaskPollingService_ConcurrentRequests(t *testing.T) {
	setup()
	// Create a mock data provider
	mockProvider := &MockTaskDataProvider{}
	mockProvider.On("SelectPendingTasks", mock.Anything).Return([]*dao_tool_call_task.ObjToolCallTask{}, nil)

	// Create a test service
	config := background.TaskPollingConfig{
		PollInterval: 50 * time.Millisecond,
		DataProvider: mockProvider,
	}
	service := background.NewTaskPollingService(config)

	// Start the service
	err := service.Start()
	assert.NoError(t, err)
	defer service.Stop()

	// Start multiple concurrent requests
	const numRequests = 5
	var wg sync.WaitGroup
	results := make([]*dao_tool_call_task.ObjToolCallTask, numRequests)

	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			task, err := service.WaitForTask(1, 100*time.Millisecond)
			assert.NoError(t, err)
			results[index] = task
		}(i)
	}

	// Wait for all requests to complete
	wg.Wait()

	// All should have timed out with nil results
	for i := 0; i < numRequests; i++ {
		assert.Nil(t, results[i])
	}
}

func TestTaskPollingService_GlobalInstance(t *testing.T) {
	setup()
	// Test that global instance works
	config := background.TaskPollingConfig{
		PollInterval: 100 * time.Millisecond,
	}

	// Initialize global instance
	background.InitTaskPollingService(config)

	// Get the instance
	service := background.GetTaskPollingService()
	assert.NotNil(t, service)

	// Should return the same instance on subsequent calls
	service2 := background.GetTaskPollingService()
	assert.Equal(t, service, service2)
}

package session

import (
	"context"
	"errors"
	"fmt"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// SessionExecInputData Session命令执行输入数据
type SessionExecInputData struct {
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
	Command     string `json:"command" validate:"required,min=1"`
}

// SessionExecOutputData Session命令执行输出数据
type SessionExecOutputData struct {
	SessionID int64  `json:"session_id"`
	Stdout    string `json:"stdout"`
	Stderr    string `json:"stderr"`
}

// SessionExec Session命令执行服务
type SessionExec struct {
	base.Service[SessionExecInputData, SessionExecOutputData]
}

// Execute 执行Session命令执行逻辑
func (s *SessionExec) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	var session *dao_session.ObjSession

	// 1. 根据输入参数获取Session
	if input.SessionCode != "" {
		session, err = dao_session.SessionBusinessIns.GetSessionByCode(ctx, input.SessionCode)
		if err != nil {
			return nil, nil, err
		}
	} else if input.SessionID != 0 {
		session, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
		if err != nil {
			return nil, nil, err
		}
	} else {
		return nil, nil, errcode.NewCustomErr(ctx, errors.New("session_id或session_code必须提供一个"), errcode.RequiredFieldMissing)
	}

	sessionID := session.SessionID

	// 2. 检查Session状态
	if session.ContainerStatus != dao_session.ContainerStatusRunning {
		return nil, nil, errcode.NewCustomErr(ctx,
			fmt.Errorf("Session状态不是运行中，当前状态: %s", session.ContainerStatus), errcode.SessionExecFailed)
	}

	// 3. 检查Session是否有关联的容器任务
	if session.JobID == "" {
		return nil, nil, errcode.NewCustomErr(ctx, errors.New("Session没有关联的容器任务"), errcode.SessionNotFound)
	}

	// 4. 调用k8s_proxy执行命令
	execReq := &rpc_k8s_proxy.ExecCommandRequest{
		SourceType: "mcp",
		Command:    []string{"sh", "-c", input.Command},
		JobName:    session.JobID,
	}

	execResp, err := rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, execReq)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.K8sExecCommandError)
	}

	// 5. 返回执行结果
	output := &SessionExecOutputData{
		SessionID: sessionID,
		Stdout:    execResp.Stdout,
		Stderr:    execResp.Stderr,
	}

	return output, nil, nil
}

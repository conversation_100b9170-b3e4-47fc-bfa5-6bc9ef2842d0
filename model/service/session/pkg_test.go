package session_test

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/session"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	rcc "icode.baidu.com/baidu/smartprogram/rcc2-go-sdk"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao_base.OrmLazyInit()
}

func tearDownAll(t *testing.T) {}

// ===== SessionInit Real Flow Tests =====

// 准备测试数据
func prepareTestData(t *testing.T) (serverID int64, envID int64) {
	// 创建测试用的MCP服务器
	serverArgs := dao_base.JSONArray[string]{"--stdio"}
	server := &dao_register_mcp_server.ObjRegisterMcpServer{
		ServerName:       "test_filesystem_server",
		Command:          "npx",
		Args:             serverArgs,
		Description:      "测试文件系统MCP服务器",
		ServerCodeBosUrl: "https://bos.example.com/mcp-servers/filesystem.zip",
	}

	serverID, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, server)
	assert.NoError(t, err)
	assert.Greater(t, serverID, int64(0))

	// 创建测试用的环境
	envDependency := dao_base.JSONData{
		"python_version": "3.9",
		"requirements":   []string{"pandas", "numpy"},
	}
	env := &dao_mcp_env.ObjMcpEnv{
		EnvMd5:        "test_env_md5_123",
		Name:          "test_env",
		Description:   "测试环境",
		BosURL:        "https://bos.example.com/envs/test.tar.gz",
		EnvDependency: envDependency,
	}

	envID, err = dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
	assert.NoError(t, err)
	assert.Greater(t, envID, int64(0))

	return serverID, envID
}

func TestSessionInit_Execute_Success_00_WithMocks(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{1, 2},
		EnvID:          100,
		TimeoutMinutes: 1,
	}

	// 创建 gomonkey patches
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// ===== Mock RCC 配置读取 =====
	patches.ApplyFunc(rcc.GetValue, func(key string, defaultValue string) string {
		switch key {
		case "k8s_proxy.init.home_path":
			return "/home/<USER>"
		case "k8s_proxy.image_registry":
			return "ccr-2y3xupwh-vpc.cnc.bj.baidubce.com"
		case "k8s_proxy.image_path":
			return "mcp/mcp-runtime:latest"
		case "k8s_proxy.mcp_api_base":
			return "http://127.0.0.1:8080"
		case "k8s_proxy.init.source_type":
			return "mcp"
		case "k8s_proxy.init.task_type":
			return "mcp"
		case "k8s_proxy.init.resources":
			return "1"
		case "k8s_proxy.init.timeout":
			return "60"
		default:
			return defaultValue
		}
	})

	// ===== Mock BOS 配置 =====
	patches.ApplyGlobalVar(&config.DataStorageBosConfigGlobal, &config.BosConfig{
		AccessKey: "test-access-key",
		SecretKey: "test-secret-key",
		Endpoint:  "https://bos.test.com",
		Bucket:    "test-bucket",
	})

	// ===== Mock 数据库查询 =====

	// Mock MCP服务器查询
	mockServersMap := map[int64]*dao_register_mcp_server.ObjRegisterMcpServer{
		1: {
			ServerID:         1,
			ServerName:       "test_filesystem_server",
			Command:          "npx",
			Args:             dao_base.JSONArray[string]{"--stdio"},
			Description:      "测试文件系统MCP服务器",
			ServerCodeBosUrl: "https://bos.example.com/mcp-servers/filesystem.zip",
		},
		2: {
			ServerID:         2,
			ServerName:       "test_database_server",
			Command:          "python",
			Args:             dao_base.JSONArray[string]{"-m", "db_server"},
			Description:      "测试数据库MCP服务器",
			ServerCodeBosUrl: "https://bos.example.com/mcp-servers/database.zip",
		},
	}

	patches.ApplyMethod(reflect.TypeOf(dao_register_mcp_server.RegisterMcpServerBusinessIns), "GetMapByPrimaryKeys",
		func(_ dao_register_mcp_server.RegisterMcpServerBusiness, ctx context.Context, ids []int64) (map[int64]*dao_register_mcp_server.ObjRegisterMcpServer, error) {
			return mockServersMap, nil
		})

	// Mock 环境查询
	mockEnv := &dao_mcp_env.ObjMcpEnv{
		EnvID:       100,
		EnvMd5:      "test_env_md5_123",
		Name:        "test_env",
		Description: "测试环境",
		BosURL:      "https://bos.example.com/envs/test.tar.gz",
		EnvDependency: dao_base.JSONData{
			"python_version": "3.9",
			"requirements":   []string{"pandas", "numpy"},
		},
	}

	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 100 {
				return mockEnv, nil
			}
			return nil, nil
		})

	// Mock Session插入
	patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "Insert",
		func(_ dao_session.SessionBusiness, ctx context.Context, obj *dao_session.ObjSession) (int64, error) {
			// 验证插入的Session数据
			assert.Equal(t, int64(100), obj.EnvID)
			assert.NotNil(t, obj.ServerIds)
			assert.Equal(t, dao_session.ContainerStatusInit, obj.ContainerStatus)
			assert.Nil(t, obj.McpTools) // 初始化时应该为空

			return 1001, nil // 返回新的SessionID
		})

	// Mock Session状态更新
	patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "UpdateJobIDAndStatus",
		func(_ dao_session.SessionBusiness, ctx context.Context, sessionID int64, jobID string, status dao_session.ContainerStatus) error {
			assert.Equal(t, int64(1001), sessionID)
			assert.Equal(t, "test-task-12345", jobID)
			assert.Equal(t, dao_session.ContainerStatusPending, status)
			return nil
		})

	// ===== Mock k8s_proxy API 调用 (通过 RAL) =====

	// Mock RAL 调用来拦截 k8s_proxy 的所有 API 调用
	callCount := 0
	patches.ApplyFunc(ral.RAL, func(ctx context.Context, name any, req ral.Request, resp ral.Response, opts ...ral.ROption) error {
		// 类型断言获取具体的请求和响应对象
		ralReq, ok := req.(*ghttp.RalRequest)
		if !ok {
			return assert.AnError
		}

		ralResp, ok := resp.(*ghttp.RalResponse)
		if !ok {
			return assert.AnError
		}

		// 根据 API 名称处理不同的调用
		switch ralReq.APIName {
		case "CREATE_TASK":
			// Mock 创建任务成功
			mockResp := &rpc_k8s_proxy.K8sProxyResp[rpc_k8s_proxy.CreateTaskResponse]{
				Code: 0,
				Msg:  "success",
				Data: rpc_k8s_proxy.CreateTaskResponse{
					TaskID: "test-task-12345",
				},
			}
			if ralResp.Data != nil {
				respData := ralResp.Data.(*rpc_k8s_proxy.K8sProxyResp[rpc_k8s_proxy.CreateTaskResponse])
				*respData = *mockResp
			}

		case "GET_TASK_STATE":
			callCount++
			// Mock 查询任务状态 - 第一次返回 running
			mockResp := &rpc_k8s_proxy.K8sProxyResp[rpc_k8s_proxy.TaskStateResponse]{
				Code: 0,
				Msg:  "success",
				Data: rpc_k8s_proxy.TaskStateResponse{
					State: "running",
				},
			}
			if ralResp.Data != nil {
				respData := ralResp.Data.(*rpc_k8s_proxy.K8sProxyResp[rpc_k8s_proxy.TaskStateResponse])
				*respData = *mockResp
			}

		default:
			return assert.AnError
		}

		return nil
	})

	// Mock Session查询（轮询时使用）- 模拟容器已就绪
	patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "SelectByPrimaryKey",
		func(_ dao_session.SessionBusiness, ctx context.Context, id int64) (*dao_session.ObjSession, error) {
			if id == 1001 {
				session := &dao_session.ObjSession{
					SessionID:       1001,
					JobID:           "test-task-12345",
					EnvID:           100,
					ContainerStatus: dao_session.ContainerStatusRunning, // 容器已运行
					McpTools: dao_base.JSONData{
						"tools": []interface{}{
							map[string]interface{}{
								"name":        "filesystem",
								"description": "文件系统操作工具",
								"parameters":  map[string]interface{}{"type": "object"},
							},
							map[string]interface{}{
								"name":        "database",
								"description": "数据库操作工具",
								"parameters":  map[string]interface{}{"type": "object"},
							},
						},
					},
				}
				return session, nil
			}
			return nil, nil
		})

	// 执行测试
	resp, _, err := ins.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*session.SessionInitOutputData)
	assert.Equal(t, int64(1001), output.SessionID)
	assert.Equal(t, 2, len(output.MCPTools))

	// 验证工具列表
	toolNames := make([]string, len(output.MCPTools))
	for i, tool := range output.MCPTools {
		toolNames[i] = tool.Name
	}
	assert.Contains(t, toolNames, "filesystem")
	assert.Contains(t, toolNames, "database")

	t.Logf("✅ SessionInit执行成功，SessionID=%d，工具数量=%d", output.SessionID, len(output.MCPTools))
}

func TestSessionInit_Execute_ServerNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据 - 使用不存在的服务器ID
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{999999}, // 不存在的服务器ID
		EnvID:          1,
		TimeoutMinutes: 1,
	}

	// 执行测试
	resp, _, err := ins.Execute(ctx, nil)

	// 验证输出
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "MCP服务器不存在")
}

func TestSessionInit_Execute_EnvNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	serverID, _ := prepareTestData(t)

	// 准备测试数据 - 使用不存在的环境ID
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{serverID},
		EnvID:          999999, // 不存在的环境ID
		TimeoutMinutes: 1,
	}

	// 执行测试
	resp, _, err := ins.Execute(ctx, nil)

	// 验证输出
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "Environment not found")
}

func TestSessionInit_Execute_EmptyServerIDs(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	_, envID := prepareTestData(t)

	// 准备测试数据 - ServerIDs为空
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{}, // 空的服务器ID列表
		EnvID:          envID,
		TimeoutMinutes: 1,
	}

	// 执行测试 - 这里只测试验证逻辑，不测试完整的容器启动流程
	// 由于容器启动需要真实的环境，我们只验证到数据库记录创建为止

	// 验证不会因为空的ServerIDs而报错
	// 这里会在容器启动阶段失败，但我们主要验证前面的验证逻辑不会报错
	_, _, err := ins.Execute(ctx, nil)

	// 验证不是因为ServerIDs验证失败
	if err != nil {
		assert.NotContains(t, err.Error(), "MCP服务器不存在")
	}
}

func TestSessionInit_Execute_ZeroEnvID(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	serverID, _ := prepareTestData(t)

	// 准备测试数据 - EnvID为0
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{serverID},
		EnvID:          0, // 环境ID为0
		TimeoutMinutes: 1,
	}

	// 执行测试 - 这里只测试验证逻辑，不测试完整的容器启动流程
	// 验证不会因为EnvID为0而报错
	_, _, err := ins.Execute(ctx, nil)

	// 验证不是因为EnvID验证失败
	if err != nil {
		assert.NotContains(t, err.Error(), "MCP环境不存在")
	}
}

func TestSessionInit_Execute_EmptyServerIDsAndZeroEnvID(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据 - ServerIDs为空且EnvID为0
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{}, // 空的服务器ID列表
		EnvID:          0,         // 环境ID为0
		TimeoutMinutes: 1,
	}

	// 执行测试 - 这里只测试验证逻辑，不测试完整的容器启动流程
	// 验证不会因为空的ServerIDs和EnvID为0而报错
	_, _, err := ins.Execute(ctx, nil)

	// 验证不是因为ServerIDs或EnvID验证失败
	if err != nil {
		assert.NotContains(t, err.Error(), "MCP服务器不存在")
		assert.NotContains(t, err.Error(), "MCP环境不存在")
	}
}

func TestSessionInit_Execute_ContainerTimeout(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	serverID, envID := prepareTestData(t)

	// 准备测试数据
	ins := &session.SessionInit{}
	ins.InputData = &session.SessionInitInputData{
		ServerIDs:      []int64{serverID},
		EnvID:          envID,
		TimeoutMinutes: 1,
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock k8s-proxy创建任务成功
	patches.ApplyFunc(rpc_k8s_proxy.K8sProxyClientIns.CreateTask, func(ctx context.Context, req *rpc_k8s_proxy.CreateTaskRequest) (*rpc_k8s_proxy.CreateTaskResponse, error) {
		return &rpc_k8s_proxy.CreateTaskResponse{
			TaskID: "test-task-123",
		}, nil
	})

	// Mock k8s-proxy状态查询，持续返回Pending状态模拟超时
	patches.ApplyFunc(rpc_k8s_proxy.K8sProxyClientIns.GetTaskState, func(ctx context.Context, taskID string) (*rpc_k8s_proxy.TaskStateResponse, error) {
		return &rpc_k8s_proxy.TaskStateResponse{
			State: "Pending", // 持续返回Pending，触发超时
		}, nil
	})

	// 执行测试
	resp, _, err := ins.Execute(ctx, nil)

	// 验证输出
	assert.Error(t, err)
	assert.Nil(t, resp)
}

func TestFileDownload(t *testing.T) {
	ctx, _ := context.WithTimeout(context.Background(), 2*time.Minute)
	defer os.RemoveAll("./downloads")

	url := "https://mcp-env.bj.bcebos.com/tmp/file_editor?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-07-25T12%3A09%3A13Z%2F-1%2Fhost%2F84a96b37100985243ad1f1cc0c7953bec4a18ad31c6f334b86f8a9b37a469907"
	dest := "./downloads"

	path, err := DownloadByURL(ctx, url, dest)
	if err != nil {
		panic(err)
	}
	fmt.Println("saved to:", path)
}

func DownloadByURL(ctx context.Context, rawURL, destDir string) (string, error) {
	if rawURL == "" {
		return "", errors.New("empty url")
	}

	u, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("parse url: %w", err)
	}

	// 取不含 query 的纯 Path
	filename := strings.TrimSpace(path.Base(u.Path))
	if filename == "" || filename == "." || filename == "/" {
		filename = "download" // 兜底名
	}

	// 可选：再做一次简单的文件名清理（去掉可能的非法字符）
	filename = sanitizeFilename(filename)

	// 确保目录存在
	if err := os.MkdirAll(destDir, 0o755); err != nil {
		return "", fmt.Errorf("mkdir %s: %w", destDir, err)
	}

	dstPath := filepath.Join(destDir, filename)

	// 如果不想覆盖已存在的文件，可以启用下面这段逻辑自动加后缀
	// dstPath, err = avoidOverwrite(dstPath)
	// if err != nil {
	// 	return "", err
	// }

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, rawURL, nil)
	if err != nil {
		return "", fmt.Errorf("new request: %w", err)
	}

	cli := &http.Client{
		// 不在这里固定 Timeout，而是交给 ctx 控制；如果你想兜底也可以设一个较大的：
		Timeout: 0,
	}

	resp, err := cli.Do(req)
	if err != nil {
		return "", fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		return "", fmt.Errorf("bad status code: %d %s", resp.StatusCode, resp.Status)
	}

	f, err := os.Create(dstPath)
	if err != nil {
		return "", fmt.Errorf("create file: %w", err)
	}
	defer func() {
		_ = f.Close()
	}()

	if _, err := io.Copy(f, resp.Body); err != nil {
		return "", fmt.Errorf("write file: %w", err)
	}

	return dstPath, nil
}

// sanitizeFilename 做一个很轻量的文件名字符过滤，避免在不同平台上出问题。
func sanitizeFilename(name string) string {
	// 这里简单替换常见非法字符；根据需要可更严格
	illegal := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, ch := range illegal {
		name = strings.ReplaceAll(name, ch, "_")
	}
	return name
}

// avoidOverwrite 如果目标文件已存在，自动在尾部追加 (-1), (-2), ... 避免覆盖。
func avoidOverwrite(dstPath string) (string, error) {
	if _, err := os.Stat(dstPath); errors.Is(err, os.ErrNotExist) {
		return dstPath, nil
	} else if err != nil {
		return "", fmt.Errorf("stat %s: %w", dstPath, err)
	}

	ext := filepath.Ext(dstPath)
	base := strings.TrimSuffix(filepath.Base(dstPath), ext)
	dir := filepath.Dir(dstPath)

	for i := 1; ; i++ {
		candidate := filepath.Join(dir, fmt.Sprintf("%s-%d%s", base, i, ext))
		if _, err := os.Stat(candidate); errors.Is(err, os.ErrNotExist) {
			return candidate, nil
		} else if err != nil {
			return "", fmt.Errorf("stat %s: %w", candidate, err)
		}
	}
}

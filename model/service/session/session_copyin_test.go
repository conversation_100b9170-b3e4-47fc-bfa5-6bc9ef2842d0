package session_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/session"
)

func TestSessionCopyin_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "job_test_session_copyin_001",
		SessionCode:     fmt.Sprintf("test_session_copyin_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusRunning,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 使用gomonkey模拟K8s客户端的UploadFile方法
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyMethod(&rpc_k8s_proxy.K8sProxyClient{}, "UploadFile",
		func(_ *rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *rpc_k8s_proxy.UploadFileRequest) (*rpc_k8s_proxy.UploadFileResponse, error) {
			// 验证请求参数
			assert.Equal(t, "mcp", req.SourceType)
			assert.Equal(t, "job_test_session_copyin_001", req.JobName)
			assert.Len(t, req.FileInfo, 2)
			assert.Equal(t, "https://example.com/file1.txt", req.FileInfo[0].FileURL)
			assert.Equal(t, "/tmp/file1.txt", req.FileInfo[0].DestPath)
			assert.Equal(t, "https://example.com/file2.txt", req.FileInfo[1].FileURL)
			assert.Equal(t, "/tmp/file2.txt", req.FileInfo[1].DestPath)

			return &rpc_k8s_proxy.UploadFileResponse{
				Success: true,
			}, nil
		})

	// 执行文件复制操作
	copyinService := &service.SessionCopyin{}
	copyinService.InputData = &service.SessionCopyinInputData{
		SessionID: sessionID,
		Files: []service.FileInfo{
			{
				FileURL:  "https://example.com/file1.txt",
				DestPath: "/tmp/file1.txt",
			},
			{
				FileURL:  "https://example.com/file2.txt",
				DestPath: "/tmp/file2.txt",
			},
		},
	}

	resp, _, err := copyinService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.SessionCopyinOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.True(t, output.Success)
	assert.Contains(t, output.Message, "成功上传 2 个文件")
}

func TestSessionCopyin_Execute_SessionNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建服务实例（使用不存在的SessionID）
	copyinService := &service.SessionCopyin{}
	copyinService.InputData = &service.SessionCopyinInputData{
		SessionID: 99999, // 不存在的Session ID
		Files: []service.FileInfo{
			{
				FileURL:  "https://example.com/file1.txt",
				DestPath: "/tmp/file1.txt",
			},
		},
	}

	// 执行测试
	resp, _, err := copyinService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)

	// 验证错误类型
	isCustomErr, code, _ := errcode.AssertCustomError(err, errcode.SessionNotFound, "")
	assert.True(t, isCustomErr)
	assert.Equal(t, errcode.SessionNotFound, code)
}

func TestSessionCopyin_Execute_SessionNotRunning(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session（状态为停止）
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		JobID:           "job_test_session_copyin_stopped",
		SessionCode:     fmt.Sprintf("test_session_copyin_stopped_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 创建服务实例
	copyinService := &service.SessionCopyin{}
	copyinService.InputData = &service.SessionCopyinInputData{
		SessionID: sessionID,
		Files: []service.FileInfo{
			{
				FileURL:  "https://example.com/file1.txt",
				DestPath: "/tmp/file1.txt",
			},
		},
	}

	// 执行测试
	resp, _, err := copyinService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)

	// 验证错误类型
	isCustomErr, code, _ := errcode.AssertCustomError(err, errcode.SessionExecFailed, "")
	assert.True(t, isCustomErr)
	assert.Equal(t, errcode.SessionExecFailed, code)
}

func TestSessionCopyin_Execute_InvalidInput(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建服务实例（没有提供SessionID或SessionCode）
	copyinService := &service.SessionCopyin{}
	copyinService.InputData = &service.SessionCopyinInputData{
		Files: []service.FileInfo{
			{
				FileURL:  "https://example.com/file1.txt",
				DestPath: "/tmp/file1.txt",
			},
		},
	}

	// 执行测试
	resp, _, err := copyinService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)

	// 验证错误类型
	isCustomErr, code, _ := errcode.AssertCustomError(err, errcode.RequiredFieldMissing, "")
	assert.True(t, isCustomErr)
	assert.Equal(t, errcode.RequiredFieldMissing, code)
}

func TestSessionCopyin_Execute_UploadFailed(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		JobID:           "job_test_session_copyin_failed",
		SessionCode:     fmt.Sprintf("test_session_copyin_failed_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusRunning,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 使用gomonkey模拟K8s客户端的UploadFile方法返回失败
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyMethod(&rpc_k8s_proxy.K8sProxyClient{}, "UploadFile",
		func(_ *rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *rpc_k8s_proxy.UploadFileRequest) (*rpc_k8s_proxy.UploadFileResponse, error) {
			return &rpc_k8s_proxy.UploadFileResponse{
				Success: false,
			}, nil
		})

	// 执行文件复制操作
	copyinService := &service.SessionCopyin{}
	copyinService.InputData = &service.SessionCopyinInputData{
		SessionID: sessionID,
		Files: []service.FileInfo{
			{
				FileURL:  "https://example.com/file1.txt",
				DestPath: "/tmp/file1.txt",
			},
		},
	}

	resp, _, err := copyinService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.SessionCopyinOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.False(t, output.Success)
	assert.Equal(t, "文件上传失败", output.Message)
}

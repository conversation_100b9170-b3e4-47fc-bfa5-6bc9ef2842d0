package intern_test

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/intern"
)

func TestToolComplete_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建一个运行中的任务
	callID := "test_call_success_" + uuid.New().String()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    callID,
		SessionID: 1,
		ToolName:  "filesystem__read_file",
		Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
	}

	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	assert.NoError(t, err)

	// 准备测试数据
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID: 1,
		CallID:    callID,
		Status:    "success",
		Result:    `{"content": "file content", "encoding": "utf-8"}`,
		OldEnvMD5: "old_md5_123",
		NewEnvMD5: "new_md5_456",
		OldEnvURL: "http://old.env.url",
		NewEnvURL: "http://new.env.url",
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.True(t, output.Success)
	assert.Equal(t, callID, output.CallID)
	assert.Contains(t, output.Message, "任务完成更新成功")

	// 验证数据库中的记录已更新
	updatedTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, taskID)
	assert.NoError(t, err)
	assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusSuccess, updatedTask.Status)
	assert.Equal(t, `{"content": "file content", "encoding": "utf-8"}`, updatedTask.Result)
	assert.Equal(t, "old_md5_123", updatedTask.OldEnvMd5)
	assert.Equal(t, "new_md5_456", updatedTask.NewEnvMd5)
	assert.Equal(t, "http://old.env.url", updatedTask.OldEnvUrl)
	assert.Equal(t, "http://new.env.url", updatedTask.NewEnvUrl)
	assert.NotNil(t, updatedTask.CompletedAt)
	assert.Empty(t, updatedTask.ErrorMessage) // Success case should have no error message
}

func TestToolComplete_Execute_Failed_WithErrorMessage(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建一个运行中的任务
	callID := "test_call_failed_" + uuid.New().String()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    callID,
		SessionID: 1,
		ToolName:  "filesystem__read_file",
		Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
	}

	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	assert.NoError(t, err)

	// 准备测试数据 - 失败的任务带错误信息
	errorMessage := "tool not found: filesystem__read_file"
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID:    1,
		CallID:       callID,
		Status:       "failed",
		Result:       "",
		OldEnvMD5:    "old_md5_789",
		NewEnvMD5:    "old_md5_789", // Same as old since task failed
		OldEnvURL:    "http://old.env.url",
		NewEnvURL:    "http://old.env.url", // Same as old since task failed
		ErrorMessage: errorMessage,
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.True(t, output.Success)
	assert.Equal(t, callID, output.CallID)
	assert.Contains(t, output.Message, "任务完成更新成功")

	// 验证数据库中的记录已更新，特别是错误信息
	updatedTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, taskID)
	assert.NoError(t, err)
	assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusFailed, updatedTask.Status)
	assert.Empty(t, updatedTask.Result) // Failed task should have empty result
	assert.Equal(t, "old_md5_789", updatedTask.OldEnvMd5)
	assert.Equal(t, "old_md5_789", updatedTask.NewEnvMd5)
	assert.Equal(t, "http://old.env.url", updatedTask.OldEnvUrl)
	assert.Equal(t, "http://old.env.url", updatedTask.NewEnvUrl)
	assert.NotNil(t, updatedTask.CompletedAt)

	// 这是关键测试：验证错误信息被正确存储
	assert.Equal(t, errorMessage, updatedTask.ErrorMessage)
}

func TestToolComplete_Execute_Failed_WithoutErrorMessage(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建一个运行中的任务
	callID := "test_call_failed_no_error_" + uuid.New().String()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    callID,
		SessionID: 1,
		ToolName:  "filesystem__read_file",
		Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
	}

	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	assert.NoError(t, err)

	// 准备测试数据 - 失败的任务但没有错误信息
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID:    1,
		CallID:       callID,
		Status:       "failed",
		Result:       "",
		OldEnvMD5:    "old_md5_abc",
		NewEnvMD5:    "old_md5_abc",
		OldEnvURL:    "http://old.env.url",
		NewEnvURL:    "http://old.env.url",
		ErrorMessage: "", // Empty error message
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.True(t, output.Success)
	assert.Equal(t, callID, output.CallID)

	// 验证数据库中的记录已更新
	updatedTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, taskID)
	assert.NoError(t, err)
	assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusFailed, updatedTask.Status)
	assert.Empty(t, updatedTask.ErrorMessage) // Should be empty when no error message provided
}

func TestToolComplete_Execute_InvalidStatus(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据 - 无效的状态
	callID := "test_call_invalid_" + uuid.New().String()
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID: 1,
		CallID:    callID,
		Status:    "invalid_status", // Invalid status
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.False(t, output.Success)
	assert.Equal(t, callID, output.CallID)
	assert.Contains(t, output.Message, "无效的状态值")
}

func TestToolComplete_Execute_TaskNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据 - 不存在的任务
	callID := "non_existent_call_" + uuid.New().String()
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID: 1,
		CallID:    callID,
		Status:    "success",
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.False(t, output.Success)
	assert.Equal(t, callID, output.CallID)
	assert.Contains(t, output.Message, "任务不存在")
}

func TestToolComplete_Execute_SessionIDMismatch(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)
	sessionID := time.Now().UnixMilli()
	// 创建一个运行中的任务
	callID := "test_call_session_mismatch_" + uuid.New().String()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    callID,
		SessionID: sessionID,
		ToolName:  "filesystem__read_file",
		Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
	}

	_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	assert.NoError(t, err)

	// 准备测试数据 - 使用不同的Session ID
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID: sessionID + 1,
		CallID:    callID,
		Status:    "success",
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.False(t, output.Success)
	assert.Equal(t, callID, output.CallID)
	assert.Contains(t, output.Message, "任务不存在")
}

func TestToolComplete_Execute_TaskNotRunning(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建一个已完成的任务
	callID := "test_call_not_running_" + uuid.New().String()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    callID,
		SessionID: 1,
		ToolName:  "filesystem__read_file",
		Status:    dao_tool_call_task.ToolCallTaskStatusSuccess, // Already completed
	}

	_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	assert.NoError(t, err)

	// 准备测试数据
	service := &intern.ToolComplete{}
	service.InputData = &intern.ToolCompleteInputData{
		SessionID: 1,
		CallID:    callID,
		Status:    "success",
	}

	// 执行测试
	resp, _, err := service.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.ToolCompleteOutputData)
	assert.False(t, output.Success)
	assert.Equal(t, callID, output.CallID)
	assert.Contains(t, output.Message, "任务状态不正确")
}

package intern

import (
	"context"
	"fmt"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/background"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

type ToolPendingInputData struct {
	SessionID int64 `json:"session_id" validate:"required,gt=0"`
}

// ToolPendingOutputData 获取待处理任务输出数据
type ToolPendingOutputData struct {
	Tasks *dao_tool_call_task.ObjToolCallTask `json:"tasks"`
}

// ToolPending 获取待处理任务服务
type ToolPending struct {
	base.Service[ToolPendingInputData, ToolPendingOutputData]
}

// Execute 执行获取待处理任务逻辑 - 使用长轮询机制
func (s *ToolPending) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData

	resource.LoggerService.Notice(ctx, fmt.Sprintf("获取待处理任务(长轮询): session_id=%d", input.SessionID))

	// 获取任务轮询服务
	pollingService := background.GetTaskPollingService()
	// 使用长轮询等待任务，最多等待30秒
	const longPollTimeout = 30 * time.Second
	task, err := pollingService.WaitForTask(input.SessionID, longPollTimeout)

	// 在处理错误之前，首先检查请求上下文是否已被客户端取消。
	// 防止在客户端主动断开连接时（导致 context.Canceled），
	// 后续的错误处理逻辑（如回退查询）被执行，并避免记录不必要的错误或触发告警。
	if ctx.Err() == context.Canceled {
		resource.LoggerService.Notice(ctx, fmt.Sprintf("客户端主动断开连接，正常终止长轮询: session_id=%d", input.SessionID))
		return nil, nil, nil // 正常返回，不视为错误
	}

	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("长轮询等待任务失败: %v", err))
		// 回退到直接查询
		return s.executeDirectQuery(ctx, input.SessionID)
	}

	output := &ToolPendingOutputData{
		Tasks: task,
	}
	return output, nil, nil
}

// executeDirectQuery 直接查询数据库获取待处理任务
func (s *ToolPending) executeDirectQuery(ctx context.Context, sessionID int64) (ret any, ext any, err error) {
	// 直接查询数据库获取所有待处理任务，然后筛选出指定会话的第一个任务
	allTasks, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectPendingTasks(ctx)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("查询待处理任务失败: %v", err))
		return nil, nil, err
	}

	// 筛选出指定会话的第一个任务
	var sessionTask *dao_tool_call_task.ObjToolCallTask
	for _, task := range allTasks {
		if task.SessionID == sessionID {
			sessionTask = task
			break
		}
	}

	return &ToolPendingOutputData{
		Tasks: sessionTask,
	}, nil, nil
}

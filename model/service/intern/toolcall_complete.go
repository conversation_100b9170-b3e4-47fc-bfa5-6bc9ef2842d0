package intern

import (
	"context"
	"fmt"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// ToolCompleteInputData 完成任务输入数据
type ToolCompleteInputData struct {
	SessionID    int64  `json:"session_id" validate:"required,gt=0"`
	CallID       string `json:"call_id" validate:"required"`
	Status       string `json:"status" validate:"required"`
	Result       string `json:"result,omitempty"`
	OldEnvMD5    string `json:"old_env_md5,omitempty"`
	NewEnvMD5    string `json:"new_env_md5,omitempty"`
	OldEnvURL    string `json:"old_env_url,omitempty"`
	NewEnvURL    string `json:"new_env_url,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// ToolCompleteOutputData 完成任务输出数据
type ToolCompleteOutputData struct {
	Success bool   `json:"success"`
	CallID  string `json:"call_id"`
	Message string `json:"message"`
}

// ToolComplete 完成任务服务
type ToolComplete struct {
	base.Service[ToolCompleteInputData, ToolCompleteOutputData]
}

// Execute 执行完成任务逻辑
func (s *ToolComplete) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	// 验证状态值
	if input.Status != "success" && input.Status != "failed" {
		return &ToolCompleteOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: fmt.Sprintf("无效的状态值: %s，必须是 success 或 failed", input.Status),
		}, nil, nil
	}

	// 查询任务
	task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, input.SessionID, input.CallID)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError)
	}

	if task == nil {
		return &ToolCompleteOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: "任务不存在",
		}, nil, nil
	}

	// 验证Session ID匹配
	if task.SessionID != input.SessionID {
		return &ToolCompleteOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: "Session ID不匹配",
		}, nil, nil
	}

	// 检查任务状态
	if task.Status != dao_tool_call_task.ToolCallTaskStatusRunning {
		return &ToolCompleteOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: fmt.Sprintf("任务状态不正确，当前状态: %s，期望状态: running", task.Status),
		}, nil, nil
	}

	// 更新任务状态和结果
	task.Status = dao_tool_call_task.ToolCallTaskStatus(input.Status)
	task.Result = input.Result
	task.OldEnvMd5 = input.OldEnvMD5
	task.NewEnvMd5 = input.NewEnvMD5
	task.OldEnvUrl = input.OldEnvURL
	task.NewEnvUrl = input.NewEnvURL
	task.ErrorMessage = input.ErrorMessage
	now := time.Now()
	task.CompletedAt = &now

	err = dao_tool_call_task.ToolCallTaskBusinessIns.Update(ctx, task)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLUpdateError)
	}

	// Record tool call completion metrics
	switch task.Status {
	case dao_tool_call_task.ToolCallTaskStatusSuccess:
		metrics_helper.RecordToolCallSuccess()
	case dao_tool_call_task.ToolCallTaskStatusFailed:
		metrics_helper.RecordToolCallFailed()
	case dao_tool_call_task.ToolCallTaskStatusTimeout:
		metrics_helper.RecordToolCallTimeout()
	}

	output := &ToolCompleteOutputData{
		Success: true,
		CallID:  input.CallID,
		Message: "任务完成更新成功",
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("任务完成更新成功: call_id=%s, status=%s", input.CallID, input.Status))

	return output, nil, nil
}

package intern_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/ghttp"

	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/intern"
)

func TestSessionInfo_Execute(t *testing.T) {
	t.Run("TestInvalidSessionID", func(t *testing.T) {
		setUpAll(t)
		defer tearDownAll(t)

		service := &intern.SessionInfo{}
		service.InputData = &intern.SessionInfoInputData{
			SessionID: "invalid",
		}

		ctx := context.Background()
		var req ghttp.Request

		ret, ext, err := service.Execute(ctx, req)

		assert.Nil(t, ret)
		assert.Nil(t, ext)
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "invalid syntax")
	})

	t.Run("TestValidSessionIDFormat", func(t *testing.T) {
		setUpAll(t)
		defer tearDownAll(t)

		service := &intern.SessionInfo{}
		service.InputData = &intern.SessionInfoInputData{
			SessionID: "123",
		}

		// 只测试参数解析，不执行数据库操作
		assert.Equal(t, "123", service.InputData.SessionID)
	})
}

func TestSessionInfoInputData_Validation(t *testing.T) {
	t.Run("TestRequiredFields", func(t *testing.T) {
		// 测试必填字段
		input := intern.SessionInfoInputData{}
		assert.Empty(t, input.SessionID)

		input.SessionID = "123"
		assert.Equal(t, "123", input.SessionID)
	})
}

func TestSessionInfoOutputData_Structure(t *testing.T) {
	t.Run("TestOutputStructure", func(t *testing.T) {
		// 测试输出数据结构
		output := intern.SessionInfoOutputData{
			Session: &dao_session.ObjSession{
				SessionID:       123,
				JobID:           "test-job",
				EnvID:           456,
				ContainerStatus: dao_session.ContainerStatusRunning,
			},
			Servers: []*dao_register_mcp_server.ObjRegisterMcpServer{
				{
					ServerID:    1,
					ServerName:  "filesystem",
					Type:        dao_register_mcp_server.TypeLocal,
					Command:     "npx",
					Description: "文件系统操作工具",
				},
			},
			Environment: &dao_mcp_env.ObjMcpEnv{
				EnvID:       456,
				Name:        "test-env",
				Description: "测试环境",
			},
		}

		assert.NotNil(t, output.Session)
		assert.Equal(t, int64(123), output.Session.SessionID)
		assert.Equal(t, "test-job", output.Session.JobID)
		assert.Equal(t, int64(456), output.Session.EnvID)

		assert.Len(t, output.Servers, 1)
		assert.Equal(t, int64(1), output.Servers[0].ServerID)
		assert.Equal(t, "filesystem", output.Servers[0].ServerName)
		assert.Equal(t, dao_register_mcp_server.TypeLocal, output.Servers[0].Type)

		assert.NotNil(t, output.Environment)
		assert.Equal(t, int64(456), output.Environment.EnvID)
		assert.Equal(t, "test-env", output.Environment.Name)
	})
}

package intern

import (
	"context"
	"fmt"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// SessionStopInputData Session停止输入数据
type SessionStopInputData struct {
	SessionID int64  `json:"session_id" validate:"required"`
	Message   string `json:"message"`
}

// SessionStopOutputData Session停止输出数据
type SessionStopOutputData struct {
	Success   bool   `json:"success"`
	SessionID int64  `json:"session_id"`
	Message   string `json:"message"`
}

// SessionStop Session停止服务（内部API）
type SessionStop struct {
	base.Service[SessionStopInputData, SessionStopOutputData]
}

// Execute 执行Session停止逻辑
func (s *SessionStop) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData

	// 1. 验证Session是否存在
	session, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError)
	}

	// 2. 检查Session状态 - 如果已经停止，直接返回成功（幂等性）
	if session.ContainerStatus == dao_session.ContainerStatusStopped {
		output := &SessionStopOutputData{
			Success:   true,
			SessionID: input.SessionID,
			Message:   "Session已经停止",
		}
		return output, nil, nil
	}

	// 3. 记录会话日志到BOS
	err = dao_session.SessionBusinessIns.SessionLogRecord(ctx, input.SessionID, session.JobID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("记录会话日志到BOS失败: %v", err))
	}

	// 5. 更新Session状态为停止
	updateData := map[string]any{
		"container_status": dao_session.ContainerStatusStopped,
		"err_msg":          input.Message,
		"stopped_at":       time.Now(),
	}

	err = dao_session.SessionBusinessIns.UpdateMap(ctx, input.SessionID, updateData)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLUpdateError)
	}
	// Record metrics for session stopped
	metrics_helper.RecordSessionStopped()

	// 6. 构建返回结果
	output := &SessionStopOutputData{
		Success:   true,
		SessionID: input.SessionID,
		Message:   "Session停止成功",
	}
	return output, nil, nil
}

package intern

import (
	"context"
	"fmt"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// === 认领任务接口 ===

// ToolClaimInputData 认领任务输入数据
type ToolClaimInputData struct {
	SessionID int64  `json:"session_id" validate:"required,gt=0"`
	CallID    string `json:"call_id" validate:"required"`
}

// ToolClaimOutputData 认领任务输出数据
type ToolClaimOutputData struct {
	Success   bool   `json:"success"`
	CallID    string `json:"call_id"`
	Name      string `json:"name,omitempty"`
	Arguments string `json:"arguments,omitempty"`
	Message   string `json:"message,omitempty"`
}

// ToolClaim 认领任务服务
type ToolClaim struct {
	base.Service[ToolClaimInputData, ToolClaimOutputData]
}

// Execute 执行认领任务逻辑
func (s *ToolClaim) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData

	// 查询任务是否存在且为待处理状态
	task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, input.SessionID, input.CallID)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError)
	}

	if task == nil {
		return &ToolClaimOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: "任务不存在",
		}, nil, nil
	}

	// 验证Session ID匹配
	if task.SessionID != input.SessionID {
		return &ToolClaimOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: "Session ID不匹配",
		}, nil, nil
	}

	// 检查任务状态
	if task.Status != dao_tool_call_task.ToolCallTaskStatusPending {
		return &ToolClaimOutputData{
			Success: false,
			CallID:  input.CallID,
			Message: fmt.Sprintf("任务状态不正确，当前状态: %s", task.Status),
		}, nil, nil
	}

	// 更新任务状态为运行中
	task.Status = dao_tool_call_task.ToolCallTaskStatusRunning
	now := time.Now()
	task.StartedAt = &now

	err = dao_tool_call_task.ToolCallTaskBusinessIns.Update(ctx, task)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLUpdateError)
	}

	// Record tool call running metrics
	metrics_helper.RecordToolCallRunning()

	output := &ToolClaimOutputData{
		Success:   true,
		CallID:    input.CallID,
		Name:      task.ToolName,
		Arguments: task.Arguments,
		Message:   "任务认领成功",
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("任务认领成功: call_id=%s", input.CallID))

	return output, nil, nil
}

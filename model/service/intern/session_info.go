package intern

import (
	"context"
	"errors"
	"strconv"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

type SessionInfoInputData struct {
	SessionID string `json:"session_id" form:"session_id" binding:"required" validate:"required"`
}

type SessionInfoOutputData struct {
	Session     *dao_session.ObjSession                         `json:"session"`
	Servers     []*dao_register_mcp_server.ObjRegisterMcpServer `json:"servers"`
	Environment *dao_mcp_env.ObjMcpEnv                          `json:"environment"`
}

type SessionInfo struct {
	base.Service[SessionInfoInputData, SessionInfoOutputData]
}

// Execute 执行获取Session信息的逻辑
func (s *SessionInfo) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData

	// 将session_id转换为int64
	sessionID, err := strconv.ParseInt(input.SessionID, 10, 64)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.UserInputError)
	}

	// 1. 获取Session信息
	session, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	if err != nil {
		return nil, nil, err
	}

	if session == nil {
		return nil, nil, errcode.NewCustomErr(ctx, errors.New("Session不存在"), errcode.SessionNotFound)
	}

	// 2. 获取关联的MCP服务器信息
	var servers []*dao_register_mcp_server.ObjRegisterMcpServer
	if len(session.ServerIds) > 0 {
		serverMap, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.GetMapByPrimaryKeys(ctx, session.ServerIds)
		if err != nil {
			return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError)
		}

		// 按照session中的server_ids顺序排列
		for _, serverID := range session.ServerIds {
			if server, exists := serverMap[serverID]; exists {
				servers = append(servers, server)
			}
		}
	}

	// 3. 获取环境信息
	var environment *dao_mcp_env.ObjMcpEnv
	if session.EnvID > 0 {
		environment, err = dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, session.EnvID)
		if err != nil {
			return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError)
		}
	}

	// 4. 构建返回数据
	result := &SessionInfoOutputData{
		Session:     session,
		Servers:     servers,
		Environment: environment,
	}

	return result, nil, nil
}

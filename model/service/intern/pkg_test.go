package intern_test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/intern"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
}

func tearDownAll(t *testing.T) {

}

// ===== SessionReady Complete Flow Tests =====

func TestSessionReady_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 先创建一个Session记录
	serverIDsJSON := dao_base.JSONArray[int64]{1, 2}
	testSession := &dao_session.ObjSession{
		JobID:           "test-job-123",
		SessionCode:     fmt.Sprintf("test_session_ready_%d", time.Now().UnixNano()),
		EnvID:           1,
		ServerIds:       serverIDsJSON,
		McpTools:        nil,
		ContainerStatus: "pending",
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, testSession)
	assert.NoError(t, err)

	// 准备测试数据
	ins := &intern.SessionReady{}
	ins.InputData = &intern.SessionReadyInputData{
		SessionID: sessionID,
		MCPTools: []types.MCPTool{
			{
				Name:        "filesystem",
				Description: "文件系统操作工具",
				Parameters:  map[string]interface{}{"type": "object"},
			},
		},
		Message: "容器已就绪",
	}

	// 执行测试
	resp, _, err := ins.Execute(ctx, nil)

	// 验证输出
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output := resp.(*intern.SessionReadyOutputData)
	assert.True(t, output.Success)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Contains(t, output.Message, "Session就绪通知处理成功")

	// 验证数据库中的记录已更新
	updatedSession, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	assert.NoError(t, err)
	assert.Equal(t, dao_session.ContainerStatusRunning, updatedSession.ContainerStatus)
	assert.NotNil(t, updatedSession.McpTools)
}

func TestSessionReady_Execute_SessionNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据 - 使用不存在的SessionID
	ins := &intern.SessionReady{}
	ins.InputData = &intern.SessionReadyInputData{
		SessionID: 999999, // 不存在的SessionID
		MCPTools: []types.MCPTool{
			{
				Name:        "filesystem",
				Description: "文件系统操作工具",
				Parameters:  map[string]interface{}{"type": "object"},
			},
		},
		Message: "容器已就绪",
	}

	// 执行测试
	resp, _, err := ins.Execute(ctx, nil)

	// 验证输出
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "Session不存在")
}

// 准备测试数据
func prepareTestData(t *testing.T) (serverID int64, envID int64) {
	// 创建测试用的MCP服务器
	serverArgs := dao_base.JSONArray[string]{"--stdio"}
	server := &dao_register_mcp_server.ObjRegisterMcpServer{
		ServerName:       "test_filesystem_server",
		Command:          "npx",
		Args:             serverArgs,
		Description:      "测试文件系统MCP服务器",
		ServerCodeBosUrl: "https://bos.example.com/mcp-servers/filesystem.zip",
	}

	serverID, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, server)
	assert.NoError(t, err)
	assert.Greater(t, serverID, int64(0))

	// 创建测试用的环境
	envDependency := dao_base.JSONData{
		"python_version": "3.9",
		"requirements":   []string{"pandas", "numpy"},
	}
	env := &dao_mcp_env.ObjMcpEnv{
		EnvMd5:        "test_env_md5_123",
		Name:          "test_env",
		Description:   "测试环境",
		BosURL:        "https://bos.example.com/envs/test.tar.gz",
		EnvDependency: envDependency,
	}

	envID, err = dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
	assert.NoError(t, err)
	assert.Greater(t, envID, int64(0))

	return serverID, envID
}

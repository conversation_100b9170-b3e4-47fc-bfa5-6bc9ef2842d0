package intern

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSessionStop_Execute(t *testing.T) {
	t.Run("TestValidSessionIDFormat", func(t *testing.T) {
		service := &SessionStop{}
		service.InputData = &SessionStopInputData{
			SessionID: 123,
		}

		// 只测试参数解析，不执行数据库操作
		assert.Equal(t, int64(123), service.InputData.SessionID)
	})
}

func TestSessionStopInputData_Validation(t *testing.T) {
	t.Run("TestRequiredSessionID", func(t *testing.T) {
		// 测试输入数据结构
		input := SessionStopInputData{
			SessionID: 123,
		}

		assert.Equal(t, int64(123), input.SessionID)
	})
}

func TestSessionStopOutputData_Structure(t *testing.T) {
	t.Run("TestOutputStructure", func(t *testing.T) {
		// 测试输出数据结构
		output := SessionStopOutputData{
			Success:   true,
			SessionID: 123,
			Message:   "Session停止成功",
		}

		assert.True(t, output.Success)
		assert.Equal(t, int64(123), output.SessionID)
		assert.Equal(t, "Session停止成功", output.Message)
	})

	t.Run("TestPartialFailureOutput", func(t *testing.T) {
		// 测试部分失败的输出结构
		output := SessionStopOutputData{
			Success:   false,
			SessionID: 123,
			Message:   "Session状态已更新为停止，但删除K8s作业失败: some error",
		}

		assert.False(t, output.Success)
		assert.Equal(t, int64(123), output.SessionID)
		assert.Contains(t, output.Message, "删除K8s作业失败")
	})
}

// TestSessionStopIdempotency 测试幂等性
func TestSessionStopIdempotency(t *testing.T) {
	t.Run("TestAlreadyStoppedSession", func(t *testing.T) {
		// 模拟已经停止的session应该直接返回成功
		// 这里只测试逻辑结构，实际的数据库测试需要在集成测试中进行

		expectedOutput := SessionStopOutputData{
			Success:   true,
			SessionID: 123,
			Message:   "Session已经停止",
		}

		assert.True(t, expectedOutput.Success)
		assert.Equal(t, "Session已经停止", expectedOutput.Message)
	})
}

// TestSessionStopErrorHandling 测试错误处理
func TestSessionStopErrorHandling(t *testing.T) {
	t.Run("TestK8sDeleteFailure", func(t *testing.T) {
		// 测试K8s删除失败时的错误处理逻辑
		expectedOutput := SessionStopOutputData{
			Success:   false,
			SessionID: 123,
			Message:   "Session状态已更新为停止，但删除K8s作业失败: k8s error",
		}

		assert.False(t, expectedOutput.Success)
		assert.Contains(t, expectedOutput.Message, "删除K8s作业失败")
	})

	t.Run("TestDatabaseUpdateFailure", func(t *testing.T) {
		// 测试数据库更新失败的情况
		// 这种情况下应该返回错误，因为状态更新是关键操作
		service := &SessionStop{}
		service.InputData = &SessionStopInputData{
			SessionID: 123,
		}

		// 验证输入数据格式正确
		assert.Equal(t, int64(123), service.InputData.SessionID)
	})
}

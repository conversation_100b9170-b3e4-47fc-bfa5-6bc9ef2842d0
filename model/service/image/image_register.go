package service

import (
	"context"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_obj_image "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/obj_image"
)

// ImageInitInputData 镜像初始化输入数据
type ImageRegisterInputData struct {
	ImageID            string                 `json:"image_id,omitempty"`
	ImagePath          string                 `json:"image_path" validate:"required"`
	ImageDescription   string                 `json:"image_description,omitempty"`
	ContainerPort      int                    `json:"container_port,omitempty"`
	ContainerEnv       map[string]interface{} `json:"container_env,omitempty"`
	ContainerCommand   string                 `json:"container_command" validate:"required"`
	ContainerArgs      []string               `json:"container_args,omitempty"`
	ContainerMounts    map[string]interface{} `json:"container_mounts,omitempty"`
	ContainerResources map[string]interface{} `json:"container_resources,omitempty"`
	RootUser           bool                   `json:"root_user,omitempty"`
}

// ImageRegisterOutputData 镜像初始化输出数据
type ImageRegisterOutputData struct {
	ID          int64  `json:"id"`
	ImageID     string `json:"image_id"`
	Status      string `json:"status"`
	Description string `json:"description"`
}

// ImageInit 镜像初始化服务
type ImageRegister struct {
	base.Service[ImageRegisterInputData, ImageRegisterOutputData]
}

// Execute 执行镜像初始化逻辑
func (s *ImageRegister) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData

	// 1. 创建镜像记录
	newImage := &dao_obj_image.ObjImage{
		ImageID:            input.ImageID, // 如果为空，Insert方法会自动生成UUID
		ImagePath:          input.ImagePath,
		ImageDescription:   input.ImageDescription,
		ContainerPort:      input.ContainerPort,
		ContainerEnv:       dao.JSONData(input.ContainerEnv),
		ContainerCommand:   input.ContainerCommand,
		ContainerArgs:      dao.JSONArray[string](input.ContainerArgs),
		ContainerMounts:    dao.JSONData(input.ContainerMounts),
		ContainerResources: dao.JSONData(input.ContainerResources),
		RootUser:           input.RootUser,
	}

	id, err := dao_obj_image.ObjImageBusinessIns.Insert(ctx, newImage)
	if err != nil {
		return nil, nil, err
	}

	response := &ImageRegisterOutputData{
		ID:      id,
		ImageID: newImage.ImageID, // 使用实际插入后的ImageID（可能是生成的UUID）
		Status:  "initialized",
	}
	return response, nil, nil
}

package service_test

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_obj_image "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/obj_image"
	service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/image"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll(t *testing.T) {}

// TestImageRegister_Execute_Success tests successful image initialization
func TestImageRegister_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/test-runtime:latest",
		ImageDescription: "测试MCP运行时镜像",
		ContainerPort:    8080,
		ContainerEnv: map[string]interface{}{
			"PYTHONPATH": "/opt/python3.10/bin/python3.10",
			"NODE_ENV":   "production",
		},
		ContainerCommand: "python",
		ContainerArgs:    []string{"--stdio", "--config", "/path/to/config.json"},
		ContainerMounts: map[string]interface{}{
			"input":  "/home/<USER>/input",
			"output": "/home/<USER>/output",
		},
		ContainerResources: map[string]interface{}{
			"cpu":    1,
			"memory": map[string]interface{}{"value": 1, "type": "G"},
		},
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)
	assert.Equal(t, "测试MCP运行时镜像", response.Description)
}

// TestImageRegister_Execute_Success_MinimalFields tests with minimal required fields
func TestImageRegister_Execute_Success_MinimalFields(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备最小必需字段的测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/minimal-test:latest",
		ContainerCommand: "node",
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)
	assert.Empty(t, response.Description) // 空描述

	t.Logf("✅ ImageRegister最小字段测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_DatabaseError tests database insertion error
func TestImageRegister_Execute_DatabaseError(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/error-test:latest",
		ContainerCommand: "python",
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 创建 gomonkey patches 来模拟数据库错误
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock 数据库插入失败
	patches.ApplyMethod(reflect.TypeOf(dao_obj_image.ObjImageBusinessIns), "Insert",
		func(_ dao_obj_image.ObjImageBusiness, ctx context.Context, obj *dao_obj_image.ObjImage) (int64, error) {
			return 0, assert.AnError
		})

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, ret)
	assert.Nil(t, ext)

	t.Logf("✅ 数据库错误测试成功，错误信息: %v", err)
}

// TestImageRegister_Execute_Success_WithMocks tests with mocked database operations
func TestImageRegister_Execute_Success_WithMocks(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/mock-test:latest",
		ImageDescription: "Mock测试镜像",
		ContainerPort:    9090,
		ContainerEnv: map[string]interface{}{
			"ENV": "test",
		},
		ContainerCommand: "java",
		ContainerArgs:    []string{"-jar", "app.jar"},
		ContainerMounts: map[string]interface{}{
			"data": "/data",
		},
		ContainerResources: map[string]interface{}{
			"cpu": 2,
		},
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 创建 gomonkey patches
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock 数据库插入成功
	patches.ApplyMethod(reflect.TypeOf(dao_obj_image.ObjImageBusinessIns), "Insert",
		func(_ dao_obj_image.ObjImageBusiness, ctx context.Context, obj *dao_obj_image.ObjImage) (int64, error) {
			// 验证插入的数据
			assert.Equal(t, "mcp/mock-test:latest", obj.ImagePath)
			assert.Equal(t, "Mock测试镜像", obj.ImageDescription)
			assert.Equal(t, 9090, obj.ContainerPort)
			assert.Equal(t, "java", obj.ContainerCommand)
			assert.NotNil(t, obj.ContainerEnv)
			assert.NotNil(t, obj.ContainerArgs)
			assert.NotNil(t, obj.ContainerMounts)
			assert.NotNil(t, obj.ContainerResources)

			return 12345, nil // 返回模拟的ImageID
		})

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Equal(t, int64(12345), response.ImageID)
	assert.Equal(t, "initialized", response.Status)
	assert.Equal(t, "Mock测试镜像", response.Description)

	t.Logf("✅ Mock测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_ComplexData tests with complex container configurations
func TestImageRegister_Execute_ComplexData(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备复杂配置的测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/complex-runtime:v1.2.3",
		ImageDescription: "复杂配置的MCP运行时镜像",
		ContainerPort:    8443,
		ContainerEnv: map[string]interface{}{
			"PYTHONPATH":  "/opt/python3.10/bin/python3.10",
			"NODE_ENV":    "production",
			"DEBUG":       false,
			"MAX_WORKERS": 10,
			"TIMEOUT":     30.5,
			"FEATURES":    []string{"feature1", "feature2"},
			"CONFIG": map[string]interface{}{
				"database": map[string]interface{}{
					"host": "localhost",
					"port": 5432,
				},
			},
		},
		ContainerCommand: "/usr/local/bin/python",
		ContainerArgs: []string{
			"-m", "mcp_server",
			"--config", "/etc/mcp/config.yaml",
			"--log-level", "info",
			"--workers", "4",
		},
		ContainerMounts: map[string]interface{}{
			"config":    "/etc/mcp",
			"data":      "/var/lib/mcp",
			"logs":      "/var/log/mcp",
			"temp":      "/tmp/mcp",
			"workspace": "/home/<USER>",
		},
		ContainerResources: map[string]interface{}{
			"cpu": 4,
			"memory": map[string]interface{}{
				"value": 8,
				"type":  "G",
			},
			"disk": map[string]interface{}{
				"value": 100,
				"type":  "G",
			},
			"gpu": map[string]interface{}{
				"enabled": true,
				"count":   1,
				"type":    "nvidia-tesla-v100",
			},
		},
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)
	assert.Equal(t, "复杂配置的MCP运行时镜像", response.Description)

	t.Logf("✅ 复杂数据测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_EmptyContainerEnv tests with empty container environment
func TestImageRegister_Execute_EmptyContainerEnv(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备空环境变量的测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/empty-env:latest",
		ImageDescription: "空环境变量测试镜像",
		ContainerCommand: "python",
		ContainerEnv:     map[string]interface{}{}, // 空环境变量
		ContainerArgs:    []string{},               // 空参数
		ContainerMounts:  map[string]interface{}{}, // 空挂载
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)

	t.Logf("✅ 空环境变量测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_NilContainerFields tests with nil container fields
func TestImageRegister_Execute_NilContainerFields(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备nil字段的测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:          "mcp/nil-fields:latest",
		ContainerCommand:   "node",
		ContainerEnv:       nil, // nil环境变量
		ContainerArgs:      nil, // nil参数
		ContainerMounts:    nil, // nil挂载
		ContainerResources: nil, // nil资源
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)

	t.Logf("✅ Nil字段测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_SpecialCharacters tests with special characters in fields
func TestImageRegister_Execute_SpecialCharacters(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备包含特殊字符的测试数据
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/special-chars:latest",
		ImageDescription: "测试镜像 with special chars: !@#$%^&*()_+-={}[]|\\:;\"'<>?,./",
		ContainerCommand: "python",
		ContainerEnv: map[string]interface{}{
			"SPECIAL_VAR": "value with spaces and symbols: !@#$%",
			"UNICODE":     "测试中文字符 🚀 emoji",
			"JSON_LIKE":   `{"key": "value", "number": 123}`,
		},
		ContainerArgs: []string{
			"--config", "/path/with spaces/config.json",
			"--special-arg", "value!@#$%",
		},
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)
	assert.Contains(t, response.Description, "special chars")

	t.Logf("✅ 特殊字符测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_LargeData tests with large data structures
func TestImageRegister_Execute_LargeData(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备大数据量的测试数据
	largeEnv := make(map[string]interface{})
	for i := 0; i < 100; i++ {
		largeEnv[fmt.Sprintf("VAR_%d", i)] = fmt.Sprintf("value_%d", i)
	}

	largeArgs := make([]string, 50)
	for i := 0; i < 50; i++ {
		largeArgs[i] = fmt.Sprintf("--arg-%d", i)
	}

	largeMounts := make(map[string]interface{})
	for i := 0; i < 20; i++ {
		largeMounts[fmt.Sprintf("mount_%d", i)] = fmt.Sprintf("/path/to/mount_%d", i)
	}

	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/large-data:latest",
		ImageDescription: "大数据量测试镜像",
		ContainerCommand: "python",
		ContainerEnv:     largeEnv,
		ContainerArgs:    largeArgs,
		ContainerMounts:  largeMounts,
		ContainerResources: map[string]interface{}{
			"cpu":    16,
			"memory": map[string]interface{}{"value": 64, "type": "G"},
			"limits": map[string]interface{}{
				"max_connections": 1000,
				"max_memory":      "128G",
				"max_cpu":         "32",
			},
		},
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.ImageRegisterOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ImageID, int64(0))
	assert.Equal(t, "initialized", response.Status)

	t.Logf("✅ 大数据量测试成功，ImageID=%d", response.ImageID)
}

// TestImageRegister_Execute_EmptyImagePath tests error when ImagePath is empty
func TestImageRegister_Execute_EmptyImagePath(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据，ImagePath 为空
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "", // 空的 ImagePath
		ContainerCommand: "python",
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	_, _, err := ins.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err, "Expected an error for empty ImagePath, but got nil")

	t.Logf("✅ 空ImagePath测试成功，错误信息: %v", err)
}

// TestImageRegister_Execute_EmptyContainerCommand tests error when ContainerCommand is empty
func TestImageRegister_Execute_EmptyContainerCommand(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试数据，ContainerCommand 为空
	inputData := &service.ImageRegisterInputData{
		ImagePath:        "mcp/test-runtime:latest",
		ContainerCommand: "", // 空的 ContainerCommand
	}

	// 创建服务实例
	ins := &service.ImageRegister{}
	ins.InputData = inputData

	// 执行测试
	_, _, err := ins.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err, "Expected an error for empty ContainerCommand, but got nil")

	t.Logf("✅ 空ContainerCommand测试成功，错误信息: %v", err)
}

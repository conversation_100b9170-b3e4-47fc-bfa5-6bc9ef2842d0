package tool

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// ToolCallRequest 工具调用请求结构
type ToolCallRequest struct {
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
	Name        string `json:"name" validate:"required"`
	Arguments   string `json:"arguments" validate:"required"`
	CallID      string `json:"call_id" validate:"omitempty"`
	TimeoutSecs int    `json:"timeout_seconds,omitempty"`
}

// ToolCallResponse 工具调用响应结构
type ToolCallResponse struct {
	CallID       string `json:"call_id"`
	Status       string `json:"status"`
	Result       string `json:"result,omitempty"`
	OldEnvMD5    string `json:"old_env_md5,omitempty"`
	NewEnvMD5    string `json:"new_env_md5,omitempty"`
	OldEnvURL    string `json:"old_env_url,omitempty"`
	NewEnvURL    string `json:"new_env_url,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// ToolCallCommon 通用工具调用逻辑
type ToolCallCommon struct{}

// ValidateSession 验证Session有效性
func (tc *ToolCallCommon) ValidateSession(ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
	var session *dao_session.ObjSession
	var err error

	if sessionCode != "" {
		session, err = dao_session.SessionBusinessIns.GetSessionByCode(ctx, sessionCode)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("通过 session_code 查询Session失败: %v", err))
			return nil, errcode.NewCustomErr(ctx, err, errcode.SessionNotFound)
		}
	} else if sessionID > 0 {
		session, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("查询Session失败: %v", err))
			return nil, err
		}
	} else {
		return nil, errcode.NewCustomErr(ctx, errors.New("session_id 或 session_code 必须提供一个"), errcode.RequiredFieldMissing)
	}
	return session, nil
}

// CreateToolCallTask 创建工具调用任务记录
func (tc *ToolCallCommon) CreateToolCallTask(ctx context.Context, req *ToolCallRequest, session *dao_session.ObjSession) (*dao_tool_call_task.ObjToolCallTask, error) {
	// 获取当前环境MD5（从Session的环境信息中）
	oldEnvMD5 := ""
	oldEnvURL := ""
	// TODO: 这里可以从session中获取当前环境的MD5和URL信息
	// 目前先设置为空，后续可以扩展

	// 创建工具调用任务记录
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    req.CallID,
		SessionID: session.SessionID,
		ToolName:  req.Name,
		Arguments: req.Arguments,
		OldEnvMd5: oldEnvMD5,
		OldEnvUrl: oldEnvURL,
		Status:    dao_tool_call_task.ToolCallTaskStatusPending,
	}

	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("创建工具调用任务失败: %v", err))
		return nil, err
	}

	task.TaskID = taskID

	return task, nil
}

// WaitForTaskCompletion 等待任务完成（用于同步调用）
func (tc *ToolCallCommon) WaitForTaskCompletion(ctx context.Context, sessionID int64, callID string, timeoutSecs int) (*ToolCallResponse, error) {
	// 设置默认超时时间
	if timeoutSecs <= 0 {
		timeoutSecs = 30 // 默认30秒
	}

	pollingInterval := 1 * time.Second // 1秒轮询间隔
	maxWaitTime := time.Duration(timeoutSecs) * time.Second
	startTime := time.Now()

	for {
		// 检查超时
		if time.Since(startTime) > maxWaitTime {
			// 更新任务状态为超时
			task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, sessionID, callID)
			if err == nil && task != nil && task.Status == dao_tool_call_task.ToolCallTaskStatusRunning {
				_ = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateStatus(ctx, task.TaskID, dao_tool_call_task.ToolCallTaskStatusTimeout, "任务执行超时")
			}

			return nil, errcode.NewCustomErr(ctx, fmt.Errorf("任务执行超时，超过%d秒", timeoutSecs), errcode.ToolCallTaskTimeoutError)
		}

		// 查询任务状态
		task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, sessionID, callID)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("查询任务状态失败: %v", err))
			time.Sleep(pollingInterval)
			continue
		}

		if task == nil {
			return nil, errcode.NewCustomErr(ctx, errors.New("任务不存在"), errcode.ToolCallTaskNotFound)
		}

		// 检查任务是否完成
		switch task.Status {
		case dao_tool_call_task.ToolCallTaskStatusSuccess:
			return &ToolCallResponse{
				CallID:    callID,
				Status:    string(task.Status),
				Result:    task.Result,
				OldEnvMD5: task.OldEnvMd5,
				NewEnvMD5: task.NewEnvMd5,
				OldEnvURL: task.OldEnvUrl,
				NewEnvURL: task.NewEnvUrl,
			}, nil

		case dao_tool_call_task.ToolCallTaskStatusFailed:
			return &ToolCallResponse{
				CallID:       callID,
				Status:       string(task.Status),
				Result:       task.Result,
				ErrorMessage: task.ErrorMessage,
				OldEnvMD5:    task.OldEnvMd5,
				NewEnvMD5:    task.NewEnvMd5,
				OldEnvURL:    task.OldEnvUrl,
				NewEnvURL:    task.NewEnvUrl,
			}, errcode.NewCustomErr(ctx, fmt.Errorf("%s: %s", string(task.Status), task.ErrorMessage), errcode.ToolCallTaskTimeoutError)

		case dao_tool_call_task.ToolCallTaskStatusTimeout:
			return &ToolCallResponse{
				CallID:       callID,
				Status:       string(task.Status),
				Result:       task.Result,
				ErrorMessage: task.ErrorMessage,
				OldEnvMD5:    task.OldEnvMd5,
				NewEnvMD5:    task.NewEnvMd5,
				OldEnvURL:    task.OldEnvUrl,
				NewEnvURL:    task.NewEnvUrl,
			}, errcode.NewCustomErr(ctx, fmt.Errorf("%s: %s", string(task.Status), task.ErrorMessage), errcode.ToolCallTaskTimeoutError)

		case dao_tool_call_task.ToolCallTaskStatusPending, dao_tool_call_task.ToolCallTaskStatusRunning:
			// 任务还在执行中，继续等待
			time.Sleep(pollingInterval)
			continue

		default:
			return nil, errcode.NewCustomErr(ctx, fmt.Errorf("未知任务状态: %s", task.Status), errcode.SysFileReadError)
		}
	}
}

// WaitForTaskCompletionAndGetTask 等待任务完成并返回任务对象（用于策略模式）
func (tc *ToolCallCommon) WaitForTaskCompletionAndGetTask(ctx context.Context, sessionID int64, callID string, timeoutSecs int) (*dao_tool_call_task.ObjToolCallTask, error) {
	// 设置默认超时时间
	if timeoutSecs <= 0 {
		timeoutSecs = 30 // 默认30秒
	}

	pollingInterval := 1 * time.Second // 1秒轮询间隔
	maxWaitTime := time.Duration(timeoutSecs) * time.Second
	startTime := time.Now()

	for {
		// 检查超时
		if time.Since(startTime) > maxWaitTime {
			// 更新任务状态为超时
			task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, sessionID, callID)
			if err == nil && task != nil && task.Status == dao_tool_call_task.ToolCallTaskStatusRunning {
				_ = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateStatus(ctx, task.TaskID, dao_tool_call_task.ToolCallTaskStatusTimeout, "任务执行超时")
				// 重新查询更新后的任务状态
				task, _ = dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, sessionID, callID)
			}

			return task, errcode.NewCustomErr(ctx, fmt.Errorf("任务执行超时，超过%d秒", timeoutSecs), errcode.ToolCallTaskTimeoutError)
		}

		// 查询任务状态
		task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, sessionID, callID)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("查询任务状态失败: %v", err))
			time.Sleep(pollingInterval)
			continue
		}

		if task == nil {
			return nil, errcode.NewCustomErr(ctx, errors.New("任务不存在"), errcode.SQLSelectError)
		}

		// 检查任务是否完成
		switch task.Status {
		case dao_tool_call_task.ToolCallTaskStatusSuccess,
			dao_tool_call_task.ToolCallTaskStatusFailed,
			dao_tool_call_task.ToolCallTaskStatusTimeout:
			return task, nil

		case dao_tool_call_task.ToolCallTaskStatusPending, dao_tool_call_task.ToolCallTaskStatusRunning:
			// 任务还在执行中，继续等待
			time.Sleep(pollingInterval)
			continue

		default:
			return task, errcode.NewCustomErr(ctx, fmt.Errorf("未知任务状态: %s", task.Status), errcode.SysFileReadError)
		}
	}
}

// GetTaskStatus 获取任务状态（用于异步调用查询）
func (tc *ToolCallCommon) GetTaskStatus(ctx context.Context, sessionID int64, callID string) (*ToolCallResponse, error) {
	task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, sessionID, callID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("查询任务状态失败: %v", err))
		return nil, err
	}

	if task == nil {
		return nil, errcode.NewCustomErr(ctx, errors.New("任务不存在"), errcode.SQLSelectError)
	}

	// 检查任务状态，如果失败或超时则返回错误
	switch task.Status {
	case dao_tool_call_task.ToolCallTaskStatusFailed:
		return nil, errcode.NewCustomErr(ctx, errors.New(task.ErrorMessage), errcode.ToolCallTaskExecuteError)
	case dao_tool_call_task.ToolCallTaskStatusTimeout:
		return nil, errcode.NewCustomErr(ctx, errors.New(task.ErrorMessage), errcode.ToolCallTaskTimeoutError)
	default:
		return &ToolCallResponse{
			CallID:       callID,
			Status:       string(task.Status),
			Result:       task.Result,
			OldEnvMD5:    task.OldEnvMd5,
			NewEnvMD5:    task.NewEnvMd5,
			OldEnvURL:    task.OldEnvUrl,
			NewEnvURL:    task.NewEnvUrl,
			ErrorMessage: task.ErrorMessage,
		}, nil
	}
}

// ValidateToolCallRequest 验证工具调用请求参数
func (tc *ToolCallCommon) ValidateToolCallRequest(ctx context.Context, req *ToolCallRequest) error {
	if req.SessionID <= 0 && req.SessionCode == "" {
		return errcode.NewCustomErr(ctx, errors.New("session_id 和 session_code 必须提供一个"), errcode.UserInputError)
	}

	if req.Name == "" {
		return errcode.NewCustomErr(ctx, errors.New("tool name不能为空"), errcode.SQLSelectError)
	}

	// 如果CallID为空，自动生成一个UUID
	if req.CallID == "" {
		req.CallID = uuid.New().String()
	}

	if req.Arguments == "" {
		return errcode.NewCustomErr(ctx, errors.New("arguments不能为空"), errcode.SQLSelectError)
	}

	return nil
}

package tool

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSweAgentStrategy_BuildCommand(t *testing.T) {
	strategy := &SweAgentStrategy{}

	tests := []struct {
		name      string
		toolName  string
		arguments map[string]interface{}
		expected  []string
	}{
		{
			name:     "file_editor with command and parameters",
			toolName: "file_editor",
			arguments: map[string]interface{}{
				"command": "view",
				"path":    "/testbed",
				"concise": true,
			},
			expected: []string{"file_editor", "view", "--path", "/testbed", "--concise", "True"},
		},
		{
			name:     "tool with boolean false",
			toolName: "test_tool",
			arguments: map[string]interface{}{
				"command": "run",
				"verbose": false,
				"count":   5,
			},
			expected: []string{"test_tool", "run", "--verbose", "False", "--count", "5"},
		},
		{
			name:     "tool without command",
			toolName: "simple_tool",
			arguments: map[string]interface{}{
				"input":  "test.txt",
				"output": "result.txt",
			},
			expected: []string{"simple_tool", "--input", "test.txt", "--output", "result.txt"},
		},
		{
			name:      "tool with no arguments",
			toolName:  "no_args_tool",
			arguments: map[string]interface{}{},
			expected:  []string{"no_args_tool"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := strategy.BuildCommand(tt.toolName, tt.arguments)
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCommandStrategyManager(t *testing.T) {
	manager := NewCommandStrategyManager()

	t.Run("get existing strategy", func(t *testing.T) {
		strategy, err := manager.GetStrategy("sweagent")
		assert.NoError(t, err)
		assert.NotNil(t, strategy)
		assert.Equal(t, "sweagent", strategy.GetStrategyName())
	})

	t.Run("get non-existing strategy", func(t *testing.T) {
		_, err := manager.GetStrategy("nonexistent")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "strategy 'nonexistent' not found")
	})

	t.Run("build command with strategy", func(t *testing.T) {
		argumentsJSON := `{"command":"view","path":"/testbed","concise":true}`
		result, err := manager.BuildCommand("sweagent", "file_editor", argumentsJSON)
		assert.NoError(t, err)
		expected := []string{"file_editor", "view", "--path", "/testbed", "--concise", "True"}
		assert.Equal(t, expected, result)
	})

	t.Run("build command with invalid JSON", func(t *testing.T) {
		argumentsJSON := `{"command":"view","path":"/testbed","concise":true` // 缺少结束括号
		_, err := manager.BuildCommand("sweagent", "file_editor", argumentsJSON)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to parse arguments")
	})

	t.Run("build command with empty arguments", func(t *testing.T) {
		result, err := manager.BuildCommand("sweagent", "simple_tool", "")
		assert.NoError(t, err)
		expected := []string{"simple_tool"}
		assert.Equal(t, expected, result)
	})
}

func TestGlobalCommandStrategyManager(t *testing.T) {
	t.Run("global manager has sweagent strategy", func(t *testing.T) {
		strategy, err := GlobalCommandStrategyManager.GetStrategy("sweagent")
		assert.NoError(t, err)
		assert.NotNil(t, strategy)
		assert.Equal(t, "sweagent", strategy.GetStrategyName())
	})
}

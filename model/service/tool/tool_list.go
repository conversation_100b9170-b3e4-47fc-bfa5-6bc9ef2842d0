package tool

import (
	"context"
	"errors"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// ToolListInputData 查询工具列表输入数据
type ToolListInputData struct {
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
}

// ToolListOutputData 查询工具列表输出数据
type ToolListOutputData struct {
	MCPTools []types.MCPTool `json:"mcp_tools"`
}

// ToolList 查询工具列表服务
type ToolList struct {
	base.Service[ToolListInputData, ToolListOutputData]
	common *ToolCallCommon // 通用逻辑处理器
}

// NewToolList 创建查询工具列表服务实例
func NewToolList() *ToolList {
	return &ToolList{
		common: &ToolCallCommon{},
	}
}

// Execute 执行查询工具列表逻辑
func (s *ToolList) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	if input.SessionID == 0 && input.SessionCode == "" {
		return nil, nil, errcode.NewCustomErr(ctx, errors.New("session_id 和 session_code 必须提供一个"), errcode.UserInputError)
	}

	var session *dao_session.ObjSession
	if input.SessionCode != "" {
		session, err = dao_session.SessionBusinessIns.GetSessionByCode(ctx, input.SessionCode)
	} else {
		session, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
	}

	if err != nil {
		return nil, nil, err
	}

	if session.ContainerStatus == dao_session.ContainerStatusPending {
		return nil, nil, errcode.NewCustomErr(ctx, errors.New("容器正在初始化，请稍后"), errcode.K8sContainerCreateError)
	}
	if session.McpTools == nil {
		return nil, nil, errcode.NewCustomErr(ctx, errors.New("工具列表未初始化，请稍后"), errcode.K8sContainerCreateError)
	}
	// 将JSONData转换为MCPTool列表
	mcpTools, err := utils.ConvertJSONDataToMCPTools(&session.McpTools)
	if err != nil {
		return nil, nil, errcode.NewCustomErr(ctx, err, errcode.SysFileReadError, "解析MCP工具列表失败: "+err.Error())
	}

	return &ToolListOutputData{MCPTools: mcpTools}, nil, nil
}

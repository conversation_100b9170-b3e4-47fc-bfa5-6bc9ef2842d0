package tool

import (
	"context"
	"errors"
	"fmt"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	toolcommand "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool/command"
)

// ToolCallSyncInputData 同步工具调用输入数据
type ToolCallSyncInputData struct {
	ToolCallRequest
}

// ToolCallSyncOutputData 同步工具调用输出数据
type ToolCallSyncOutputData struct {
	ToolCallResponse
}

// ToolCallSync 同步工具调用服务
type ToolCallSync struct {
	base.Service[ToolCallSyncInputData, ToolCallSyncOutputData]
	common *ToolCallCommon // 通用逻辑处理器
}

// NewToolCallSync 创建同步工具调用服务实例
func NewToolCallSync() *ToolCallSync {
	return &ToolCallSync{
		common: &ToolCallCommon{},
	}
}

// Execute 执行同步工具调用逻辑
func (s *ToolCallSync) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	// 2. 验证请求参数
	if err := s.common.ValidateToolCallRequest(ctx, &input.ToolCallRequest); err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("工具调用参数验证失败: %v", err))
		return nil, nil, err
	}

	// 3. 验证Session有效性
	session, err := s.common.ValidateSession(ctx, input.SessionID, input.SessionCode)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("Session验证失败: %v", err))
		return nil, nil, err
	}

	// 4. 根据session是否有image_id判断执行路径
	if session.ImageID != "" {
		// 有image_id，走k8s_proxy_client直接执行路径（使用sweagent策略）
		response, err := s.executeToolViaK8sProxy(ctx, session, &input.ToolCallRequest)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("k8s_proxy执行工具失败: %v", err))
			return nil, nil, err
		}

		// 构建返回结果
		output := &ToolCallSyncOutputData{
			ToolCallResponse: *response,
		}
		return output, nil, nil
	} else {
		// 没有image_id，走原有的MCP工具调用路径（使用mcp策略）
		response, err := s.executeToolViaMCP(ctx, session, &input.ToolCallRequest)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("MCP执行工具失败: %v", err))
			return nil, nil, err
		}

		// 构建返回结果
		output := &ToolCallSyncOutputData{
			ToolCallResponse: *response,
		}
		return output, nil, nil
	}
}

// executeToolViaMCP 通过MCP路径执行工具调用（使用mcp策略）
func (s *ToolCallSync) executeToolViaMCP(ctx context.Context, session *dao_session.ObjSession, req *ToolCallRequest) (*ToolCallResponse, error) {
	// 校验call_id不重复
	existingTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, session.SessionID, req.CallID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("查询已存在任务失败: %v", err))
		return nil, err
	}
	if existingTask != nil {
		return nil, errcode.NewCustomErr(ctx, errors.New("call_id已存在"), errcode.ToolCallIDDuplicate)
	}

	// 创建工具调用任务记录
	_, err = s.common.CreateToolCallTask(ctx, req, session)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("创建工具调用任务失败: %v", err))
		return nil, err
	}

	// 等待任务完成（同步等待）
	task, err := s.common.WaitForTaskCompletionAndGetTask(ctx, session.SessionID, req.CallID, req.TimeoutSecs)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("等待任务完成失败: %v", err))
		return nil, err
	}

	// 直接组装MCP响应（不使用策略模式）
	return &ToolCallResponse{
		CallID:       req.CallID,
		Status:       string(task.Status),
		Result:       task.Result,
		ErrorMessage: task.ErrorMessage,
		OldEnvMD5:    task.OldEnvMd5,
		NewEnvMD5:    task.NewEnvMd5,
		OldEnvURL:    task.OldEnvUrl,
		NewEnvURL:    task.NewEnvUrl,
	}, nil
}

// executeToolViaK8sProxy 通过k8s_proxy_client直接执行工具调用
func (s *ToolCallSync) executeToolViaK8sProxy(ctx context.Context, session *dao_session.ObjSession, req *ToolCallRequest) (*ToolCallResponse, error) {

	// 1. 检查call_id是否重复（在tool_call_task表中）
	existingTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, session.SessionID, req.CallID)
	if err != nil {
		return nil, fmt.Errorf("查询已存在工具调用任务失败: %w", err)
	}
	if existingTask != nil {
		return nil, errcode.NewCustomErr(ctx, errors.New("call_id已存在"), errcode.ToolCallIDDuplicate)
	}

	// 2. 使用SweAgent策略构建执行命令
	cmdArgs, err := toolcommand.GlobalCallProcessStrategyManager.BuildCommand("sweagent", req.Name, req.Arguments)
	if err != nil {
		return nil, fmt.Errorf("构建执行命令失败: %w", err)
	}

	// 3. 创建工具调用任务记录，状态直接设为running
	now := time.Now()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    req.CallID,
		SessionID: session.SessionID,
		ToolName:  req.Name,
		Arguments: req.Arguments,
		Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
		StartedAt: &now,
	}

	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	if err != nil {
		return nil, err
	}

	// 4. 调用k8s_proxy_client执行命令
	// TODO: sourceType 后续根据栾伟给的文档修改
	execReq := &rpc_k8s_proxy.ExecCommandRequest{
		SourceType: "mcp",
		JobName:    session.JobID,
		Command:    cmdArgs,
	}

	execResp, err := rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, execReq)
	if err != nil {
		// 更新状态为失败
		_ = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateStatus(ctx, taskID, dao_tool_call_task.ToolCallTaskStatusFailed, err.Error())
		return nil, err
	}

	// 5. 使用SweAgent策略组装响应
	assemblyInput := &toolcommand.ResponseAssemblyInput{
		Request: &toolcommand.ToolCallRequest{
			SessionID:   req.SessionID,
			SessionCode: req.SessionCode,
			CallID:      req.CallID,
			Name:        req.Name,
			Arguments:   req.Arguments,
			TimeoutSecs: req.TimeoutSecs,
		},
		ExecResponse: execResp,
		CallID:       req.CallID,
		ToolName:     req.Name,
	}

	response, err := toolcommand.GlobalCallProcessStrategyManager.AssembleResponse(ctx, "sweagent", assemblyInput)
	if err != nil {
		// 更新状态为失败
		_ = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateStatus(ctx, taskID, dao_tool_call_task.ToolCallTaskStatusFailed, err.Error())
		return nil, fmt.Errorf("SweAgent策略组装响应失败: %w", err)
	}

	// 6. 更新任务状态并保存结果
	completedAt := time.Now()
	if execResp.Code == 0 {
		task.Status = dao_tool_call_task.ToolCallTaskStatusSuccess
	} else {
		task.Status = dao_tool_call_task.ToolCallTaskStatusFailed
	}
	task.Result = response.Result
	task.ErrorMessage = response.ErrorMessage
	task.CompletedAt = &completedAt

	err = dao_tool_call_task.ToolCallTaskBusinessIns.Update(ctx, task)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("更新工具调用任务结果失败: %v", err))
	}

	resource.LoggerService.Notice(ctx,
		fmt.Sprintf("k8s_proxy执行工具完成: session_id=%d, session_code=%s, call_id=%s, stdout_length=%d, stderr_length=%d",
			session.SessionID, session.SessionCode, req.CallID, len(execResp.Stdout), len(execResp.Stderr)))

	// 转换响应类型
	return &ToolCallResponse{
		CallID:       response.CallID,
		Status:       response.Status,
		Result:       response.Result,
		ErrorMessage: response.ErrorMessage,
		OldEnvMD5:    response.OldEnvMD5,
		NewEnvMD5:    response.NewEnvMD5,
		OldEnvURL:    response.OldEnvURL,
		NewEnvURL:    response.NewEnvURL,
	}, nil
}

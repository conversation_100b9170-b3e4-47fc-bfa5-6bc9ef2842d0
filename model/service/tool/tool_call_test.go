package tool_test

import (
	"context"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
}

func tearDownAll(t *testing.T) {
}

// TestValidateToolCallRequest_AutoGenerateCallID 测试CallID自动生成功能
func TestValidateToolCallRequest_AutoGenerateCallID(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tc := &tool.ToolCallCommon{}

	// 测试CallID为空时自动生成
	req := &tool.ToolCallRequest{
		SessionID: 1,
		Name:      "test_tool",
		Arguments: `{"test": "value"}`,
		CallID:    "", // 空CallID
	}

	err := tc.ValidateToolCallRequest(ctx, req)

	// 验证没有错误
	assert.NoError(t, err)

	// 验证CallID已被自动生成
	assert.NotEmpty(t, req.CallID)
	assert.NotEqual(t, "", req.CallID)

	// 验证生成的CallID是有效的UUID格式（36个字符，包含连字符）
	assert.Len(t, req.CallID, 36)
}

// TestValidateToolCallRequest_KeepExistingCallID 测试保持现有CallID
func TestValidateToolCallRequest_KeepExistingCallID(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tc := &tool.ToolCallCommon{}

	originalCallID := "user-provided-call-id"
	req := &tool.ToolCallRequest{
		SessionID: 1,
		Name:      "test_tool",
		Arguments: `{"test": "value"}`,
		CallID:    originalCallID,
	}

	err := tc.ValidateToolCallRequest(ctx, req)

	// 验证没有错误
	assert.NoError(t, err)

	// 验证CallID保持不变
	assert.Equal(t, originalCallID, req.CallID)
}

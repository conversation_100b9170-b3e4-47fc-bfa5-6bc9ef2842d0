package tool_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool"
)

// tearDownAllToolList extends the base tearDownAll with tool list specific cleanup
func tearDownAllToolList(t *testing.T) {
	// Clean up test data
	cleanupTestSessions(t)
}

// cleanupTestSessions 清理测试数据
func cleanupTestSessions(t *testing.T) {
	// Delete test sessions created during tests
	testSessionCodes := []string{
		"test_session_running",
		"test_session_pending",
		"test_session_failed",
		"test_session_no_tools",
		"test_session_malformed_tools",
	}

	for _, code := range testSessionCodes {
		session, err := dao_session.SessionBusinessIns.GetSessionByCode(ctx, code)
		if err == nil && session != nil {
			dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)
		}
	}
}

// createTestSession 创建测试会话数据
func createTestSession(t *testing.T, sessionCode string, status dao_session.ContainerStatus, mcpTools *dao_base.JSONData) *dao_session.ObjSession {
	now := time.Now()

	session := &dao_session.ObjSession{
		SessionCode:     sessionCode,
		JobID:           "test_job_" + sessionCode,
		EnvID:           1001,
		ServerIds:       dao_base.JSONArray[int64]{1, 2},
		ContainerStatus: status,
		TimeoutSeconds:  3600,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	}

	// Handle mcpTools - if nil, leave it as zero value (nil map)
	// If not nil, set it to the provided value
	if mcpTools != nil {
		session.McpTools = *mcpTools
	}
	// If mcpTools is nil, session.McpTools remains as zero value (nil map)

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	session.SessionID = sessionID
	return session
}

// createValidMCPTools 创建有效的MCP工具数据
func createValidMCPTools() *dao_base.JSONData {
	return &dao_base.JSONData{
		"tools": []interface{}{
			map[string]interface{}{
				"name":        "file_operations",
				"description": "文件操作工具",
				"parameters": map[string]interface{}{
					"path": map[string]interface{}{
						"type":        "string",
						"description": "文件路径",
						"required":    true,
					},
				},
			},
			map[string]interface{}{
				"name":        "web_search",
				"description": "网络搜索工具",
				"parameters": map[string]interface{}{
					"query": map[string]interface{}{
						"type":        "string",
						"description": "搜索关键词",
						"required":    true,
					},
				},
			},
		},
	}
}

// createMalformedMCPTools 创建格式错误的MCP工具数据
func createMalformedMCPTools() *dao_base.JSONData {
	return &dao_base.JSONData{
		"tools": "invalid_format", // 应该是数组，但这里是字符串
	}
}

// TestToolList_Execute_ValidParameters 测试有效参数
func TestToolList_Execute_ValidParameters(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	tests := []struct {
		name           string
		setupSession   func() *dao_session.ObjSession
		inputData      tool.ToolListInputData
		expectedTools  int
		expectError    bool
		expectedErrMsg string
	}{
		{
			name: "ValidSessionID_RunningStatus_WithTools",
			setupSession: func() *dao_session.ObjSession {
				sessionCode := fmt.Sprintf("test_session_running_%d", time.Now().UnixNano())
				return createTestSession(t, sessionCode, dao_session.ContainerStatusRunning, createValidMCPTools())
			},
			inputData: tool.ToolListInputData{
				SessionID: 0, // Will be set by setupSession
			},
			expectedTools: 2,
			expectError:   false,
		},
		{
			name: "ValidSessionCode_RunningStatus_WithTools",
			setupSession: func() *dao_session.ObjSession {
				sessionCode := fmt.Sprintf("test_session_running_code_%d", time.Now().UnixNano())
				return createTestSession(t, sessionCode, dao_session.ContainerStatusRunning, createValidMCPTools())
			},
			inputData: tool.ToolListInputData{
				SessionCode: "", // Will be set by setupSession
			},
			expectedTools: 2,
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test session
			session := tt.setupSession()
			defer dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)

			// Create a copy of input data to avoid modifying the original
			inputData := tt.inputData

			// Update input data with actual session ID or code
			if inputData.SessionID == 0 && inputData.SessionCode == "" {
				inputData.SessionID = session.SessionID
			} else if inputData.SessionCode == "" && inputData.SessionID == 0 {
				inputData.SessionCode = session.SessionCode
			} else if inputData.SessionCode == "" {
				inputData.SessionCode = session.SessionCode
			}

			// Create service instance
			service := tool.NewToolList()
			service.InputData = &inputData

			// Execute service
			result, ext, err := service.Execute(ctx, nil)

			if tt.expectError {
				assert.Error(t, err)
				if tt.expectedErrMsg != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.Nil(t, ext)
				assert.NotNil(t, result)

				output, ok := result.(*tool.ToolListOutputData)
				assert.True(t, ok)
				assert.Len(t, output.MCPTools, tt.expectedTools)

				// Verify tool structure
				if len(output.MCPTools) > 0 {
					firstTool := output.MCPTools[0]
					assert.NotEmpty(t, firstTool.Name)
					assert.NotEmpty(t, firstTool.Description)
					assert.NotNil(t, firstTool.Parameters)
				}
			}
		})
	}
}

// TestToolList_Execute_InvalidParameters 测试无效参数
func TestToolList_Execute_InvalidParameters(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	tests := []struct {
		name            string
		inputData       tool.ToolListInputData
		expectedErrCode int
		expectedErrMsg  string
	}{
		{
			name: "MissingBothSessionIDAndCode",
			inputData: tool.ToolListInputData{
				SessionID:   0,
				SessionCode: "",
			},
			expectedErrCode: errcode.UserInputError,
			expectedErrMsg:  "session_id 和 session_code 必须提供一个",
		},
		{
			name: "NonExistentSessionID",
			inputData: tool.ToolListInputData{
				SessionID: 999999,
			},
			expectedErrCode: errcode.SessionNotFound,
		},
		{
			name: "NonExistentSessionCode",
			inputData: tool.ToolListInputData{
				SessionCode: "non_existent_session",
			},
			expectedErrCode: errcode.SessionNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := tool.NewToolList()
			service.InputData = &tt.inputData

			result, ext, err := service.Execute(ctx, nil)

			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Nil(t, ext)

			isCustomErr, code, _ := errcode.AssertCustomError(err, tt.expectedErrCode, tt.expectedErrMsg)
			assert.True(t, isCustomErr)
			assert.Equal(t, tt.expectedErrCode, code)
		})
	}
}

// TestToolList_Execute_SessionStatusEdgeCases 测试会话状态边界情况
func TestToolList_Execute_SessionStatusEdgeCases(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	tests := []struct {
		name            string
		containerStatus dao_session.ContainerStatus
		mcpTools        *dao_base.JSONData
		expectedErrCode int
		expectedErrMsg  string
	}{
		{
			name:            "PendingStatus",
			containerStatus: dao_session.ContainerStatusPending,
			mcpTools:        createValidMCPTools(),
			expectedErrCode: errcode.K8sContainerCreateError,
			expectedErrMsg:  "容器正在初始化，请稍后",
		},
		{
			name:            "RunningStatus_NoTools",
			containerStatus: dao_session.ContainerStatusRunning,
			mcpTools:        nil,
			expectedErrCode: errcode.K8sContainerCreateError,
			expectedErrMsg:  "工具列表未初始化，请稍后",
		},
		{
			name:            "FailedStatus_WithTools",
			containerStatus: dao_session.ContainerStatusFailed,
			mcpTools:        createValidMCPTools(),
			expectedErrCode: 0, // Should succeed even if container failed
		},
		{
			name:            "StoppedStatus_WithTools",
			containerStatus: dao_session.ContainerStatusStopped,
			mcpTools:        createValidMCPTools(),
			expectedErrCode: 0, // Should succeed even if container stopped
		},
		{
			name:            "TimeoutStatus_WithTools",
			containerStatus: dao_session.ContainerStatusTimeout,
			mcpTools:        createValidMCPTools(),
			expectedErrCode: 0, // Should succeed even if container timed out
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test session with specific status and unique session code
			sessionCode := fmt.Sprintf("test_session_status_%s_%d", tt.name, time.Now().UnixNano())
			session := createTestSession(t, sessionCode, tt.containerStatus, tt.mcpTools)
			defer dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)

			// Create service instance
			service := tool.NewToolList()
			service.InputData = &tool.ToolListInputData{
				SessionID: session.SessionID,
			}

			// Execute service
			result, ext, err := service.Execute(ctx, nil)

			if tt.expectedErrCode != 0 {
				assert.Error(t, err)
				isCustomErr, code, _ := errcode.AssertCustomError(err, tt.expectedErrCode, tt.expectedErrMsg)
				assert.True(t, isCustomErr)
				assert.Equal(t, tt.expectedErrCode, code)
			} else {
				assert.NoError(t, err)
				assert.Nil(t, ext)
				assert.NotNil(t, result)

				output, ok := result.(*tool.ToolListOutputData)
				assert.True(t, ok)
				assert.NotNil(t, output.MCPTools)
			}
		})
	}
}

// TestToolList_Execute_MCPToolsDataFormats 测试MCP工具数据格式
func TestToolList_Execute_MCPToolsDataFormats(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	tests := []struct {
		name            string
		mcpTools        *dao_base.JSONData
		expectedErrCode int
		expectedErrMsg  string
		expectedTools   int
	}{
		{
			name:          "ValidToolsFormat",
			mcpTools:      createValidMCPTools(),
			expectedTools: 2,
		},
		{
			name: "EmptyToolsArray",
			mcpTools: &dao_base.JSONData{
				"tools": []interface{}{},
			},
			expectedTools: 0,
		},
		{
			name:            "MalformedToolsFormat",
			mcpTools:        createMalformedMCPTools(),
			expectedErrCode: errcode.SysFileReadError,
			expectedErrMsg:  "解析MCP工具列表失败",
		},
		{
			name: "MissingToolsField",
			mcpTools: &dao_base.JSONData{
				"other_field": "value",
			},
			expectedTools: 0,
		},
		{
			name: "PartiallyValidTools",
			mcpTools: &dao_base.JSONData{
				"tools": []interface{}{
					map[string]interface{}{
						"name":        "valid_tool",
						"description": "有效工具",
						"parameters":  map[string]interface{}{},
					},
					"invalid_tool_format", // 无效格式，应该被跳过
					map[string]interface{}{
						"name":        "another_valid_tool",
						"description": "另一个有效工具",
						"parameters":  map[string]interface{}{},
					},
				},
			},
			expectedTools: 2, // 只有有效的工具会被包含
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test session with specific MCP tools data and unique session code
			sessionCode := fmt.Sprintf("test_session_tools_%s_%d", tt.name, time.Now().UnixNano())
			session := createTestSession(t, sessionCode, dao_session.ContainerStatusRunning, tt.mcpTools)
			defer dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)

			// Create service instance
			service := tool.NewToolList()
			service.InputData = &tool.ToolListInputData{
				SessionID: session.SessionID,
			}

			// Execute service
			result, ext, err := service.Execute(ctx, nil)

			if tt.expectedErrCode != 0 {
				assert.Error(t, err)
				isCustomErr, code, _ := errcode.AssertCustomError(err, tt.expectedErrCode, tt.expectedErrMsg)
				assert.True(t, isCustomErr)
				assert.Equal(t, tt.expectedErrCode, code)
			} else {
				assert.NoError(t, err)
				assert.Nil(t, ext)
				assert.NotNil(t, result)

				output, ok := result.(*tool.ToolListOutputData)
				assert.True(t, ok)
				assert.Len(t, output.MCPTools, tt.expectedTools)

				// Verify each tool has required fields
				for _, tool := range output.MCPTools {
					assert.NotEmpty(t, tool.Name)
					assert.NotEmpty(t, tool.Description)
					assert.NotNil(t, tool.Parameters)
				}
			}
		})
	}
}

// TestToolList_Execute_BoundaryValues 测试边界值
func TestToolList_Execute_BoundaryValues(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	tests := []struct {
		name           string
		inputData      tool.ToolListInputData
		expectedErrMsg string
	}{
		{
			name: "ZeroSessionID",
			inputData: tool.ToolListInputData{
				SessionID: 0,
			},
			expectedErrMsg: "session_id 和 session_code 必须提供一个",
		},
		{
			name: "NegativeSessionID",
			inputData: tool.ToolListInputData{
				SessionID: -1,
			},
			// Negative ID should be treated as valid input but will fail at DB level
		},
		{
			name: "EmptySessionCode",
			inputData: tool.ToolListInputData{
				SessionCode: "",
			},
			expectedErrMsg: "session_id 和 session_code 必须提供一个",
		},
		{
			name: "WhitespaceSessionCode",
			inputData: tool.ToolListInputData{
				SessionCode: "   ",
			},
			// Should be treated as valid but non-existent
		},
		{
			name: "VeryLongSessionCode",
			inputData: tool.ToolListInputData{
				SessionCode: fmt.Sprintf("very_long_session_code_%s_%d", string(make([]byte, 500)), time.Now().UnixNano()), // Very long unique string
			},
			// Should be treated as valid but non-existent
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := tool.NewToolList()
			service.InputData = &tt.inputData

			result, ext, err := service.Execute(ctx, nil)

			if tt.expectedErrMsg != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErrMsg)
			} else {
				// For cases without specific error message, we expect some kind of error
				// (either validation error or DB error)
				assert.Error(t, err)
			}
			assert.Nil(t, result)
			assert.Nil(t, ext)
		})
	}
}

// TestToolList_Execute_ConcurrentAccess 测试并发访问
func TestToolList_Execute_ConcurrentAccess(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	// Create a test session
	sessionCode := fmt.Sprintf("test_session_concurrent_%d", time.Now().UnixNano())
	session := createTestSession(t, sessionCode, dao_session.ContainerStatusRunning, createValidMCPTools())
	defer dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)

	// Number of concurrent goroutines
	concurrency := 10
	results := make(chan error, concurrency)

	// Launch concurrent requests
	for i := 0; i < concurrency; i++ {
		go func() {
			service := tool.NewToolList()
			service.InputData = &tool.ToolListInputData{
				SessionID: session.SessionID,
			}

			result, ext, err := service.Execute(ctx, nil)

			if err != nil {
				results <- err
				return
			}

			if result == nil {
				results <- assert.AnError
				return
			}

			output, ok := result.(*tool.ToolListOutputData)
			if !ok {
				results <- assert.AnError
				return
			}

			if len(output.MCPTools) != 2 {
				results <- assert.AnError
				return
			}

			if ext != nil {
				results <- assert.AnError
				return
			}

			results <- nil
		}()
	}

	// Collect results
	for i := 0; i < concurrency; i++ {
		err := <-results
		assert.NoError(t, err, "Concurrent request %d failed", i)
	}
}

// TestToolList_Execute_ResponseFormat 测试响应格式
func TestToolList_Execute_ResponseFormat(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	// Create test session with complex tools
	complexTools := &dao_base.JSONData{
		"tools": []interface{}{
			map[string]interface{}{
				"name":        "complex_tool",
				"description": "复杂工具测试",
				"parameters": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"file_path": map[string]interface{}{
							"type":        "string",
							"description": "文件路径",
							"required":    true,
						},
						"options": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"recursive": map[string]interface{}{
									"type":        "boolean",
									"description": "是否递归",
									"default":     false,
								},
								"max_depth": map[string]interface{}{
									"type":        "integer",
									"description": "最大深度",
									"minimum":     1,
									"maximum":     10,
								},
							},
						},
					},
					"required": []string{"file_path"},
				},
			},
		},
	}

	sessionCode := fmt.Sprintf("test_session_response_format_%d", time.Now().UnixNano())
	session := createTestSession(t, sessionCode, dao_session.ContainerStatusRunning, complexTools)
	defer dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)

	service := tool.NewToolList()
	service.InputData = &tool.ToolListInputData{
		SessionID: session.SessionID,
	}

	result, ext, err := service.Execute(ctx, nil)

	// Verify response structure
	assert.NoError(t, err)
	assert.Nil(t, ext)
	assert.NotNil(t, result)

	output, ok := result.(*tool.ToolListOutputData)
	assert.True(t, ok)
	assert.Len(t, output.MCPTools, 1)

	// Verify complex tool structure
	tool := output.MCPTools[0]
	assert.Equal(t, "complex_tool", tool.Name)
	assert.Equal(t, "复杂工具测试", tool.Description)
	assert.NotNil(t, tool.Parameters)

	// Verify nested parameters structure
	params := tool.Parameters
	assert.Equal(t, "object", params["type"])

	properties, ok := params["properties"].(map[string]interface{})
	assert.True(t, ok)
	assert.Contains(t, properties, "file_path")
	assert.Contains(t, properties, "options")
}

// TestToolList_Execute_LargeDataSet 测试大数据集
func TestToolList_Execute_LargeDataSet(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	// Create a large number of tools
	toolsArray := make([]interface{}, 100)
	for i := 0; i < 100; i++ {
		toolsArray[i] = map[string]interface{}{
			"name":        fmt.Sprintf("tool_%d", i),
			"description": fmt.Sprintf("工具 %d 的描述", i),
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"param1": map[string]interface{}{
						"type":        "string",
						"description": fmt.Sprintf("参数1 for tool %d", i),
					},
				},
			},
		}
	}

	largeTools := &dao_base.JSONData{
		"tools": toolsArray,
	}

	sessionCode := fmt.Sprintf("test_session_large_dataset_%d", time.Now().UnixNano())
	session := createTestSession(t, sessionCode, dao_session.ContainerStatusRunning, largeTools)
	defer dao_session.SessionBusinessIns.Delete(ctx, session.SessionID)

	service := tool.NewToolList()
	service.InputData = &tool.ToolListInputData{
		SessionID: session.SessionID,
	}

	result, ext, err := service.Execute(ctx, nil)

	// Verify large dataset handling
	assert.NoError(t, err)
	assert.Nil(t, ext)
	assert.NotNil(t, result)

	output, ok := result.(*tool.ToolListOutputData)
	assert.True(t, ok)
	assert.Len(t, output.MCPTools, 100)

	// Verify first and last tools
	assert.Equal(t, "tool_0", output.MCPTools[0].Name)
	assert.Equal(t, "tool_99", output.MCPTools[99].Name)
}

// TestToolList_Execute_SessionCodePriority 测试SessionCode优先级
func TestToolList_Execute_SessionCodePriority(t *testing.T) {
	setUpAll(t)
	defer tearDownAllToolList(t)

	// Create two test sessions
	sessionCode1 := fmt.Sprintf("test_session_priority_1_%d", time.Now().UnixNano())
	session1 := createTestSession(t, sessionCode1, dao_session.ContainerStatusRunning, createValidMCPTools())
	defer dao_session.SessionBusinessIns.Delete(ctx, session1.SessionID)

	sessionCode2 := fmt.Sprintf("test_session_priority_2_%d", time.Now().UnixNano())
	session2 := createTestSession(t, sessionCode2, dao_session.ContainerStatusRunning, createValidMCPTools())
	defer dao_session.SessionBusinessIns.Delete(ctx, session2.SessionID)

	// Test that SessionCode takes priority over SessionID
	service := tool.NewToolList()
	service.InputData = &tool.ToolListInputData{
		SessionID:   session1.SessionID,   // This should be ignored
		SessionCode: session2.SessionCode, // This should be used
	}

	result, ext, err := service.Execute(ctx, nil)

	assert.NoError(t, err)
	assert.Nil(t, ext)
	assert.NotNil(t, result)

	output, ok := result.(*tool.ToolListOutputData)
	assert.True(t, ok)
	assert.Len(t, output.MCPTools, 2)
}

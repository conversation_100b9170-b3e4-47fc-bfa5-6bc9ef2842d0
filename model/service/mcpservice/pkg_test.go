package service_test

import (
	"context"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/mcpservice"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll(t *testing.T) {}

func TestRegister_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 设置测试数据
	inputData := &service.RegisterMcpServerInputData{
		ServerName:           "test-server",
		Command:              "python",
		Args:                 []string{"app.py", "--port", "8080"},
		Description:          "测试MCP服务器",
		MockMcpServerCodeURL: "https://example.com/server-code.zip",
	}

	// 创建服务实例
	ins := &service.RegisterMcpSever{}
	ins.InputData = inputData
	// 执行测试
	ctx := context.Background()

	ret, ext, err := ins.Execute(ctx, nil)

	// 验证结果
	require.NoError(t, err)
	assert.Nil(t, ext)

	response, ok := ret.(*service.RegisterMcpServerOutputData)
	require.True(t, ok)
	assert.Greater(t, response.ServerID, int64(0))
	assert.Equal(t, "test-server", response.ServerName)
	assert.Equal(t, "registed", response.Status)

}

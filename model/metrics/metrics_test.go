package metrics

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestSessionMetricsRegistration(t *testing.T) {
	// Test that metrics can be registered without panicking
	// We can't easily test the actual registration with gmetrics in unit tests
	// since it uses a global registry, so we'll just verify the functions don't panic

	assert.NotPanics(t, func() {
		RegisterSessionMetrics()
	}, "RegisterSessionMetrics should not panic")
}

func TestToolCallMetricsRegistration(t *testing.T) {
	// Test that metrics can be registered without panicking
	assert.NotPanics(t, func() {
		RegisterToolCallMetrics()
	}, "RegisterToolCallMetrics should not panic")
}

func TestSessionStatusMetricsUpdate(t *testing.T) {
	// Test that updating session status metrics doesn't panic
	assert.NotPanics(t, func() {
		UpdateSessionStatusMetrics(5, 3, 2, 1, 0)
	}, "UpdateSessionStatusMetrics should not panic")
}

func TestToolCallStatusMetricsUpdate(t *testing.T) {
	// Test that updating tool call status metrics doesn't panic
	assert.NotPanics(t, func() {
		UpdateToolCallStatusMetrics(10, 5, 15, 2, 1)
	}, "UpdateToolCallStatusMetrics should not panic")
}

func TestSessionTransitionRecording(t *testing.T) {
	// Test that recording functions don't panic
	assert.NotPanics(t, func() {
		RecordPendingToRunningDuration(2 * time.Second)
		RecordRunningToStoppedDuration(30 * time.Second)
		RecordSessionActiveDuration(45 * time.Second)
	}, "Session transition recording should not panic")
}

func TestToolCallTransitionRecording(t *testing.T) {
	// Test that recording functions don't panic
	assert.NotPanics(t, func() {
		RecordPendingToRunningToolCallDuration(100 * time.Millisecond)
		RecordRunningToSuccessToolCallDuration(5 * time.Second)
		RecordToolCallExecution("filesystem__read_file", "success", 3*time.Second)
	}, "Tool call transition recording should not panic")
}

func TestInstrumentationHelpers(t *testing.T) {
	// Test that instrumentation helper functions don't panic
	assert.NotPanics(t, func() {
		RecordSessionCreated()
		RecordSessionPending()
		RecordSessionRunning()
		RecordToolCallCreated()
		RecordToolCallSuccess()
	}, "Instrumentation helpers should not panic")
}

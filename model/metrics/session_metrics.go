package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
)

var (
	// Session status gauges
	sessionPendingGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_session_pending_total",
		Help: "Current number of sessions in pending status",
	})

	sessionRunningGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_session_running_total",
		Help: "Current number of sessions in running status",
	})

	sessionStoppedGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_session_stopped_total",
		Help: "Current number of sessions in stopped status",
	})

	sessionTimeoutGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_session_timeout_total",
		Help: "Current number of sessions in timeout status",
	})

	sessionFailedGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_session_failed_total",
		Help: "Current number of sessions in failed status",
	})

	// Session transition time histograms
	pendingToRunningHistogram = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "mcp_session_pending_to_running_duration_seconds",
		Help:    "Histogram of time taken for sessions to transition from pending to running",
		Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300, 600},
	})

	runningToStoppedHistogram = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "mcp_session_running_to_stopped_duration_seconds",
		Help:    "Histogram of time taken for sessions to transition from running to stopped",
		Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300, 600, 1800, 3600},
	})

	runningToTimeoutHistogram = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "mcp_session_running_to_timeout_duration_seconds",
		Help:    "Histogram of time taken for sessions to transition from running to timeout",
		Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300, 600, 1800, 3600},
	})

	sessionActiveDurationHistogram = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "mcp_session_active_duration_seconds",
		Help:    "Histogram of total session active time (from start to completion)",
		Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300, 600, 1800, 3600, 7200},
	})

	// Session status counters (for rate calculations)
	sessionStatusChangeCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "mcp_session_status_change_total",
			Help: "Total number of session status changes by status",
		},
		[]string{"status"},
	)

	// Long-running session gauge for distributed monitoring
	longRunningSessionsGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_session_long_running_total",
		Help: "Current number of sessions running longer than 10 minutes",
	})
)

// RegisterSessionMetrics registers all session-related metrics with Prometheus
func RegisterSessionMetrics() {
	// Get the service registry from gmetrics
	serviceRegistry := gmetrics.Default().ServiceRegistry

	// Register all metrics
	serviceRegistry.MustRegister(sessionPendingGauge)
	serviceRegistry.MustRegister(sessionRunningGauge)
	serviceRegistry.MustRegister(sessionStoppedGauge)
	serviceRegistry.MustRegister(sessionTimeoutGauge)
	serviceRegistry.MustRegister(sessionFailedGauge)

	serviceRegistry.MustRegister(pendingToRunningHistogram)
	serviceRegistry.MustRegister(runningToStoppedHistogram)
	serviceRegistry.MustRegister(runningToTimeoutHistogram)
	serviceRegistry.MustRegister(sessionActiveDurationHistogram)

	serviceRegistry.MustRegister(sessionStatusChangeCounter)
	serviceRegistry.MustRegister(longRunningSessionsGauge)
}

// UpdateSessionStatusMetrics updates the session status gauges based on current counts
func UpdateSessionStatusMetrics(pendingCount, runningCount, stoppedCount, timeoutCount, failedCount int) {
	sessionPendingGauge.Set(float64(pendingCount))
	sessionRunningGauge.Set(float64(runningCount))
	sessionStoppedGauge.Set(float64(stoppedCount))
	sessionTimeoutGauge.Set(float64(timeoutCount))
	sessionFailedGauge.Set(float64(failedCount))
}

// RecordSessionStatusChange records a session status change and increments the counter
func RecordSessionStatusChange(status string) {
	sessionStatusChangeCounter.WithLabelValues(status).Inc()
}

// RecordPendingToRunningDuration records the time taken for a session to transition from pending to running
func RecordPendingToRunningDuration(duration time.Duration) {
	pendingToRunningHistogram.Observe(duration.Seconds())
}

// RecordRunningToStoppedDuration records the time taken for a session to transition from running to stopped
func RecordRunningToStoppedDuration(duration time.Duration) {
	runningToStoppedHistogram.Observe(duration.Seconds())
}

// RecordRunningToTimeoutDuration records the time taken for a session to transition from running to timeout
func RecordRunningToTimeoutDuration(duration time.Duration) {
	runningToTimeoutHistogram.Observe(duration.Seconds())
}

// RecordSessionActiveDuration records the total active time of a session
func RecordSessionActiveDuration(duration time.Duration) {
	sessionActiveDurationHistogram.Observe(duration.Seconds())
}

// UpdateLongRunningSessionsCount updates the count of long-running sessions
func UpdateLongRunningSessionsCount(count int64) {
	longRunningSessionsGauge.Set(float64(count))
}

// RecordSessionStatusTransition records a session status transition with timing
func RecordSessionStatusTransition(fromStatus, toStatus string, transitionTime time.Duration, sessionStartTime *time.Time) {
	// Record the status change
	RecordSessionStatusChange(toStatus)

	// Record transition timing based on status change
	switch {
	case fromStatus == "pending" && toStatus == "running":
		RecordPendingToRunningDuration(transitionTime)
	case fromStatus == "running" && toStatus == "stopped":
		RecordRunningToStoppedDuration(transitionTime)
		if sessionStartTime != nil {
			totalDuration := time.Since(*sessionStartTime)
			RecordSessionActiveDuration(totalDuration)
		}
	case fromStatus == "running" && toStatus == "timeout":
		RecordRunningToTimeoutDuration(transitionTime)
		if sessionStartTime != nil {
			totalDuration := time.Since(*sessionStartTime)
			RecordSessionActiveDuration(totalDuration)
		}
	case toStatus == "failed":
		if sessionStartTime != nil {
			totalDuration := time.Since(*sessionStartTime)
			RecordSessionActiveDuration(totalDuration)
		}
	}
}

package metrics

import (
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
)

// InstrumentSessionStatusChange is a helper function to record session status changes
// This can be called from business logic to record metrics
func InstrumentSessionStatusChange(sessionID int64, fromStatus, toStatus string, sessionStartTime *time.Time) {
	// Calculate transition time (for now, we'll use a placeholder)
	// In a real implementation, you might want to track the time when the previous status was set
	transitionTime := time.Second // Placeholder

	RecordSessionStatusTransition(fromStatus, toStatus, transitionTime, sessionStartTime)
}

// InstrumentToolCallStatusChange is a helper function to record tool call status changes
// This can be called from business logic to record metrics
func InstrumentToolCallStatusChange(taskID int64, fromStatus, toStatus, toolName string, startTime *time.Time) {
	var transitionTime time.Duration
	if startTime != nil {
		transitionTime = time.Since(*startTime)
	}

	RecordToolCallStatusTransition(fromStatus, toStatus, toolName, transitionTime)
}

// These functions provide a simple interface for business logic to record metrics
// without requiring complex integration

// RecordSessionCreated records when a new session is created
func RecordSessionCreated() {
	RecordSessionStatusChange("init")
}

// RecordSessionPending records when a session becomes pending
func RecordSessionPending() {
	RecordSessionStatusChange("pending")
}

// RecordSessionRunning records when a session becomes running
func RecordSessionRunning() {
	RecordSessionStatusChange("running")
}

// RecordSessionStopped records when a session is stopped
func RecordSessionStopped() {
	RecordSessionStatusChange("stopped")
}

// RecordSessionTimeout records when a session times out
func RecordSessionTimeout() {
	RecordSessionStatusChange("timeout")
}

// RecordSessionFailed records when a session fails
func RecordSessionFailed() {
	RecordSessionStatusChange("failed")
}

// RecordToolCallCreated records when a new tool call is created
func RecordToolCallCreated() {
	RecordToolCallStatusChange("pending")
}

// RecordToolCallRunning records when a tool call becomes running
func RecordToolCallRunning() {
	RecordToolCallStatusChange("running")
}

// RecordToolCallSuccess records when a tool call succeeds
func RecordToolCallSuccess() {
	RecordToolCallStatusChange("success")
}

// RecordToolCallFailed records when a tool call fails
func RecordToolCallFailed() {
	RecordToolCallStatusChange("failed")
}

// RecordToolCallTimeout records when a tool call times out
func RecordToolCallTimeout() {
	RecordToolCallStatusChange("timeout")
}

// MetricsRecorderImpl implements the MetricsRecorder interface
type MetricsRecorderImpl struct{}

// RecordSessionStatusChange implements MetricsRecorder interface
func (m *MetricsRecorderImpl) RecordSessionStatusChange(status string) {
	RecordSessionStatusChange(status)
}

// RecordToolCallStatusChange implements MetricsRecorder interface
func (m *MetricsRecorderImpl) RecordToolCallStatusChange(status string) {
	RecordToolCallStatusChange(status)
}

// RecordSessionStatusTransition implements MetricsRecorder interface
func (m *MetricsRecorderImpl) RecordSessionStatusTransition(fromStatus, toStatus string, transitionTime time.Duration, sessionStartTime *time.Time) {
	RecordSessionStatusTransition(fromStatus, toStatus, transitionTime, sessionStartTime)
}

// RecordToolCallStatusTransition implements MetricsRecorder interface
func (m *MetricsRecorderImpl) RecordToolCallStatusTransition(fromStatus, toStatus, toolName string, transitionTime time.Duration) {
	RecordToolCallStatusTransition(fromStatus, toStatus, toolName, transitionTime)
}

// RegisterMetricsRecorder registers the metrics recorder with the helper
func RegisterMetricsRecorder() {
	metrics_helper.SetMetricsRecorder(&MetricsRecorderImpl{})
}

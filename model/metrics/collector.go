package metrics

import (
	"context"
	"fmt"
	"sync"
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// MetricsCollector periodically collects metrics from the database
type MetricsCollector struct {
	mu       sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	interval time.Duration
	running  bool
}

var (
	metricsCollector *MetricsCollector
)

// InitMetricsCollector initializes the global metrics collector
func InitMetricsCollector(interval time.Duration) {
	ctx, cancel := context.WithCancel(context.Background())

	metricsCollector = &MetricsCollector{
		ctx:      ctx,
		cancel:   cancel,
		interval: interval,
		running:  false,
	}
}

// GetMetricsCollector returns the global metrics collector instance
func GetMetricsCollector() *MetricsCollector {
	return metricsCollector
}

// Start starts the metrics collection loop
func (mc *MetricsCollector) Start() error {
	mc.mu.Lock()
	defer mc.mu.Unlock()

	if mc.running {
		return fmt.Errorf("metrics collector is already running")
	}

	mc.running = true
	go mc.collectLoop()

	resource.LoggerBg.Notice(mc.ctx, fmt.Sprintf("Metrics collector started with interval %v", mc.interval))
	return nil
}

// Stop stops the metrics collection loop
func (mc *MetricsCollector) Stop() {
	mc.mu.Lock()
	defer mc.mu.Unlock()

	if !mc.running {
		return
	}

	mc.cancel()
	mc.running = false
	resource.LoggerBg.Notice(mc.ctx, "Metrics collector stopped")
}

// collectLoop runs the metrics collection loop
func (mc *MetricsCollector) collectLoop() {
	ticker := time.NewTicker(mc.interval)
	defer ticker.Stop()

	// Collect metrics immediately on start
	mc.collectMetrics()

	for {
		select {
		case <-mc.ctx.Done():
			return
		case <-ticker.C:
			mc.collectMetrics()
		}
	}
}

// collectMetrics collects all metrics from the database
func (mc *MetricsCollector) collectMetrics() {
	// Collect session metrics
	mc.collectSessionMetrics()

	// Collect tool call metrics
	mc.collectToolCallMetrics()

	// Collect additional metrics for distributed monitoring
	mc.collectLongRunningMetrics()
}

// collectSessionMetrics collects session status counts from the database
func (mc *MetricsCollector) collectSessionMetrics() {
	ctx := mc.ctx

	// Count sessions by status
	statusCounts := make(map[dao_session.ContainerStatus]int)

	// Query all active sessions grouped by status
	// For distributed stateless deployment, we query the database as single source of truth
	var results []struct {
		ContainerStatus dao_session.ContainerStatus `gorm:"column:container_status"`
		Count           int                         `gorm:"column:count"`
	}

	err := dao.GetDb(ctx).Model(&dao_session.ObjSession{}).
		Select("container_status, COUNT(*) as count").
		Group("container_status").
		Find(&results).Error

	if err != nil {
		resource.LoggerBg.Warning(ctx, fmt.Sprintf("Failed to collect session metrics: %v", err))
		return
	}

	// Initialize all counts to 0
	statusCounts[dao_session.ContainerStatusPending] = 0
	statusCounts[dao_session.ContainerStatusRunning] = 0
	statusCounts[dao_session.ContainerStatusStopped] = 0
	statusCounts[dao_session.ContainerStatusTimeout] = 0
	statusCounts[dao_session.ContainerStatusFailed] = 0

	// Update counts from query results
	for _, result := range results {
		statusCounts[result.ContainerStatus] = result.Count
	}

	// Update Prometheus metrics
	UpdateSessionStatusMetrics(
		statusCounts[dao_session.ContainerStatusPending],
		statusCounts[dao_session.ContainerStatusRunning],
		statusCounts[dao_session.ContainerStatusStopped],
		statusCounts[dao_session.ContainerStatusTimeout],
		statusCounts[dao_session.ContainerStatusFailed],
	)

	// Log metrics for debugging in distributed environment
	resource.LoggerBg.Debug(ctx, fmt.Sprintf("Session metrics collected - Pending: %d, Running: %d, Stopped: %d, Timeout: %d, Failed: %d",
		statusCounts[dao_session.ContainerStatusPending],
		statusCounts[dao_session.ContainerStatusRunning],
		statusCounts[dao_session.ContainerStatusStopped],
		statusCounts[dao_session.ContainerStatusTimeout],
		statusCounts[dao_session.ContainerStatusFailed]))
}

// collectToolCallMetrics collects tool call status counts from the database
func (mc *MetricsCollector) collectToolCallMetrics() {
	ctx := mc.ctx

	// Count tool calls by status
	statusCounts := make(map[dao_tool_call_task.ToolCallTaskStatus]int)

	// Query all active tool calls grouped by status
	// For distributed stateless deployment, we query the database as single source of truth
	var results []struct {
		Status dao_tool_call_task.ToolCallTaskStatus `gorm:"column:tool_call_status"`
		Count  int                                   `gorm:"column:count"`
	}

	err := dao.GetDb(ctx).Model(&dao_tool_call_task.ObjToolCallTask{}).
		Select("tool_call_status, COUNT(*) as count").
		Group("tool_call_status").
		Find(&results).Error

	if err != nil {
		resource.LoggerBg.Warning(ctx, fmt.Sprintf("Failed to collect tool call metrics: %v", err))
		return
	}

	// Initialize all counts to 0
	statusCounts[dao_tool_call_task.ToolCallTaskStatusPending] = 0
	statusCounts[dao_tool_call_task.ToolCallTaskStatusRunning] = 0
	statusCounts[dao_tool_call_task.ToolCallTaskStatusSuccess] = 0
	statusCounts[dao_tool_call_task.ToolCallTaskStatusFailed] = 0
	statusCounts[dao_tool_call_task.ToolCallTaskStatusTimeout] = 0

	// Update counts from query results
	for _, result := range results {
		statusCounts[result.Status] = result.Count
	}

	// Update Prometheus metrics
	UpdateToolCallStatusMetrics(
		statusCounts[dao_tool_call_task.ToolCallTaskStatusPending],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusRunning],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusSuccess],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusFailed],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusTimeout],
	)

	// Log metrics for debugging in distributed environment
	resource.LoggerBg.Debug(ctx, fmt.Sprintf("Tool call metrics collected - Pending: %d, Running: %d, Success: %d, Failed: %d, Timeout: %d",
		statusCounts[dao_tool_call_task.ToolCallTaskStatusPending],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusRunning],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusSuccess],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusFailed],
		statusCounts[dao_tool_call_task.ToolCallTaskStatusTimeout]))
}

// collectLongRunningMetrics collects metrics for long-running sessions and tool calls
// This is important for distributed stateless deployment to detect stuck processes
func (mc *MetricsCollector) collectLongRunningMetrics() {
	ctx := mc.ctx
	now := time.Now()

	// Check for long-running sessions (running for more than 10 minutes)
	var longRunningSessions int64
	err := dao.GetDb(ctx).Model(&dao_session.ObjSession{}).
		Where("container_status = ? AND created_at < ?",
			dao_session.ContainerStatusRunning, now.Add(-10*time.Minute)).
		Count(&longRunningSessions).Error

	if err != nil {
		resource.LoggerBg.Warning(ctx, fmt.Sprintf("Failed to collect long-running session metrics: %v", err))
	} else {
		// Update the gauge for long-running sessions
		UpdateLongRunningSessionsCount(longRunningSessions)
		if longRunningSessions > 0 {
			resource.LoggerBg.Warning(ctx, fmt.Sprintf("Found %d long-running sessions (>10 minutes)", longRunningSessions))
		}
	}

	// Check for long-pending tool calls (pending for more than 5 minutes)
	var longPendingToolCalls int64
	err = dao.GetDb(ctx).Model(&dao_tool_call_task.ObjToolCallTask{}).
		Where("tool_call_status = ? AND created_at < ?",
			dao_tool_call_task.ToolCallTaskStatusPending, now.Add(-5*time.Minute)).
		Count(&longPendingToolCalls).Error

	if err != nil {
		resource.LoggerBg.Warning(ctx, fmt.Sprintf("Failed to collect long-pending tool call metrics: %v", err))
	} else {
		// Update the gauge for long-pending tool calls
		UpdateLongPendingToolCallsCount(longPendingToolCalls)
		if longPendingToolCalls > 0 {
			resource.LoggerBg.Warning(ctx, fmt.Sprintf("Found %d long-pending tool calls (>5 minutes)", longPendingToolCalls))
		}
	}

	// Check for long-running tool calls (running for more than 5 minutes)
	var longRunningToolCalls int64
	err = dao.GetDb(ctx).Model(&dao_tool_call_task.ObjToolCallTask{}).
		Where("tool_call_status = ? AND started_at IS NOT NULL AND started_at < ?",
			dao_tool_call_task.ToolCallTaskStatusRunning, now.Add(-5*time.Minute)).
		Count(&longRunningToolCalls).Error

	if err != nil {
		resource.LoggerBg.Warning(ctx, fmt.Sprintf("Failed to collect long-running tool call metrics: %v", err))
	} else {
		// Update the gauge for long-running tool calls
		UpdateLongRunningToolCallsCount(longRunningToolCalls)
		if longRunningToolCalls > 0 {
			resource.LoggerBg.Warning(ctx, fmt.Sprintf("Found %d long-running tool calls (>5 minutes)", longRunningToolCalls))
		}
	}
}

package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/gdp/metrics/gmetrics"
)

var (
	// Tool call status gauges
	toolCallPendingGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_pending_total",
		Help: "Current number of tool calls in pending status",
	})

	toolCallRunningGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_running_total",
		Help: "Current number of tool calls in running status",
	})

	toolCallSuccessGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_success_total",
		Help: "Current number of tool calls in success status",
	})

	toolCallFailedGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_failed_total",
		Help: "Current number of tool calls in failed status",
	})

	toolCallTimeoutGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_timeout_total",
		Help: "Current number of tool calls in timeout status",
	})

	// Tool call execution time histograms
	pendingToRunningToolCallHistogram = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "mcp_tool_call_pending_to_running_duration_seconds",
		Help:    "Histogram of scheduling time (pending to running transition)",
		Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300},
	})

	runningToSuccessToolCallHistogram = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "mcp_tool_call_running_to_success_duration_seconds",
		Help:    "Histogram of execution time (running to success transition)",
		Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300, 600},
	})

	// Tool call status counters (for rate calculations)
	toolCallStatusChangeCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "mcp_tool_call_status_change_total",
			Help: "Total number of tool call status changes by status",
		},
		[]string{"status"},
	)

	// Tool call metrics by tool name
	toolCallExecutionCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "mcp_tool_call_execution_total",
			Help: "Total number of tool call executions by tool name and status",
		},
		[]string{"tool_name", "status"},
	)

	toolCallExecutionDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "mcp_tool_call_execution_duration_seconds",
			Help:    "Tool call execution duration by tool name",
			Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300, 600},
		},
		[]string{"tool_name"},
	)

	// Long-running tool call gauges for distributed monitoring
	longPendingToolCallsGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_long_pending_total",
		Help: "Current number of tool calls pending longer than 5 minutes",
	})

	longRunningToolCallsGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "mcp_tool_call_long_running_total",
		Help: "Current number of tool calls running longer than 5 minutes",
	})
)

// RegisterToolCallMetrics registers all tool call-related metrics with Prometheus
func RegisterToolCallMetrics() {
	// Get the service registry from gmetrics
	serviceRegistry := gmetrics.Default().ServiceRegistry

	// Register all metrics
	serviceRegistry.MustRegister(toolCallPendingGauge)
	serviceRegistry.MustRegister(toolCallRunningGauge)
	serviceRegistry.MustRegister(toolCallSuccessGauge)
	serviceRegistry.MustRegister(toolCallFailedGauge)
	serviceRegistry.MustRegister(toolCallTimeoutGauge)

	serviceRegistry.MustRegister(pendingToRunningToolCallHistogram)
	serviceRegistry.MustRegister(runningToSuccessToolCallHistogram)

	serviceRegistry.MustRegister(toolCallStatusChangeCounter)
	serviceRegistry.MustRegister(toolCallExecutionCounter)
	serviceRegistry.MustRegister(toolCallExecutionDuration)
	serviceRegistry.MustRegister(longPendingToolCallsGauge)
	serviceRegistry.MustRegister(longRunningToolCallsGauge)
}

// UpdateToolCallStatusMetrics updates the tool call status gauges based on current counts
func UpdateToolCallStatusMetrics(pendingCount, runningCount, successCount, failedCount, timeoutCount int) {
	toolCallPendingGauge.Set(float64(pendingCount))
	toolCallRunningGauge.Set(float64(runningCount))
	toolCallSuccessGauge.Set(float64(successCount))
	toolCallFailedGauge.Set(float64(failedCount))
	toolCallTimeoutGauge.Set(float64(timeoutCount))
}

// RecordToolCallStatusChange records a tool call status change and increments the counter
func RecordToolCallStatusChange(status string) {
	toolCallStatusChangeCounter.WithLabelValues(status).Inc()
}

// RecordPendingToRunningToolCallDuration records the time taken for a tool call to transition from pending to running
func RecordPendingToRunningToolCallDuration(duration time.Duration) {
	pendingToRunningToolCallHistogram.Observe(duration.Seconds())
}

// RecordRunningToSuccessToolCallDuration records the time taken for a tool call to transition from running to success
func RecordRunningToSuccessToolCallDuration(duration time.Duration) {
	runningToSuccessToolCallHistogram.Observe(duration.Seconds())
}

// RecordToolCallExecution records a tool call execution with tool name and status
func RecordToolCallExecution(toolName, status string, duration time.Duration) {
	toolCallExecutionCounter.WithLabelValues(toolName, status).Inc()
	if duration > 0 {
		toolCallExecutionDuration.WithLabelValues(toolName).Observe(duration.Seconds())
	}
}

// UpdateLongPendingToolCallsCount updates the count of long-pending tool calls
func UpdateLongPendingToolCallsCount(count int64) {
	longPendingToolCallsGauge.Set(float64(count))
}

// UpdateLongRunningToolCallsCount updates the count of long-running tool calls
func UpdateLongRunningToolCallsCount(count int64) {
	longRunningToolCallsGauge.Set(float64(count))
}

// RecordToolCallStatusTransition records a tool call status transition with timing
func RecordToolCallStatusTransition(fromStatus, toStatus, toolName string, transitionTime time.Duration) {
	// Record the status change
	RecordToolCallStatusChange(toStatus)

	// Record transition timing based on status change
	switch {
	case fromStatus == "pending" && toStatus == "running":
		RecordPendingToRunningToolCallDuration(transitionTime)
	case fromStatus == "running" && toStatus == "success":
		RecordRunningToSuccessToolCallDuration(transitionTime)
		RecordToolCallExecution(toolName, "success", transitionTime)
	case toStatus == "failed":
		RecordToolCallExecution(toolName, "failed", transitionTime)
	case toStatus == "timeout":
		RecordToolCallExecution(toolName, "timeout", transitionTime)
	}
}

package metrics

import (
	"time"
)

// InitMetrics initializes all MCP metrics
func InitMetrics() {
	// Register session metrics
	RegisterSessionMetrics()

	// Register tool call metrics
	RegisterToolCallMetrics()

	// Register metrics recorder with helper
	RegisterMetricsRecorder()

	// Initialize metrics collector with 15-second interval
	InitMetricsCollector(15 * time.Second)
}

// StartMetricsCollection starts the metrics collection process
func StartMetricsCollection() error {
	collector := GetMetricsCollector()
	if collector == nil {
		return nil
	}
	return collector.Start()
}

// StopMetricsCollection stops the metrics collection process
func StopMetricsCollection() {
	collector := GetMetricsCollector()
	if collector != nil {
		collector.Stop()
	}
}

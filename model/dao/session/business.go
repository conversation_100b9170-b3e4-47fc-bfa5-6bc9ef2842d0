package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

var SessionBusinessIns = SessionBusiness{}

type SessionBusiness struct{}

// Insert 插入会话记录
func (bus SessionBusiness) Insert(ctx context.Context, data *ObjSession) (int64, error) {
	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		return 0, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLInsertError, "Insert session")
	}

	// Record session creation metrics
	metrics_helper.RecordSessionCreated()

	return data.SessionID, nil
}

// InsertInTx 在事务中插入会话记录
func (bus SessionBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjSession) (int64, error) {
	result := tx.Create(data)
	if result.Error != nil {
		return 0, errcode.NewCustomErr(ctx, result.Error, errcode.SQLInsertError, "Insert session in transaction")
	}
	return data.SessionID, nil
}

// SelectByPrimaryKey 根据主键查询会话
func (bus SessionBusiness) SelectByPrimaryKey(ctx context.Context, sessionID int64) (*ObjSession, error) {
	var session ObjSession
	tx := dao.GetDb(ctx).
		Where("session_id = ? AND is_delete = ?", sessionID, false).
		First(&session)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SessionNotFound, fmt.Sprintf("Session not found: sessionID=%d", sessionID))
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select session by primary key")
	}
	return &session, nil
}

// SelectByJobID 根据作业ID查询会话
func (bus SessionBusiness) SelectByJobID(ctx context.Context, jobID string) (*ObjSession, error) {
	var session ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("job_id = ? AND is_delete = ?", jobID, false).
		First(&session)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select session by job ID")
	}
	return &session, nil
}

// GetSessionByCode 根据会话代码查询会话
func (bus SessionBusiness) GetSessionByCode(ctx context.Context, sessionCode string) (*ObjSession, error) {
	var session ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("session_code = ? AND is_delete = ?", sessionCode, false).
		First(&session)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SessionNotFound, fmt.Sprintf("Session not found: sessionCode=%s", sessionCode))
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Get session by code")
	}
	return &session, nil
}

// GetMapByPrimaryKeys 根据主键列表获取会话映射
func (bus SessionBusiness) GetMapByPrimaryKeys(ctx context.Context, sessionIDs []int64) (map[int64]*ObjSession, error) {
	if len(sessionIDs) == 0 {
		return make(map[int64]*ObjSession), nil
	}

	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("session_id IN (?) AND is_delete = ?", sessionIDs, false).
		Find(&sessions)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Get session map by primary keys")
	}

	sessionsMap := make(map[int64]*ObjSession)
	for _, item := range sessions {
		sessionsMap[item.SessionID] = item
	}
	return sessionsMap, nil
}

// UpdateStatus 更新会话状态
func (bus SessionBusiness) UpdateJobIDAndStatus(ctx context.Context, sessionID int64, jobID string, status ContainerStatus) error {
	updates := map[string]any{
		"job_id":           jobID,
		"container_status": status,
	}
	fillStopAt(updates, status)

	err := bus.UpdateMap(ctx, sessionID, updates)
	if err == nil {
		// Record status change metrics
		switch status {
		case ContainerStatusPending:
			metrics_helper.RecordSessionPending()
		case ContainerStatusRunning:
			metrics_helper.RecordSessionRunning()
		case ContainerStatusStopping:
			metrics_helper.RecordSessionStopping()
		case ContainerStatusStopped:
			metrics_helper.RecordSessionStopped()
		case ContainerStatusTimeout:
			metrics_helper.RecordSessionTimeout()
		case ContainerStatusFailed:
			metrics_helper.RecordSessionFailed()
		}
	}

	return err
}

func (bus SessionBusiness) UpdateMcpToolsAndStatus(ctx context.Context, sessionID int64, mcpToolsJSON *dao.JSONData, status ContainerStatus) error {
	updates := map[string]any{
		"mcp_tools":        mcpToolsJSON,
		"container_status": status,
	}
	fillStopAt(updates, status)

	err := bus.UpdateMap(ctx, sessionID, updates)
	if err == nil {
		// Record status change metrics
		switch status {
		case ContainerStatusPending:
			metrics_helper.RecordSessionPending()
		case ContainerStatusRunning:
			metrics_helper.RecordSessionRunning()
		case ContainerStatusStopping:
			metrics_helper.RecordSessionStopping()
		case ContainerStatusStopped:
			metrics_helper.RecordSessionStopped()
		case ContainerStatusTimeout:
			metrics_helper.RecordSessionTimeout()
		case ContainerStatusFailed:
			metrics_helper.RecordSessionFailed()
		}
	}

	return err
}

// UpdateStatusWithError 更新会话状态并记录错误信息
func (bus SessionBusiness) UpdateStatusWithError(ctx context.Context, sessionID int64, status ContainerStatus, errMsg string) error {

	updates := map[string]any{
		"container_status": status,
	}
	if errMsg != "" {
		// 截断过长的错误信息，保留前15000个字符
		const maxErrMsgLength = 15000
		if len(errMsg) > maxErrMsgLength {
			errMsg = errMsg[:maxErrMsgLength] + "...(truncated)"
		}
		updates["err_msg"] = &errMsg
	}
	fillStopAt(updates, status)

	err := bus.UpdateMap(ctx, sessionID, updates)
	if err == nil {
		// Record status change metrics
		switch status {
		case ContainerStatusPending:
			metrics_helper.RecordSessionPending()
		case ContainerStatusRunning:
			metrics_helper.RecordSessionRunning()
		case ContainerStatusStopping:
			metrics_helper.RecordSessionStopping()
		case ContainerStatusStopped:
			metrics_helper.RecordSessionStopped()
		case ContainerStatusTimeout:
			metrics_helper.RecordSessionTimeout()
		case ContainerStatusFailed:
			metrics_helper.RecordSessionFailed()
		}
	}

	return err
}
func (bus SessionBusiness) UpdateMap(ctx context.Context, sessionID int64, updates map[string]any) error {
	tx := dao.GetDb(ctx).Model(&ObjSession{}).Where("session_id = ? AND is_delete = ?", sessionID, false).Updates(updates)
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Update session map")
	}
	return nil
}

func (bus SessionBusiness) UpdateReward(ctx context.Context, sessionID int64, reward *RewardData) error {
	tx := dao.GetDb(ctx).Model(&ObjSession{}).Where("session_id = ? AND is_delete = ?", sessionID, false).
		Update("reward", reward.ToJSONData())
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Update session reward")
	}
	return nil
}

// Delete 软删除会话记录
func (bus SessionBusiness) Delete(ctx context.Context, sessionID int64) error {
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("session_id = ? AND is_delete = ?", sessionID, false).
		Updates(map[string]any{
			"is_delete": true,
		})
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Delete session")
	}
	return nil
}

// SelectByStatus 根据状态查询会话列表
func (bus SessionBusiness) SelectByStatus(ctx context.Context, status ContainerStatus) ([]*ObjSession, error) {
	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("container_status = ? AND is_delete = ?", status, false).
		Order("created_at DESC").
		Find(&sessions)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select sessions by status")
	}
	return sessions, nil
}

// SelectByEnvID 根据环境ID查询会话列表
func (bus SessionBusiness) SelectByEnvID(ctx context.Context, envID int64) ([]*ObjSession, error) {
	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("env_id = ? AND is_delete = ?", envID, false).
		Order("created_at DESC").
		Find(&sessions)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select sessions by environment ID")
	}
	return sessions, nil
}

// SessionLogRecord 记录会话日志到BOS
func (bus SessionBusiness) SessionLogRecord(ctx context.Context, sessionID int64, jobID string) error {
	// 2. 检查会话是否有关联的Kubernetes任务
	if jobID == "" {
		return errcode.NewCustomErr(ctx, errors.New("no associated Kubernetes job"), errcode.K8sContainerStartError, "Session log record validation")
	}
	// 3. 从Kubernetes获取任务日志
	logResp, err := rpc_k8s_proxy.K8sProxyClientIns.GetTaskLogStream(ctx, jobID)
	if err != nil {
		return errcode.NewCustomErr(ctx, err, errcode.K8sContainerGetError, "Get Kubernetes task log")
	}

	// 4. 处理日志内容 - 将\n转换为实际换行符
	logContent := strings.ReplaceAll(logResp.Log, "\\n", "\n")

	// 5. 生成BOS存储路径
	bosKey := fmt.Sprintf("mcp_log/%s.log", jobID)

	// 6. 上传日志到BOS
	err = rpc_bos.UploadObjectFromString(ctx, bosKey, logContent, "text/plain; charset=utf-8")
	if err != nil {
		return errcode.NewCustomErr(ctx, err, errcode.BosUploadObjectError, "Upload log to BOS")
	}

	// 7. 获取BOS下载链接
	logURL := rpc_bos.GenerateVisitURL(ctx, bosKey, -1)

	// 8. 更新会话记录，存储日志下载链接
	err = bus.UpdateMap(ctx, sessionID, map[string]any{
		"log_url": &logURL,
	})
	if err != nil {
		return err // UpdateMap already returns safe error
	}
	return nil
}

// SelectByPage 分页查询会话
func (bus SessionBusiness) SelectByPage(ctx context.Context, page, size int64, status ContainerStatus) (int64, []*ObjSession, error) {
	query := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("is_delete = ?", false)

	if status != "" {
		query = query.Where("container_status = ?", status)
	}

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Count sessions by page")
	}

	// 分页查询
	offset := (page - 1) * size
	var sessions []*ObjSession
	err = query.Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(size)).
		Find(&sessions).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Select sessions by page")
	}

	return total, sessions, nil
}

// SelectActiveSessions 查询活跃会话（运行中的会话）
func (bus SessionBusiness) SelectActiveSessions(ctx context.Context) ([]*ObjSession, error) {
	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("container_status IN (?) AND is_delete = ?",
			[]ContainerStatus{ContainerStatusPending, ContainerStatusRunning},
			false).
		Order("created_at DESC").
		Find(&sessions)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select active sessions")
	}
	return sessions, nil
}

// SelectTimeoutSessions 查询超时会话（运行超过指定时间的会话）
func (bus SessionBusiness) SelectTimeoutSessions(ctx context.Context, timeoutDuration time.Duration) ([]*ObjSession, error) {
	timeoutBefore := time.Now().Add(-timeoutDuration)

	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("container_status IN (?) AND created_at < ? AND is_delete = ?",
			[]ContainerStatus{ContainerStatusPending, ContainerStatusRunning},
			timeoutBefore,
			false).
		Order("created_at ASC").
		Find(&sessions)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select timeout sessions")
	}
	return sessions, nil
}

func fillStopAt(updates map[string]any, status ContainerStatus) {
	if status == ContainerStatusStopping || status == ContainerStatusStopped || status == ContainerStatusTimeout || status == ContainerStatusFailed {
		now := time.Now()
		updates["stopped_at"] = &now
	}
}

package dao

import (
	"time"

	"database/sql/driver"

	dao "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

type ObjSession struct {
	SessionID       int64                `gorm:"primaryKey;column:session_id;autoIncrement" json:"session_id"`
	JobID           string               `gorm:"column:job_id;default:''" json:"job_id"`
	EnvID           int64                `gorm:"column:env_id;default:0" json:"env_id"`
	ImageID         string               `gorm:"column:image_id;default:''" json:"image_id"`
	SessionCode     string               `gorm:"column:session_code;default:''" json:"session_code"`
	ServerIds       dao.JSONArray[int64] `gorm:"column:server_ids" json:"server_ids"`
	McpTools        dao.JSONData         `gorm:"column:mcp_tools" json:"mcp_tools"`
	ContainerStatus ContainerStatus      `gorm:"column:container_status;default:init" json:"container_status"`
	TimeoutSeconds  int64                `gorm:"column:timeout_seconds;default:0" json:"timeout_seconds"`
	Reward          dao.JSONData         `gorm:"column:reward" json:"reward"`
	ErrMsg          *string              `gorm:"column:err_msg" json:"err_msg"`
	LogURL          *string              `gorm:"column:log_url" json:"log_url"`
	StoppedAt       *time.Time           `gorm:"column:stopped_at" json:"stopped_at"`
	CreatedAt       *time.Time           `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3)" json:"created_at"`
	UpdatedAt       *time.Time           `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)" json:"updated_at"`
	IsDelete        bool                 `gorm:"column:is_delete;default:false" json:"is_delete"`
}

func (c *ObjSession) TableName() string {
	return "obj_session"
}

// ContainerStatus 容器状态枚举
type ContainerStatus string

const (
	ContainerStatusInit     ContainerStatus = "init"     // 初始化
	ContainerStatusPending  ContainerStatus = "pending"  // 等待中
	ContainerStatusRunning  ContainerStatus = "running"  // 运行中
	ContainerStatusStopping ContainerStatus = "stopping" // 停止中
	ContainerStatusStopped  ContainerStatus = "stopped"  // 已停止
	ContainerStatusTimeout  ContainerStatus = "timeout"  // 超时
	ContainerStatusFailed   ContainerStatus = "failed"   // 失败
)

// Scan 实现sql.Scanner接口，用于从数据库读取
func (c *ContainerStatus) Scan(value interface{}) error {
	*c = ContainerStatus(value.([]byte))
	return nil
}

// Value 实现driver.Valuer接口，用于写入数据库
func (c ContainerStatus) Value() (driver.Value, error) {
	return string(c), nil
}

// RewardData 奖励数据结构，存储k8sexecute接口返回结果
type RewardData struct {
	Code      int    `json:"code"`       // k8s执行返回码
	Message   string `json:"message"`    // k8s执行返回消息
	StderrURL string `json:"stderr_url"` // 标准错误输出内容上传到BOS后的永久URL
	StdoutURL string `json:"stdout_url"` // 标准输出内容上传到BOS后的永久URL
}

// ToJSONData 将RewardData转换为JSONData，用于数据库存储
func (r *RewardData) ToJSONData() dao.JSONData {
	return dao.JSONData{
		"code":       r.Code,
		"message":    r.Message,
		"stderr_url": r.StderrURL,
		"stdout_url": r.StdoutURL,
	}
}

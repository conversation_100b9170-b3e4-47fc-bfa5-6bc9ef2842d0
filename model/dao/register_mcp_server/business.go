package dao

import (
	"context"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

var RegisterMcpServerBusinessIns = RegisterMcpServerBusiness{}

type RegisterMcpServerBusiness struct{}

// Insert 插入MCP服务器记录
func (bus RegisterMcpServerBusiness) Insert(ctx context.Context, data *ObjRegisterMcpServer) (int64, error) {
	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		return 0, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLInsertError, "Insert MCP server")
	}
	return data.ServerID, nil
}

// InsertInTx 在事务中插入MCP服务器记录
func (bus RegisterMcpServerBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjRegisterMcpServer) (int64, error) {
	result := tx.Create(data)
	if result.Error != nil {
		return 0, errcode.NewCustomErr(ctx, result.Error, errcode.SQLInsertError, "Insert MCP server in transaction")
	}
	return data.ServerID, nil
}

// SelectByPrimaryKey 根据主键查询MCP服务器
func (bus RegisterMcpServerBusiness) SelectByPrimaryKey(ctx context.Context, serverID int64) (*ObjRegisterMcpServer, error) {
	var server ObjRegisterMcpServer
	tx := dao.GetDb(ctx).
		Where("server_id = ? AND is_delete = ?", serverID, false).
		First(&server)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.McpServerNotFound, "MCP服务器不存在", "Select MCP server by primary key")
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select MCP server by primary key")
	}
	return &server, nil
}

// GetMapByPrimaryKeys 根据主键列表获取MCP服务器映射
func (bus RegisterMcpServerBusiness) GetMapByPrimaryKeys(ctx context.Context, serverIDs []int64) (map[int64]*ObjRegisterMcpServer, error) {
	if len(serverIDs) == 0 {
		return make(map[int64]*ObjRegisterMcpServer), nil
	}

	var servers []*ObjRegisterMcpServer
	tx := dao.GetDb(ctx).Model(&ObjRegisterMcpServer{}).
		Where("server_id IN (?) AND is_delete = ?", serverIDs, false).
		Find(&servers)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Get map by primary keys")
	}

	serversMap := make(map[int64]*ObjRegisterMcpServer)
	for _, item := range servers {
		serversMap[item.ServerID] = item
	}
	return serversMap, nil
}

// Update 更新MCP服务器记录
func (bus RegisterMcpServerBusiness) Update(ctx context.Context, data *ObjRegisterMcpServer) error {
	tx := dao.GetDb(ctx).Model(&ObjRegisterMcpServer{}).
		Where("server_id = ? AND is_delete = ?", data.ServerID, false).
		Updates(map[string]any{
			"server_name":         data.ServerName,
			"command":             data.Command,
			"args":                data.Args,
			"description":         data.Description,
			"server_code_bos_url": data.ServerCodeBosUrl,
			"updated_at":          data.UpdatedAt,
		})
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Update MCP server")
	}
	return nil
}

// UpdateInTx 在事务中更新MCP服务器记录
func (bus RegisterMcpServerBusiness) UpdateInTx(ctx context.Context, tx *gorm.DB, data *ObjRegisterMcpServer) error {
	result := tx.Model(&ObjRegisterMcpServer{}).
		Where("server_id = ? AND is_delete = ?", data.ServerID, false).
		Updates(map[string]any{
			"server_name":         data.ServerName,
			"command":             data.Command,
			"args":                data.Args,
			"description":         data.Description,
			"server_code_bos_url": data.ServerCodeBosUrl,
			"updated_at":          data.UpdatedAt,
		})
	if result.Error != nil {
		return errcode.NewCustomErr(ctx, result.Error, errcode.SQLUpdateError, "Update MCP server in transaction")
	}
	return nil
}

// Delete 软删除MCP服务器记录
func (bus RegisterMcpServerBusiness) Delete(ctx context.Context, serverID int64) error {
	tx := dao.GetDb(ctx).Model(&ObjRegisterMcpServer{}).
		Where("server_id = ? AND is_delete = ?", serverID, false).
		Updates(map[string]any{
			"is_delete": true,
		})
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Delete MCP server")
	}
	return nil
}

// DeleteInTx 在事务中软删除MCP服务器记录
func (bus RegisterMcpServerBusiness) DeleteInTx(ctx context.Context, tx *gorm.DB, serverID int64) error {
	result := tx.Model(&ObjRegisterMcpServer{}).
		Where("server_id = ? AND is_delete = ?", serverID, false).
		Updates(map[string]any{
			"is_delete": true,
		})
	if result.Error != nil {
		return errcode.NewCustomErr(ctx, result.Error, errcode.SQLUpdateError, "Delete MCP server in transaction")
	}
	return nil
}

// SelectAllActive 查询所有有效的MCP服务器
func (bus RegisterMcpServerBusiness) SelectAllActive(ctx context.Context) ([]*ObjRegisterMcpServer, error) {
	var servers []*ObjRegisterMcpServer
	tx := dao.GetDb(ctx).Model(&ObjRegisterMcpServer{}).
		Where("is_delete = ?", false).
		Order("created_at DESC").
		Find(&servers)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select all active MCP servers")
	}
	return servers, nil
}

// SelectByPage 分页查询MCP服务器
func (bus RegisterMcpServerBusiness) SelectByPage(ctx context.Context, page, size int64, keyword string) (int64, []*ObjRegisterMcpServer, error) {
	query := dao.GetDb(ctx).Model(&ObjRegisterMcpServer{}).
		Where("is_delete = ?", false)

	if keyword != "" {
		query = query.Where("server_name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Select MCP servers by page")
	}

	// 分页查询
	offset := (page - 1) * size
	var servers []*ObjRegisterMcpServer
	err = query.Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(size)).
		Find(&servers).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Select MCP servers by page")
	}

	return total, servers, nil
}

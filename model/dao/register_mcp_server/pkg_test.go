package dao_test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll() {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll() {}

func setUp() {
	dao.TxMysqlMain = dao.CliMysqlMain.Begin()
}

func tearDown() {
	dao.TxMysqlMain.Rollback()
}

func TestRegisterMcpServerLifecycle(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestCRUD", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		serverArgs := base.JSONArray[string]{"--stdio", "--config", "/path/to/config.json"}
		server := &dao_register_mcp_server.ObjRegisterMcpServer{
			ServerName:       "test_filesystem_server",
			Command:          "npx",
			Args:             serverArgs,
			Description:      "测试文件系统MCP服务器",
			ServerCodeBosUrl: "https://bos.example.com/mcp-servers/filesystem.zip",
		}

		// 测试插入
		id, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, server)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 测试查询
		result, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, server.ServerName, result.ServerName)
		assert.Equal(t, server.Command, result.Command)
		assert.Equal(t, server.Args, result.Args)

		// 测试更新
		server.ServerID = id
		server.Description = "更新后的描述"
		server.Command = "python"
		err = dao_register_mcp_server.RegisterMcpServerBusinessIns.Update(ctx, server)
		assert.NoError(t, err)

		// 验证更新结果
		updatedResult, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, "更新后的描述", updatedResult.Description)
		assert.Equal(t, "python", updatedResult.Command)

		// 测试删除
		err = dao_register_mcp_server.RegisterMcpServerBusinessIns.Delete(ctx, id)
		assert.NoError(t, err)

		// 验证删除
		_, err = dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.Error(t, err)
	})

	t.Run("TestInsertInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		serverArgs := base.JSONArray[string]{"-m", "mcp_sqlite"}
		server := &dao_register_mcp_server.ObjRegisterMcpServer{
			ServerName:       "test_sqlite_server_tx",
			Command:          "python",
			Args:             serverArgs,
			Description:      "事务测试SQLite MCP服务器",
			ServerCodeBosUrl: "https://bos.example.com/mcp-servers/sqlite.zip",
		}

		// 开启事务
		tx := dao.GetDb(ctx)

		// 测试事务中插入
		id, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.InsertInTx(ctx, tx, server)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 验证插入结果
		result, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, server.ServerName, result.ServerName)
	})

	t.Run("TestUpdateInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		tx := dao.GetDb(ctx)
		now := time.Now().UnixNano()

		serverArgs := base.JSONArray[string]{"--help"}
		server := &dao_register_mcp_server.ObjRegisterMcpServer{
			ServerName:       fmt.Sprintf("test_server_update_tx_%d", now),
			Command:          "node",
			Args:             serverArgs,
			Description:      "事务更新测试",
			ServerCodeBosUrl: "https://bos.example.com/mcp-servers/test.zip",
		}

		id, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.InsertInTx(ctx, tx, server)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		server.ServerID = id
		server.Description = "事务更新后的描述"
		server.Command = "python"
		err = dao_register_mcp_server.RegisterMcpServerBusinessIns.UpdateInTx(ctx, tx, server)
		assert.NoError(t, err)
	})
}

func TestGetMapByPrimaryKeys(t *testing.T) {
	setUpAll()
	defer tearDownAll()
	setUp()
	defer tearDown()

	// 创建多个测试服务器
	servers := []*dao_register_mcp_server.ObjRegisterMcpServer{
		{
			ServerName:       "test_server_multi_1",
			Command:          "npx",
			Args:             base.JSONArray[string]{"--stdio"},
			Description:      "多服务器测试1",
			ServerCodeBosUrl: "https://bos.example.com/server1.zip",
		},
		{
			ServerName:       "test_server_multi_2",
			Command:          "python",
			Args:             base.JSONArray[string]{"-m", "server2"},
			Description:      "多服务器测试2",
			ServerCodeBosUrl: "https://bos.example.com/server2.zip",
		},
		{
			ServerName:       "test_server_multi_3",
			Command:          "node",
			Args:             base.JSONArray[string]{"server3.js"},
			Description:      "多服务器测试3",
			ServerCodeBosUrl: "https://bos.example.com/server3.zip",
		},
	}

	var serverIDs []int64
	// 插入测试数据
	for _, server := range servers {
		id, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, server)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))
		serverIDs = append(serverIDs, id)
	}

	// 测试场景1: 查询多个服务器
	serversMap, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.GetMapByPrimaryKeys(ctx, serverIDs)
	assert.NoError(t, err)
	assert.Equal(t, 3, len(serversMap))
	assert.NotNil(t, serversMap[serverIDs[0]])
	assert.NotNil(t, serversMap[serverIDs[1]])
	assert.NotNil(t, serversMap[serverIDs[2]])
	assert.Equal(t, servers[0].ServerName, serversMap[serverIDs[0]].ServerName)
	assert.Equal(t, servers[1].ServerName, serversMap[serverIDs[1]].ServerName)
	assert.Equal(t, servers[2].ServerName, serversMap[serverIDs[2]].ServerName)

	// 测试场景2: 包含不存在的ID
	nonExistIDs := []int64{serverIDs[0], 99999, serverIDs[2]}
	serversMap2, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.GetMapByPrimaryKeys(ctx, nonExistIDs)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(serversMap2))
	assert.NotNil(t, serversMap2[serverIDs[0]])
	assert.Nil(t, serversMap2[99999])
	assert.NotNil(t, serversMap2[serverIDs[2]])

	// 测试场景3: 空ID列表
	emptyIDs := []int64{}
	serversMap3, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.GetMapByPrimaryKeys(ctx, emptyIDs)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(serversMap3))
}

func TestRegisterMcpServerQueries(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestSelectAllActive", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试服务器
		server := &dao_register_mcp_server.ObjRegisterMcpServer{
			ServerName:       "test_active_server",
			Command:          "npx",
			Args:             base.JSONArray[string]{"--stdio"},
			Description:      "活跃服务器测试",
			ServerCodeBosUrl: "https://bos.example.com/active.zip",
		}

		id, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, server)
		assert.NoError(t, err)

		// 查询所有活跃服务器
		servers, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectAllActive(ctx)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(servers), 1)

		// 验证包含新创建的服务器
		found := false
		for _, s := range servers {
			if s.ServerID == id {
				found = true
				assert.Equal(t, server.ServerName, s.ServerName)
				break
			}
		}
		assert.True(t, found)
	})

	t.Run("TestSelectByPage", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试服务器
		serverPrefix := fmt.Sprintf("test_page_server_%d", time.Now().UnixNano())
		for i := 0; i < 5; i++ {
			server := &dao_register_mcp_server.ObjRegisterMcpServer{
				ServerName:       fmt.Sprintf("%s_%d", serverPrefix, i),
				Command:          "node",
				Args:             base.JSONArray[string]{fmt.Sprintf("server%d.js", i)},
				Description:      fmt.Sprintf("分页测试服务器%d", i),
				ServerCodeBosUrl: fmt.Sprintf("https://bos.example.com/page%d.zip", i),
			}
			_, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, server)
			assert.NoError(t, err)
		}

		// 测试分页查询（无关键词）
		total, servers, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPage(ctx, 1, 10, "")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
		assert.GreaterOrEqual(t, len(servers), 5)

		// 测试分页查询（带关键词）
		total, servers, err = dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPage(ctx, 1, 10, "test_page_server")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))

		// 测试分页功能
		total, servers, err = dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPage(ctx, 1, 3, "")
		assert.NoError(t, err)
		assert.LessOrEqual(t, len(servers), 3)

		// 测试关键词搜索
		total, servers, err = dao_register_mcp_server.RegisterMcpServerBusinessIns.SelectByPage(ctx, 1, 10, "分页测试")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
	})

	t.Run("TestDeleteInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试服务器
		server := &dao_register_mcp_server.ObjRegisterMcpServer{
			ServerName:       "test_delete_tx_server",
			Command:          "python",
			Args:             base.JSONArray[string]{"-m", "test"},
			Description:      "事务删除测试",
			ServerCodeBosUrl: "https://bos.example.com/delete.zip",
		}

		tx := dao.GetDb(ctx)
		id, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.InsertInTx(ctx, tx, server)
		assert.NoError(t, err)

		// 在事务中删除
		err = dao_register_mcp_server.RegisterMcpServerBusinessIns.DeleteInTx(ctx, tx, id)
		assert.NoError(t, err)
	})
}

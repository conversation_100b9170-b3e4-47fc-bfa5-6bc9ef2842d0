package dao

import (
	"database/sql/driver"
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

type ObjRegisterMcpServer struct {
	ServerID         int64                 `gorm:"primaryKey;column:server_id;autoIncrement" json:"server_id"`
	ServerName       string                `gorm:"column:server_name;unique;not null" json:"server_name"`
	Type             Type                  `gorm:"column:type;default:'local'" json:"type"`
	Url              string                `gorm:"column:url;default:''" json:"url"`
	Headers          dao.JSONData          `gorm:"column:headers" json:"headers"`
	Command          string                `gorm:"column:command;default:''" json:"command"`
	Args             dao.JSONArray[string] `gorm:"column:args" json:"args"`
	Description      string                `gorm:"column:description" json:"description"`
	Env              dao.JSONData          `gorm:"column:env" json:"env"`
	ServerCodeBosUrl string                `gorm:"column:server_code_bos_url;default:''" json:"server_code_bos_url"`
	CreatedAt        *time.Time            `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3)" json:"created_at"`
	UpdatedAt        *time.Time            `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)" json:"updated_at"`
	IsDelete         bool                  `gorm:"column:is_delete;default:false" json:"is_delete"`
}

func (c *ObjRegisterMcpServer) TableName() string {
	return "obj_register_mcp_server"
}

type Type string

const (
	TypeLocal  Type = "local"  // 本地stdio传输
	TypeRemote Type = "remote" // 远程streamable传输
	TypeSSE    Type = "sse"    // 服务器发送事件传输
)

// Scan 实现sql.Scanner接口，用于从数据库读取
func (c *Type) Scan(value interface{}) error {
	*c = Type(value.([]byte))
	return nil
}

// Value 实现driver.Valuer接口，用于写入数据库
func (c Type) Value() (driver.Value, error) {
	return string(c), nil
}

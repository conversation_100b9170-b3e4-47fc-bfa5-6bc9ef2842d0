package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

var ToolCallTaskBusinessIns = ToolCallTaskBusiness{}

type ToolCallTaskBusiness struct{}

// Insert 插入工具调用任务记录
func (bus ToolCallTaskBusiness) Insert(ctx context.Context, data *ObjToolCallTask) (int64, error) {
	now := time.Now()
	data.CreatedAt = &now
	data.UpdatedAt = &now

	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		return 0, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLInsertError, "Insert tool call task")
	}

	// Record tool call creation metrics
	metrics_helper.RecordToolCallCreated()

	return data.TaskID, nil
}

// InsertInTx 在事务中插入工具调用任务记录
func (bus ToolCallTaskBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjToolCallTask) (int64, error) {
	now := time.Now()
	data.CreatedAt = &now
	data.UpdatedAt = &now

	result := tx.Create(data)
	if result.Error != nil {
		return 0, errcode.NewCustomErr(ctx, result.Error, errcode.SQLInsertError, "Insert tool call task in transaction")
	}
	return data.TaskID, nil
}

// SelectByPrimaryKey 根据主键查询工具调用任务
func (bus ToolCallTaskBusiness) SelectByPrimaryKey(ctx context.Context, taskID int64) (*ObjToolCallTask, error) {
	var task ObjToolCallTask
	tx := dao.GetDb(ctx).
		Where("task_id = ? AND is_delete = ?", taskID, false).
		First(&task)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.ToolCallTaskNotFound, fmt.Sprintf("Tool call task not found: taskID=%d", taskID), "Select tool call task by primary key")
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select tool call task by primary key")
	}
	return &task, nil
}

// SelectByCallID 根据调用ID查询工具调用任务
func (bus ToolCallTaskBusiness) SelectByCallID(ctx context.Context, sessionID int64, callID string) (*ObjToolCallTask, error) {
	var task ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("call_id = ? AND session_id = ? AND is_delete = ?", callID, sessionID, false).
		First(&task)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select tool call task by call ID")
	}
	return &task, nil
}

// GetMapByPrimaryKeys 根据主键列表获取工具调用任务映射
func (bus ToolCallTaskBusiness) GetMapByPrimaryKeys(ctx context.Context, taskIDs []int64) (map[int64]*ObjToolCallTask, error) {
	if len(taskIDs) == 0 {
		return make(map[int64]*ObjToolCallTask), nil
	}

	var tasks []*ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("task_id IN (?) AND is_delete = ?", taskIDs, false).
		Find(&tasks)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Get tool call task map by primary keys")
	}

	tasksMap := make(map[int64]*ObjToolCallTask)
	for _, item := range tasks {
		tasksMap[item.TaskID] = item
	}
	return tasksMap, nil
}

// Update 更新工具调用任务记录
func (bus ToolCallTaskBusiness) Update(ctx context.Context, data *ObjToolCallTask) error {
	now := time.Now()
	data.UpdatedAt = &now

	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("task_id = ? AND is_delete = ?", data.TaskID, false).
		Updates(map[string]any{
			"call_id":          data.CallID,
			"session_id":       data.SessionID,
			"tool_name":        data.ToolName,
			"arguments":        data.Arguments,
			"result":           data.Result,
			"old_env_md5":      data.OldEnvMd5,
			"new_env_md5":      data.NewEnvMd5,
			"old_env_url":      data.OldEnvUrl,
			"new_env_url":      data.NewEnvUrl,
			"tool_call_status": data.Status,
			"error_message":    data.ErrorMessage,
			"started_at":       data.StartedAt,
			"completed_at":     data.CompletedAt,
			"updated_at":       data.UpdatedAt,
		})
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Update tool call task")
	}
	return nil
}

// UpdateStatus 更新任务状态
func (bus ToolCallTaskBusiness) UpdateStatus(ctx context.Context, taskID int64, status ToolCallTaskStatus, errorMessage string) error {
	now := time.Now()
	updates := map[string]any{
		"tool_call_status": status,
		"updated_at":       &now,
	}

	// 根据状态设置不同的时间字段
	switch status {
	case ToolCallTaskStatusRunning:
		updates["started_at"] = &now
	case ToolCallTaskStatusSuccess, ToolCallTaskStatusFailed, ToolCallTaskStatusTimeout:
		updates["completed_at"] = &now
		if errorMessage != "" {
			updates["error_message"] = errorMessage
		}
	}

	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("task_id = ? AND is_delete = ?", taskID, false).
		Updates(updates)
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Update tool call task status")
	}

	// Record tool call status change metrics
	switch status {
	case ToolCallTaskStatusRunning:
		metrics_helper.RecordToolCallRunning()
	case ToolCallTaskStatusSuccess:
		metrics_helper.RecordToolCallSuccess()
	case ToolCallTaskStatusFailed:
		metrics_helper.RecordToolCallFailed()
	case ToolCallTaskStatusTimeout:
		metrics_helper.RecordToolCallTimeout()
	}

	return nil
}

// UpdateStatusInTx 在事务中更新任务状态
func (bus ToolCallTaskBusiness) UpdateStatusInTx(ctx context.Context, tx *gorm.DB, taskID int64, status ToolCallTaskStatus, errorMessage string) error {
	now := time.Now()
	updates := map[string]any{
		"tool_call_status": status,
		"updated_at":       &now,
	}
	fillTime(status, updates)
	result := tx.Model(&ObjToolCallTask{}).
		Where("task_id = ? AND is_delete = ?", taskID, false).
		Updates(updates)
	if result.Error != nil {
		return errcode.NewCustomErr(ctx, result.Error, errcode.SQLUpdateError, "Update tool call task status in transaction")
	}

	// Record tool call status change metrics if status is being updated
	if statusValue, exists := updates["tool_call_status"]; exists {
		if status, ok := statusValue.(ToolCallTaskStatus); ok {
			switch status {
			case ToolCallTaskStatusRunning:
				metrics_helper.RecordToolCallRunning()
			case ToolCallTaskStatusSuccess:
				metrics_helper.RecordToolCallSuccess()
			case ToolCallTaskStatusFailed:
				metrics_helper.RecordToolCallFailed()
			case ToolCallTaskStatusTimeout:
				metrics_helper.RecordToolCallTimeout()
			}
		}
	}

	return nil
}

func (bus ToolCallTaskBusiness) UpdateMap(ctx context.Context, taskID int64, updates map[string]any) error {
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("task_id = ? AND is_delete = ?", taskID, false).
		Updates(updates)
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Database operation")
	}
	return nil
}

// Delete 软删除工具调用任务记录
func (bus ToolCallTaskBusiness) Delete(ctx context.Context, taskID int64) error {
	now := time.Now()
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("task_id = ? AND is_delete = ?", taskID, false).
		Updates(map[string]any{
			"is_delete":  true,
			"updated_at": &now,
		})
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Database operation")
	}
	return nil
}

// DeleteInTx 在事务中软删除工具调用任务记录
func (bus ToolCallTaskBusiness) DeleteInTx(ctx context.Context, tx *gorm.DB, taskID int64) error {
	now := time.Now()
	result := tx.Model(&ObjToolCallTask{}).
		Where("task_id = ? AND is_delete = ?", taskID, false).
		Updates(map[string]any{
			"is_delete":  true,
			"updated_at": &now,
		})
	if result.Error != nil {
		return errcode.NewCustomErr(ctx, result.Error, errcode.SQLUpdateError, "Database operation")
	}
	return nil
}

// SelectBySessionID 根据会话ID查询工具调用任务列表
func (bus ToolCallTaskBusiness) SelectBySessionID(ctx context.Context, sessionID int64) ([]*ObjToolCallTask, error) {
	var tasks []*ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("session_id = ? AND is_delete = ?", sessionID, false).
		Order("created_at DESC").
		Find(&tasks)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Database operation")
	}
	return tasks, nil
}

// SelectByStatus 根据状态查询工具调用任务列表
func (bus ToolCallTaskBusiness) SelectByStatus(ctx context.Context, status ToolCallTaskStatus) ([]*ObjToolCallTask, error) {
	var tasks []*ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("tool_call_status = ? AND is_delete = ?", status, false).
		Order("created_at DESC").
		Find(&tasks)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Database operation")
	}
	return tasks, nil
}

// SelectByToolName 根据工具名称查询工具调用任务列表
func (bus ToolCallTaskBusiness) SelectByToolName(ctx context.Context, toolName string) ([]*ObjToolCallTask, error) {
	var tasks []*ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("tool_name = ? AND is_delete = ?", toolName, false).
		Order("created_at DESC").
		Find(&tasks)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Database operation")
	}
	return tasks, nil
}

// SelectByPage 分页查询工具调用任务
func (bus ToolCallTaskBusiness) SelectByPage(ctx context.Context, page, size int64, sessionID, status, toolName string) (int64, []*ObjToolCallTask, error) {
	query := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("is_delete = ?", false)

	if sessionID != "" {
		query = query.Where("session_id = ?", sessionID)
	}
	if status != "" {
		query = query.Where("tool_call_status = ?", status)
	}
	if toolName != "" {
		query = query.Where("tool_name LIKE ?", "%"+toolName+"%")
	}

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Database operation")
	}

	// 分页查询
	offset := (page - 1) * size
	var tasks []*ObjToolCallTask
	err = query.Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(size)).
		Find(&tasks).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Database operation")
	}

	return total, tasks, nil
}

// SelectPendingTasks 查询待处理的任务
func (bus ToolCallTaskBusiness) SelectPendingTasks(ctx context.Context) ([]*ObjToolCallTask, error) {
	var tasks []*ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("tool_call_status = ? AND is_delete = ?", ToolCallTaskStatusPending, false).
		Order("created_at ASC").
		Find(&tasks)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Database operation")
	}
	return tasks, nil
}

// SelectTimeoutTasks 查询超时任务（运行超过指定时间的任务）
func (bus ToolCallTaskBusiness) SelectTimeoutTasks(ctx context.Context, timeoutDuration time.Duration) ([]*ObjToolCallTask, error) {
	timeoutBefore := time.Now().Add(-timeoutDuration)

	var tasks []*ObjToolCallTask
	tx := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Where("tool_call_status = ? AND created_at < ? AND is_delete = ?",
			ToolCallTaskStatusRunning,
			timeoutBefore,
			false).
		Order("created_at ASC").
		Find(&tasks)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Database operation")
	}
	return tasks, nil
}

// SelectTaskStatistics 查询任务统计信息
func (bus ToolCallTaskBusiness) SelectTaskStatistics(ctx context.Context, sessionID int64) (map[string]int64, error) {
	type StatusCount struct {
		ToolCallStatus string `json:"tool_call_status"`
		Count          int64  `json:"count"`
	}

	var results []StatusCount
	query := dao.GetDb(ctx).Model(&ObjToolCallTask{}).
		Select("tool_call_status, COUNT(*) as count").
		Where("is_delete = ?", false).
		Group("tool_call_status")

	if sessionID != 0 {
		query = query.Where("session_id = ?", sessionID)
	}

	err := query.Find(&results).Error
	if err != nil {
		return nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Database operation")
	}

	statistics := make(map[string]int64)
	for _, result := range results {
		statistics[result.ToolCallStatus] = result.Count
	}

	return statistics, nil
}

func fillTime(status ToolCallTaskStatus, updates map[string]any) {
	now := time.Now()
	// 根据状态设置不同的时间字段
	switch status {
	case ToolCallTaskStatusRunning:
		updates["started_at"] = &now
	case ToolCallTaskStatusSuccess, ToolCallTaskStatusFailed, ToolCallTaskStatusTimeout:
		updates["completed_at"] = &now
	}
}

package dao

import (
	"database/sql/driver"
	"time"
)

type ObjToolCallTask struct {
	TaskID       int64              `gorm:"primaryKey;column:task_id;autoIncrement" json:"task_id"`
	CallID       string             `gorm:"column:call_id;not null;uniqueIndex:uk_call_id_session_id" json:"call_id"`
	SessionID    int64              `gorm:"column:session_id;not null;uniqueIndex:uk_call_id_session_id" json:"session_id"`
	ToolName     string             `gorm:"column:tool_name;not null" json:"tool_name"`
	Arguments    string             `gorm:"column:arguments" json:"arguments"`
	Result       string             `gorm:"column:result" json:"result"`
	OldEnvMd5    string             `gorm:"column:old_env_md5" json:"old_env_md5"`
	NewEnvMd5    string             `gorm:"column:new_env_md5" json:"new_env_md5"`
	OldEnvUrl    string             `gorm:"column:old_env_url" json:"old_env_url"`
	NewEnvUrl    string             `gorm:"column:new_env_url" json:"new_env_url"`
	Status       ToolCallTaskStatus `gorm:"column:tool_call_status;default:pending" json:"status"`
	ErrorMessage string             `gorm:"column:error_message" json:"error_message"`
	StartedAt    *time.Time         `gorm:"column:started_at" json:"started_at"`
	CompletedAt  *time.Time         `gorm:"column:completed_at" json:"completed_at"`
	CreatedAt    *time.Time         `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3)" json:"created_at"`
	UpdatedAt    *time.Time         `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)" json:"updated_at"`
	IsDelete     bool               `gorm:"column:is_delete;default:false" json:"is_delete"`
}

func (c *ObjToolCallTask) TableName() string {
	return "obj_tool_call_task"
}

type ToolCallTaskStatus string

const (
	ToolCallTaskStatusPending  ToolCallTaskStatus = "pending"
	ToolCallTaskStatusRunning  ToolCallTaskStatus = "running"
	ToolCallTaskStatusSuccess  ToolCallTaskStatus = "success"
	ToolCallTaskStatusFailed   ToolCallTaskStatus = "failed"
	ToolCallTaskStatusTimeout  ToolCallTaskStatus = "timeout"
	ToolCallTaskStatusCanceled ToolCallTaskStatus = "canceled"
)

// Scan 实现sql.Scanner接口，用于从数据库读取
func (c *ToolCallTaskStatus) Scan(value interface{}) error {
	*c = ToolCallTaskStatus(value.([]byte))
	return nil
}

// Value 实现driver.Valuer接口，用于写入数据库
func (c ToolCallTaskStatus) Value() (driver.Value, error) {
	return string(c), nil
}

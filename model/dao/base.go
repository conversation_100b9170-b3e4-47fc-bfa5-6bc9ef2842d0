package dao

import (
	"context"
	"database/sql/driver"
	"encoding/json"

	// "log"
	// "os"
	"sync"
	// "time"

	"gorm.io/gorm"
	// "gorm.io/gorm/logger"
	"icode.baidu.com/baidu/gdp/gorm_adapter"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
)

var TxMysqlMain *gorm_adapter.GormDB
var CliMysqlMain *gorm_adapter.GormDB
var ormMainOnce sync.Once

func OrmLazyInit() {
	ormMainOnce.Do(func() {

		var err error

		// // 配置日志
		// newLogger := logger.New(
		// 	log.New(os.Stdout, "\r\n", log.LstdFlags),
		// 	logger.Config{
		// 		SlowThreshold: time.Second,
		// 		LogLevel:      logger.Info, // 设置日志级别
		// 		Colorful:      true,
		// 	},
		// )
		// 初始化数据库连接
		CliMysqlMain, err = gorm_adapter.NewGorm(
			resource.MySQLClientMain,
			gorm_adapter.OptConfig(&gorm.Config{
				// Logger: newLogger,
			}),
		)
		if err != nil {
			panic(err.Error())
		}
		CliMysqlMain.Debug()
	})
}

func GetDb(ctx context.Context) *gorm_adapter.GormDB {
	OrmLazyInit()
	if TxMysqlMain != nil && TxMysqlMain.Error == nil {
		return TxMysqlMain
	}
	return CliMysqlMain.WithContext(ctx)
}

func TransactionFinish(ctx context.Context, tx *gorm.DB, err *error) {
	if tx == nil {
		resource.LoggerService.Error(ctx, "transaction is nil in TransactionFinish")
		return
	}

	if TxMysqlMain == nil {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
				panic(r)
			}
		}()
		if *err != nil {
			tx.Rollback()
		} else {
			if e := tx.Commit().Error; e != nil {
				*err = e
				resource.LoggerService.Error(ctx, "transaction commit failed", logit.Error("error", *err))
			}
		}
	}
}

// JSONArray JSON数组类型
type JSONArray[T any] []T

func (a JSONArray[T]) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func (a *JSONArray[T]) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(bytes, &a)
}

// JSONData JSON数据类型，用于存储工具列表
type JSONData map[string]interface{}

func (j JSONData) Value() (driver.Value, error) {
	return json.Marshal(j)
}

func (j *JSONData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(bytes, &j)
}

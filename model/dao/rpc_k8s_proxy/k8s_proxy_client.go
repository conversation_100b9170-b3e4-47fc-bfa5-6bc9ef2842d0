package dao

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
)

var K8sProxyClientIns = &K8sProxyClient{}

type K8sProxyClient struct {
}

type K8sProxyResp[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data T      `json:"data"`
}

// CreateTaskRequest 创建任务请求参数
type CreateTaskRequest struct {
	SessionID  int64    `json:"session_id"`
	ImagePath  string   `json:"image_path"`
	Command    string   `json:"command"`
	Args       []string `json:"args,omitempty"`
	Timeout    int      `json:"timeout"`
	OutputInfo string   `json:"output_info"`
	SourceType string   `json:"source_type"`
	TaskType   string   `json:"task_type"`
	Input      string   `json:"input,omitempty"`
	Resources  string   `json:"resources,omitempty"`
	IsRootUser bool     `json:"isRootUser"`
}

// CreateTaskResponse 创建任务响应数据
type CreateTaskResponse struct {
	TaskID string `json:"task_id"`
}

// TaskStateResponse 任务状态响应数据
type TaskStateResponse struct {
	State     string `json:"state"`
	OutputURL string `json:"output_url,omitempty"`
	Log       string `json:"log,omitempty"`
}

// TaskLogRequest 获取任务日志请求参数
type TaskLogRequest struct {
	TaskID string `json:"task_id"`
}

// TaskLogResponse 任务日志响应数据
type TaskLogResponse struct {
	Log     string `json:"log"`
	LogTime string `json:"log_time"`
}

// DeleteJobRequest 删除任务请求参数
type DeleteJobRequest struct {
	TaskID string `json:"task_id"`
}

// PodCountResponse 集群pod数量响应数据
type PodCountResponse struct {
	Count int `json:"count"`
}

// CreateTask 启动pod执行任务
func (client *K8sProxyClient) CreateTask(ctx context.Context, req *CreateTaskRequest) (*CreateTaskResponse, error) {
	path := "/api/v1/internal/dataeng_k8s/create_task"
	hd := http.Header{}
	hd.Add("X-Trace-Id", fmt.Sprintf("mcp_online_server_session_%d", req.SessionID))
	hd.Add("Content-Type", "application/json")
	reqBody, err := json.Marshal(req)
	if err != nil {
		resource.LoggerService.Error(ctx, "marshal request error:"+err.Error())
		return nil, err
	}

	ralReq := &ghttp.RalRequest{
		APIName: "CREATE_TASK",
		Method:  "POST",
		Header:  hd,
		Path:    path,
		Body:    bytes.NewReader(reqBody),
	}

	ret := &K8sProxyResp[CreateTaskResponse]{
		Code: -1,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err = ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "ral error:"+err.Error())
		return nil, err
	}
	if ret.Code != 0 {
		if ret.Code == 1001 {
			return nil, errcode.NewCustomErr(ctx, errors.New(ret.Msg), errcode.K8sRateLimitError, "K8s rate limit: "+ret.Msg)
		}
		if ret.Code == 10202 {
			return nil, errcode.NewCustomErr(ctx, errors.New(ret.Msg), errcode.K8sJobLimitError, "K8s job limit: "+ret.Msg)
		}
		return nil, errcode.NewCustomErr(ctx, errors.New(ret.Msg),
			errcode.K8sContainerCreateError, "K8s create task error[code: "+strconv.Itoa(ret.Code)+", msg: "+ret.Msg+"]")
	}
	return &ret.Data, nil
}

// ExecCommandRequest 执行命令请求
type ExecCommandRequest struct {
	SourceType string   `json:"source_type"`
	Command    []string `json:"command"`
	JobName    string   `json:"job_name"`
	Timeout    int      `json:"timeout"`
}

// ExecCommandResponse 执行命令响应
type ExecCommandResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Stdout  string `json:"stdout"`
	Stderr  string `json:"stderr"`
}

// ExecCommand 在容器内执行命令
func (client *K8sProxyClient) ExecCommand(ctx context.Context, req *ExecCommandRequest) (*ExecCommandResponse, error) {
	path := "/api/v1/internal/dataeng_k8s/exec_command"
	hd := http.Header{}
	hd.Add("X-Trace-Id", fmt.Sprintf("mcp_online_server_exec_%s", req.JobName))
	hd.Add("Content-Type", "application/json")
	if req.Timeout <= 0 {
		req.Timeout = 120
	}
	reqBody, err := json.Marshal(req)
	if err != nil {
		resource.LoggerService.Error(ctx, "marshal exec command request error:"+err.Error())
		return nil, err
	}

	log.Println(string(reqBody))

	ralReq := &ghttp.RalRequest{
		APIName: "EXEC_COMMAND",
		Method:  "POST",
		Header:  hd,
		Path:    path,
		Body:    bytes.NewReader(reqBody),
	}

	ret := &K8sProxyResp[ExecCommandResponse]{
		Code: -1,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err = ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "exec command ral error:"+err.Error())
		return nil, err
	}
	if ret.Code != 0 {
		// 根据错误码映射具体错误
		return nil, errcode.NewCustomErr(ctx, errors.New(ret.Msg),
			errcode.K8sExecCommandError, "K8s exec command error[code: "+strconv.Itoa(ret.Code)+", msg: "+ret.Msg+"]")
	}
	return &ret.Data, nil
}

// UploadFileRequest 上传文件请求
type UploadFileRequest struct {
	SourceType string     `json:"source_type"`
	JobName    string     `json:"job_name"`
	FileInfo   []FileInfo `json:"file_info"`
}

// FileInfo 文件信息
type FileInfo struct {
	FileURL  string `json:"file_url"`
	DestPath string `json:"dest_path"`
}

// UploadFileResponse 上传文件响应
type UploadFileResponse struct {
	Success bool `json:"success"`
}

// UploadFile 上传文件到容器
func (client *K8sProxyClient) UploadFile(ctx context.Context, req *UploadFileRequest) (*UploadFileResponse, error) {
	path := "/api/v1/internal/dataeng_k8s/upload_file"
	hd := http.Header{}
	hd.Add("X-Trace-Id", fmt.Sprintf("mcp_online_server_upload_%s", req.JobName))
	hd.Add("Content-Type", "application/json")
	reqBody, err := json.Marshal(req)
	if err != nil {
		resource.LoggerService.Error(ctx, "marshal upload file request error:"+err.Error())
		return nil, err
	}

	ralReq := &ghttp.RalRequest{
		APIName: "UPLOAD_FILE",
		Method:  "POST",
		Header:  hd,
		Path:    path,
		Body:    bytes.NewReader(reqBody),
	}

	ret := &K8sProxyResp[string]{
		Code: -1,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err = ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "upload file ral error:"+err.Error())
		return nil, err
	}
	if ret.Code != 0 {
		return nil, errcode.NewCustomErr(ctx, errors.New(ret.Msg),
			errcode.K8sContainerCreateError, "K8s upload file error[code: "+strconv.Itoa(ret.Code)+", msg: "+ret.Msg+"]")
	}
	return &UploadFileResponse{Success: true}, nil
}

// GetTaskState 查询pod任务状态
func (client *K8sProxyClient) GetTaskState(ctx context.Context, taskID string) (*TaskStateResponse, error) {
	path := "/api/v1/internal/dataeng_k8s/get_task_state"

	ralReq := &ghttp.RalRequest{
		APIName: "GET_TASK_STATE",
		Method:  "GET",
		Path:    path,
		Query: url.Values{
			"task_id": []string{taskID},
		},
	}

	ret := &K8sProxyResp[TaskStateResponse]{
		Code: -1,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err := ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "ral error:"+err.Error())
		return nil, err
	}
	if ret.Code != 0 {
		if strings.Contains(ret.Msg, "not found") {
			return &TaskStateResponse{
				State: "deleted",
			}, nil
		}
		return nil, errcode.NewCustomErr(ctx, errors.New(ret.Msg),
			errcode.K8sContainerCreateError, "K8s get task state error[code: "+strconv.Itoa(ret.Code)+", msg: "+ret.Msg+"]")
	}
	return &ret.Data, nil
}

// GetTaskLogStream 获取pod中任务执行日志
func (client *K8sProxyClient) GetTaskLogStream(ctx context.Context, taskID string) (*TaskLogResponse, error) {
	path := "/api/v1/internal/dataeng_k8s/get_task_log_stream"
	hd := http.Header{}
	hd.Add("Content-Type", "application/json")
	req := &TaskLogRequest{
		TaskID: taskID,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		resource.LoggerService.Error(ctx, "marshal request error:"+err.Error())
		return nil, err
	}

	ralReq := &ghttp.RalRequest{
		APIName: "GET_TASK_LOG_STREAM",
		Method:  "POST",
		Header:  hd,
		Path:    path,
		Body:    bytes.NewReader(reqBody),
	}

	ret := &K8sProxyResp[TaskLogResponse]{
		Code: -1,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err = ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "ral error:"+err.Error())
		return nil, err
	}
	if ret.Code != 0 {
		resource.LoggerService.Error(ctx, "code != 0, err:"+ret.Msg)
		return nil, errors.New(ret.Msg)
	}
	return &ret.Data, nil
}

// DeleteJob 删除job
func (client *K8sProxyClient) DeleteJob(ctx context.Context, taskID string) error {
	path := "/api/v1/internal/dataeng_k8s/delete_job"
	hd := http.Header{}
	hd.Add("Content-Type", "application/json")

	ralReq := &ghttp.RalRequest{
		APIName: "DELETE_JOB",
		Method:  "GET",
		Header:  hd,
		Path:    path,
		Query: url.Values{
			"task_id": []string{taskID},
		},
	}

	ret := &K8sProxyResp[map[string]interface{}]{
		Code: 0,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err := ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "ral error:"+err.Error())
		return err
	}
	if ret.Code != 0 {
		resource.LoggerService.Error(ctx, "code != 0, err:"+ret.Msg)
	}
	return nil
}

// GetPodCount 获取集群pod数量
func (client *K8sProxyClient) GetPodCount(ctx context.Context, sourceType string) (*PodCountResponse, error) {
	path := "/api/v1/openapi/dataeng_k8s/get_pod_count"

	ralReq := &ghttp.RalRequest{
		APIName: "GET_POD_COUNT",
		Method:  "GET",
		Path:    path,
		Query: url.Values{
			"source_type": []string{sourceType},
		},
	}

	ret := &K8sProxyResp[PodCountResponse]{
		Code: -1,
	}
	ralResp := &ghttp.RalResponse{
		Data:    ret,
		Decoder: codec.JSONDecoder,
	}

	err := ral.RAL(ctx, "k8s_proxy", ralReq, ralResp)
	if err != nil {
		resource.LoggerService.Error(ctx, "ral error:"+err.Error())
		return nil, err
	}
	if ret.Code != 0 {
		resource.LoggerService.Error(ctx, "code != 0, err:"+ret.Msg)
		return nil, errors.New(ret.Msg)
	}
	return &ret.Data, nil
}

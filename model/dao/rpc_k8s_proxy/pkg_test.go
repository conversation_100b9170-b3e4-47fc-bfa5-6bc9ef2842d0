package dao_test

import (
	"context"
	"encoding/json"
	"errors"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	dao "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
}

func tearDownAll(t *testing.T) {}

// 测试常量定义
const (
	// 任务状态
	TaskStatePending   = "pending"
	TaskStateRunning   = "running"
	TaskStateSucceeded = "succeeded"
	TaskStateFailed    = "failed"

	// 错误码
	ErrorCodeSuccess       = 0
	ErrorCodeInvalidInput  = 10001
	ErrorCodeImageNotFound = 10002
	ErrorCodeInternalError = -1
	ErrorCodeTimeout       = 10003
	ErrorCodeResourceLimit = 10202

	// 源类型
	SourceTypeVerifier = "verifier_system"
	SourceTypeDataeng  = "dataeng"
	SourceTypeMCP      = "mcp"
)

// 测试数据生成器
func generateValidCreateTaskRequest() *dao.CreateTaskRequest {
	return &dao.CreateTaskRequest{
		SessionID:  12345,
		ImagePath:  "mcp/mcp-runtime:latest",
		Command:    "/bin/bash",
		Args:       []string{"/home/<USER>/boost.sh"},
		Timeout:    3600,
		OutputInfo: `{"path_name": "test_output", "file_name": "result.json"}`,
		SourceType: SourceTypeMCP,
		TaskType:   "MCP",
		Input:      `["/path/to/input1.json", "/path/to/input2.json"]`,
		Resources:  `{"cpu": 1, "memory": {"value": 1, "type": "G"}}`,
		IsRootUser: false,
	}
}

func generateInvalidCreateTaskRequest() *dao.CreateTaskRequest {
	req := generateValidCreateTaskRequest()
	req.ImagePath = "" // 无效的镜像路径
	return req
}

// 通用RAL Mock函数
func mockRALSuccess(t *testing.T, expectedResponse interface{}) *gomonkey.Patches {
	patches := gomonkey.NewPatches()
	patches.ApplyFunc(ral.RAL, func(ctx context.Context, name any, req ral.Request, resp ral.Response, opts ...ral.ROption) error {
		// 类型断言获取具体的响应对象
		ralResp, ok := resp.(*ghttp.RalResponse)
		assert.True(t, ok)

		// 将mock响应写入resp.Data
		if ralResp.Data != nil && expectedResponse != nil {
			// 使用反射来处理不同类型的响应
			switch respData := ralResp.Data.(type) {
			case *dao.K8sProxyResp[dao.CreateTaskResponse]:
				if mockResp, ok := expectedResponse.(*dao.K8sProxyResp[dao.CreateTaskResponse]); ok {
					*respData = *mockResp
				}
			case *dao.K8sProxyResp[dao.TaskStateResponse]:
				if mockResp, ok := expectedResponse.(*dao.K8sProxyResp[dao.TaskStateResponse]); ok {
					*respData = *mockResp
				}
			case *dao.K8sProxyResp[dao.TaskLogResponse]:
				if mockResp, ok := expectedResponse.(*dao.K8sProxyResp[dao.TaskLogResponse]); ok {
					*respData = *mockResp
				}
			case *dao.K8sProxyResp[map[string]interface{}]:
				if mockResp, ok := expectedResponse.(*dao.K8sProxyResp[map[string]interface{}]); ok {
					*respData = *mockResp
				}
			case *dao.K8sProxyResp[dao.PodCountResponse]:
				if mockResp, ok := expectedResponse.(*dao.K8sProxyResp[dao.PodCountResponse]); ok {
					*respData = *mockResp
				}
			}
		}
		return nil
	})
	return patches
}

func mockRALError(t *testing.T, err error) *gomonkey.Patches {
	patches := gomonkey.NewPatches()
	patches.ApplyFunc(ral.RAL, func(ctx context.Context, name any, req ral.Request, resp ral.Response, opts ...ral.ROption) error {
		return err
	})
	return patches
}

// ===== CreateTask 测试用例 =====

func TestCreateTask_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	req := generateValidCreateTaskRequest()

	// Mock RAL调用成功
	mockResp := &dao.K8sProxyResp[dao.CreateTaskResponse]{
		Code: ErrorCodeSuccess,
		Msg:  "success",
		Data: dao.CreateTaskResponse{
			TaskID: "test-task-12345",
		},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	result, err := client.CreateTask(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-task-12345", result.TaskID)
}

func TestCreateTask_RALError(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	req := generateValidCreateTaskRequest()

	// Mock RAL调用失败
	expectedError := errors.New("network connection failed")
	patches := mockRALError(t, expectedError)
	defer patches.Reset()

	// 执行测试
	result, err := client.CreateTask(ctx, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "network connection failed")
}

func TestCreateTask_InvalidInput(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	req := generateInvalidCreateTaskRequest()

	// Mock RAL调用返回错误码
	mockResp := &dao.K8sProxyResp[dao.CreateTaskResponse]{
		Code: ErrorCodeInvalidInput,
		Msg:  "invalid image path",
		Data: dao.CreateTaskResponse{},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	result, err := client.CreateTask(ctx, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "invalid image path")
}

func TestCreateTask_MarshalError(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	req := &dao.CreateTaskRequest{
		SessionID: 12345,
		Args:      []string{"valid", "args"},
	}

	// Mock json.Marshal失败
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
		return nil, errors.New("marshal failed")
	})

	// 执行测试
	result, err := client.CreateTask(ctx, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "marshal failed")
}

// ===== GetTaskState 测试用例 =====

func TestGetTaskState_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	taskID := "test-task-12345"

	// Mock RAL调用成功
	mockResp := &dao.K8sProxyResp[dao.TaskStateResponse]{
		Code: ErrorCodeSuccess,
		Msg:  "success",
		Data: dao.TaskStateResponse{
			State:     TaskStateSucceeded,
			OutputURL: "https://bos.example.com/output/result.tar.gz",
			Log:       "Task completed successfully",
		},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	result, err := client.GetTaskState(ctx, taskID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, TaskStateSucceeded, result.State)
	assert.Equal(t, "https://bos.example.com/output/result.tar.gz", result.OutputURL)
	assert.Equal(t, "Task completed successfully", result.Log)
}

func TestGetTaskState_TaskNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	taskID := "non-existent-task"

	// Mock RAL调用返回任务不存在错误
	mockResp := &dao.K8sProxyResp[dao.TaskStateResponse]{
		Code: ErrorCodeInternalError,
		Msg:  "job not found",
		Data: dao.TaskStateResponse{},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	result, err := client.GetTaskState(ctx, taskID)

	// 验证结果
	assert.NoError(t, err)
	assert.True(t, result.State == "deleted")
}

// ===== GetTaskLogStream 测试用例 =====

func TestGetTaskLogStream_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	taskID := "test-task-12345"

	// Mock RAL调用成功
	mockResp := &dao.K8sProxyResp[dao.TaskLogResponse]{
		Code: ErrorCodeSuccess,
		Msg:  "success",
		Data: dao.TaskLogResponse{
			Log:     "2024-01-15 10:30:00 INFO: Task started\n2024-01-15 10:30:05 INFO: Processing data\n2024-01-15 10:30:10 INFO: Task completed",
			LogTime: "2024-01-15T10:30:10Z",
		},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	result, err := client.GetTaskLogStream(ctx, taskID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, result.Log, "Task started")
	assert.Contains(t, result.Log, "Task completed")
	assert.Equal(t, "2024-01-15T10:30:10Z", result.LogTime)
}

// ===== DeleteJob 测试用例 =====

func TestDeleteJob_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	taskID := "test-task-12345"

	// Mock RAL调用成功
	mockResp := &dao.K8sProxyResp[map[string]interface{}]{
		Code: ErrorCodeSuccess,
		Msg:  "success",
		Data: map[string]interface{}{
			"deleted": true,
			"task_id": taskID,
		},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	err := client.DeleteJob(ctx, taskID)

	// 验证结果
	assert.NoError(t, err)
}

// ===== GetPodCount 测试用例 =====

func TestGetPodCount_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns
	sourceType := SourceTypeMCP

	// Mock RAL调用成功
	mockResp := &dao.K8sProxyResp[dao.PodCountResponse]{
		Code: ErrorCodeSuccess,
		Msg:  "success",
		Data: dao.PodCountResponse{
			Count: 15,
		},
	}

	patches := mockRALSuccess(t, mockResp)
	defer patches.Reset()

	// 执行测试
	result, err := client.GetPodCount(ctx, sourceType)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 15, result.Count)
}

// ===== 表驱动测试 =====

func TestCreateTask_TableDriven(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	testCases := []struct {
		name           string
		request        *dao.CreateTaskRequest
		mockResponse   *dao.K8sProxyResp[dao.CreateTaskResponse]
		mockError      error
		expectedError  bool
		expectedTaskID string
	}{
		{
			name:    "Valid MCP Task",
			request: generateValidCreateTaskRequest(),
			mockResponse: &dao.K8sProxyResp[dao.CreateTaskResponse]{
				Code: ErrorCodeSuccess,
				Msg:  "success",
				Data: dao.CreateTaskResponse{TaskID: "mcp-task-001"},
			},
			expectedError:  false,
			expectedTaskID: "mcp-task-001",
		},
		{
			name: "Valid Dataeng Task",
			request: &dao.CreateTaskRequest{
				SessionID:  67890,
				ImagePath:  "dataeng/python:3.9",
				Command:    "python",
				Args:       []string{"-m", "data_processor"},
				Timeout:    1800,
				OutputInfo: `{"path_name": "dataeng_output"}`,
				SourceType: SourceTypeDataeng,
				TaskType:   "DATAENG",
				Resources:  `{"cpu": 2, "memory": {"value": 4, "type": "G"}}`,
			},
			mockResponse: &dao.K8sProxyResp[dao.CreateTaskResponse]{
				Code: ErrorCodeSuccess,
				Msg:  "success",
				Data: dao.CreateTaskResponse{TaskID: "dataeng-task-002"},
			},
			expectedError:  false,
			expectedTaskID: "dataeng-task-002",
		},
		{
			name: "Resource Limit Error",
			request: &dao.CreateTaskRequest{
				SessionID:  11111,
				ImagePath:  "heavy/resource-intensive:latest",
				Command:    "/bin/bash",
				SourceType: SourceTypeVerifier,
				TaskType:   "VERIFIER",
			},
			mockResponse: &dao.K8sProxyResp[dao.CreateTaskResponse]{
				Code: ErrorCodeResourceLimit,
				Msg:  "cluster resource limit exceeded",
				Data: dao.CreateTaskResponse{},
			},
			expectedError: true,
		},
		{
			name: "Image Not Found",
			request: &dao.CreateTaskRequest{
				SessionID:  22222,
				ImagePath:  "non-existent/image:latest",
				Command:    "/bin/bash",
				SourceType: SourceTypeMCP,
				TaskType:   "MCP",
			},
			mockResponse: &dao.K8sProxyResp[dao.CreateTaskResponse]{
				Code: ErrorCodeImageNotFound,
				Msg:  "image not found in registry",
				Data: dao.CreateTaskResponse{},
			},
			expectedError: true,
		},
		{
			name: "Network Error",
			request: &dao.CreateTaskRequest{
				SessionID:  33333,
				ImagePath:  "valid/image:latest",
				Command:    "/bin/bash",
				SourceType: SourceTypeMCP,
				TaskType:   "MCP",
			},
			mockError:     errors.New("network connection failed"),
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			client := dao.K8sProxyClientIns

			var patches *gomonkey.Patches
			if tc.mockError != nil {
				patches = mockRALError(t, tc.mockError)
			} else {
				patches = mockRALSuccess(t, tc.mockResponse)
			}
			defer patches.Reset()

			// 执行测试
			result, err := client.CreateTask(ctx, tc.request)

			// 验证结果
			if tc.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tc.expectedTaskID, result.TaskID)
			}
		})
	}
}

// ===== 边界条件测试 =====

func TestCreateTask_BoundaryConditions(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	testCases := []struct {
		name        string
		modifyReq   func(*dao.CreateTaskRequest)
		expectError bool
		description string
	}{
		{
			name: "Empty SessionID",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.SessionID = 0
			},
			expectError: false,
			description: "SessionID为0应该被允许",
		},
		{
			name: "Very Large SessionID",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.SessionID = 9223372036854775807 // int64最大值
			},
			expectError: false,
			description: "极大的SessionID应该被处理",
		},
		{
			name: "Empty ImagePath",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.ImagePath = ""
			},
			expectError: false,
			description: "空镜像路径应该由服务端验证",
		},
		{
			name: "Empty Command",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.Command = ""
			},
			expectError: false,
			description: "空命令应该由服务端验证",
		},
		{
			name: "Zero Timeout",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.Timeout = 0
			},
			expectError: false,
			description: "零超时应该由服务端处理",
		},
		{
			name: "Negative Timeout",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.Timeout = -1
			},
			expectError: false,
			description: "负超时应该由服务端验证",
		},
		{
			name: "Very Large Timeout",
			modifyReq: func(req *dao.CreateTaskRequest) {
				req.Timeout = 2147483647 // int32最大值
			},
			expectError: false,
			description: "极大超时值应该被处理",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			client := dao.K8sProxyClientIns
			req := generateValidCreateTaskRequest()
			tc.modifyReq(req)

			// Mock RAL调用成功
			mockResp := &dao.K8sProxyResp[dao.CreateTaskResponse]{
				Code: ErrorCodeSuccess,
				Msg:  "success",
				Data: dao.CreateTaskResponse{TaskID: "boundary-test-task"},
			}

			patches := mockRALSuccess(t, mockResp)
			defer patches.Reset()

			// 执行测试
			result, err := client.CreateTask(ctx, req)

			// 验证结果
			if tc.expectError {
				assert.Error(t, err, tc.description)
			} else {
				assert.NoError(t, err, tc.description)
				assert.NotNil(t, result, tc.description)
			}
		})
	}
}

// ===== 集成测试 =====

func TestK8sProxyClient_IntegrationWorkflow(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	client := dao.K8sProxyClientIns

	// 模拟完整的工作流程：创建任务 -> 查询状态 -> 获取日志 -> 删除任务
	taskID := "integration-test-task-12345"

	t.Run("Complete Workflow", func(t *testing.T) {
		// Mock RAL调用
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		callCount := 0
		patches.ApplyFunc(ral.RAL, func(ctx context.Context, name any, req ral.Request, resp ral.Response, opts ...ral.ROption) error {
			callCount++

			// 类型断言获取具体的响应对象
			ralResp, ok := resp.(*ghttp.RalResponse)
			assert.True(t, ok)

			// 类型断言获取具体的请求对象
			ralReq, ok := req.(*ghttp.RalRequest)
			assert.True(t, ok)

			switch ralReq.APIName {
			case "CREATE_TASK":
				// 模拟创建任务成功
				mockResp := &dao.K8sProxyResp[dao.CreateTaskResponse]{
					Code: ErrorCodeSuccess,
					Msg:  "success",
					Data: dao.CreateTaskResponse{TaskID: taskID},
				}
				if ralResp.Data != nil {
					respData := ralResp.Data.(*dao.K8sProxyResp[dao.CreateTaskResponse])
					*respData = *mockResp
				}

			case "GET_TASK_STATE":
				// 模拟任务状态查询
				var state string
				if callCount <= 2 {
					state = TaskStateRunning
				} else {
					state = TaskStateSucceeded
				}

				mockResp := &dao.K8sProxyResp[dao.TaskStateResponse]{
					Code: ErrorCodeSuccess,
					Msg:  "success",
					Data: dao.TaskStateResponse{
						State:     state,
						OutputURL: "https://bos.example.com/output.tar.gz",
						Log:       "Task execution completed",
					},
				}
				if ralResp.Data != nil {
					respData := ralResp.Data.(*dao.K8sProxyResp[dao.TaskStateResponse])
					*respData = *mockResp
				}

			case "GET_TASK_LOG_STREAM":
				// 模拟日志获取
				mockResp := &dao.K8sProxyResp[dao.TaskLogResponse]{
					Code: ErrorCodeSuccess,
					Msg:  "success",
					Data: dao.TaskLogResponse{
						Log:     "2024-01-15 10:30:00 INFO: Task started\n2024-01-15 10:30:10 INFO: Task completed",
						LogTime: "2024-01-15T10:30:10Z",
					},
				}
				if ralResp.Data != nil {
					respData := ralResp.Data.(*dao.K8sProxyResp[dao.TaskLogResponse])
					*respData = *mockResp
				}

			case "DELETE_JOB":
				// 模拟删除任务
				mockResp := &dao.K8sProxyResp[map[string]interface{}]{
					Code: ErrorCodeSuccess,
					Msg:  "success",
					Data: map[string]interface{}{"deleted": true},
				}
				if ralResp.Data != nil {
					respData := ralResp.Data.(*dao.K8sProxyResp[map[string]interface{}])
					*respData = *mockResp
				}
			}

			return nil
		})

		// 1. 创建任务
		createReq := generateValidCreateTaskRequest()
		createResult, err := client.CreateTask(ctx, createReq)
		assert.NoError(t, err)
		assert.NotNil(t, createResult)
		assert.Equal(t, taskID, createResult.TaskID)

		// 2. 查询任务状态（模拟轮询）
		stateResult, err := client.GetTaskState(ctx, taskID)
		assert.NoError(t, err)
		assert.NotNil(t, stateResult)
		assert.Contains(t, []string{TaskStateRunning, TaskStateSucceeded}, stateResult.State)

		// 3. 获取任务日志
		logResult, err := client.GetTaskLogStream(ctx, taskID)
		assert.NoError(t, err)
		assert.NotNil(t, logResult)
		assert.Contains(t, logResult.Log, "Task started")

		// 4. 删除任务
		err = client.DeleteJob(ctx, taskID)
		assert.NoError(t, err)

		// 验证所有API都被调用了
		assert.GreaterOrEqual(t, callCount, 4)
	})
}

// ===== 测试套件运行函数 =====

func TestMain(m *testing.M) {
	// 可以在这里添加全局的setup和teardown逻辑
	m.Run()
}

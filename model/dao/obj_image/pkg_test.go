package dao_test

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_obj_image "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/obj_image"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll() {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll() {}

func setUp() {
	dao.TxMysqlMain = dao.CliMysqlMain.Begin()
}

func tearDown() {
	dao.TxMysqlMain.Rollback()
}

func TestObjImageLifecycle(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestCRUD", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		containerEnv := base.JSONData{
			"PYTHONPATH": "/opt/python3.10/bin/python3.10",
			"NODE_ENV":   "production",
		}
		containerArgs := base.JSONArray[string]{
			"--stdio", "--config", "/path/to/config.json",
		}
		containerMounts := base.JSONData{
			"input":  "/home/<USER>/input",
			"output": "/home/<USER>/output",
		}
		containerResources := base.JSONData{
			"cpu":    1,
			"memory": map[string]interface{}{"value": 1, "type": "G"},
		}

		image := &dao_obj_image.ObjImage{
			ImagePath:          "mcp/test-runtime:latest",
			ImageDescription:   "测试MCP运行时镜像",
			ContainerPort:      8080,
			ContainerEnv:       containerEnv,
			ContainerCommand:   "python",
			ContainerArgs:      containerArgs,
			ContainerMounts:    containerMounts,
			ContainerResources: containerResources,
		}

		// 测试插入（不提供ImageID，应该自动生成UUID）
		id, err := dao_obj_image.ObjImageBusinessIns.Insert(ctx, image)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))
		assert.NotEmpty(t, image.ImageID) // 验证自动生成了UUID

		// 测试查询
		result, err := dao_obj_image.ObjImageBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, image.ImagePath, result.ImagePath)
		assert.Equal(t, image.ContainerCommand, result.ContainerCommand)
		assert.Equal(t, image.ContainerPort, result.ContainerPort)
		assert.Equal(t, image.ImageID, result.ImageID) // 验证ImageID一致

		// 测试根据镜像路径查询
		result2, err := dao_obj_image.ObjImageBusinessIns.SelectByImagePath(ctx, image.ImagePath)
		assert.NoError(t, err)
		assert.NotNil(t, result2)
		assert.Equal(t, id, result2.ID)

		// 测试根据ImageID查询
		result3, err := dao_obj_image.ObjImageBusinessIns.SelectByImageID(ctx, image.ImageID)
		assert.NoError(t, err)
		assert.NotNil(t, result3)
		assert.Equal(t, id, result3.ID)

		// 测试更新
		image.ID = id //
		image.ImageDescription = "更新后的描述"
		image.ContainerCommand = "node"
		err = dao_obj_image.ObjImageBusinessIns.Update(ctx, image)
		assert.NoError(t, err)

		// 验证更新
		updated, err := dao_obj_image.ObjImageBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, "更新后的描述", updated.ImageDescription)
		assert.Equal(t, "node", updated.ContainerCommand)

		// 测试删除
		err = dao_obj_image.ObjImageBusinessIns.Delete(ctx, id)
		assert.NoError(t, err)

		// 验证删除（应该查询不到）
		deleted, err := dao_obj_image.ObjImageBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.Error(t, err)
		assert.Nil(t, deleted)
	})

	t.Run("TestBatchQuery", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试镜像
		images := []*dao_obj_image.ObjImage{
			{
				ImagePath:        "mcp/test1:latest",
				ImageDescription: "测试镜像1",
				ContainerCommand: "python",
			},
			{
				ImagePath:        "mcp/test2:latest",
				ImageDescription: "测试镜像2",
				ContainerCommand: "node",
			},
		}

		var ids []int64
		for _, img := range images {
			id, err := dao_obj_image.ObjImageBusinessIns.Insert(ctx, img)
			assert.NoError(t, err)
			assert.NotEmpty(t, img.ImageID) // 验证自动生成了UUID
			ids = append(ids, id)
		}

		// 测试批量查询
		results, err := dao_obj_image.ObjImageBusinessIns.GetMapByPrimaryKeys(ctx, ids)
		assert.NoError(t, err)
		assert.Len(t, results, 2)

		for _, id := range ids {
			assert.Contains(t, results, id)
		}
	})

	t.Run("TestPagination", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		for i := 0; i < 5; i++ {
			image := &dao_obj_image.ObjImage{
				ImagePath:        fmt.Sprintf("mcp/test-page-%d:latest", i),
				ImageDescription: fmt.Sprintf("分页测试镜像%d", i),
				ContainerCommand: "python",
			}
			_, err := dao_obj_image.ObjImageBusinessIns.Insert(ctx, image)
			assert.NoError(t, err)
		}

		// 测试分页查询
		total, images, err := dao_obj_image.ObjImageBusinessIns.SelectByPage(ctx, 1, 3)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
		assert.LessOrEqual(t, len(images), 3)
	})

	t.Run("TestCustomImageID", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 测试提供自定义ImageID
		customImageID := "custom-image-id-123"
		image := &dao_obj_image.ObjImage{
			ImageID:          customImageID,
			ImagePath:        "mcp/custom-test:latest",
			ImageDescription: "自定义ImageID测试",
			ContainerCommand: "python",
		}

		id, err := dao_obj_image.ObjImageBusinessIns.Insert(ctx, image)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))
		assert.Equal(t, customImageID, image.ImageID) // 验证使用了自定义ImageID

		// 测试根据自定义ImageID查询
		result, err := dao_obj_image.ObjImageBusinessIns.SelectByImageID(ctx, customImageID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, id, result.ID)
		assert.Equal(t, customImageID, result.ImageID)
	})
}

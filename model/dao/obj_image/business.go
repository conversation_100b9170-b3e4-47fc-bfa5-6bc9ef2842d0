package dao

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

var ObjImageBusinessIns = ObjImageBusiness{}

type ObjImageBusiness struct{}

// Insert 插入镜像记录
func (bus ObjImageBusiness) Insert(ctx context.Context, data *ObjImage) (int64, error) {
	// 如果image_id为空，自动生成UUID
	if data.ImageID == "" {
		data.ImageID = uuid.New().String()
	}

	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		return 0, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLInsertError)
	}
	return data.ID, nil
}

// InsertInTx 在事务中插入镜像记录
func (bus ObjImageBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjImage) (int64, error) {
	// 如果image_id为空，自动生成UUID
	if data.ImageID == "" {
		data.ImageID = uuid.New().String()
	}

	result := tx.Create(data)
	if result.Error != nil {
		return 0, errcode.NewCustomErr(ctx, result.Error, errcode.SQLInsertError, fmt.Sprintf("Insert image in transaction [imageID=%s, imagePath=%s]", data.ImageID, data.ImagePath))
	}
	return data.ID, nil
}

// SelectByPrimaryKey 根据主键查询镜像
func (bus ObjImageBusiness) SelectByPrimaryKey(ctx context.Context, id int64) (*ObjImage, error) {
	var image ObjImage
	tx := dao.GetDb(ctx).
		Where("id = ? AND is_delete = ?", id, false).
		First(&image)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.ImageNotFound, fmt.Sprintf("镜像id [%d] 不存在", id), "Select image by ID")
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select image by ID")
	}
	return &image, nil
}

// SelectByImageID 根据image_id查询镜像
func (bus ObjImageBusiness) SelectByImageID(ctx context.Context, imageID string) (*ObjImage, error) {
	var image ObjImage
	tx := dao.GetDb(ctx).
		Where("image_id = ? AND is_delete = ?", imageID, false).
		First(&image)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.ImageNotFound, fmt.Sprintf("镜像image_id [%s] 不存在", imageID), "Select image by image ID")
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select image by image ID")
	}
	return &image, nil
}

// SelectByImagePath 根据镜像路径查询镜像
func (bus ObjImageBusiness) SelectByImagePath(ctx context.Context, imagePath string) (*ObjImage, error) {
	var image ObjImage
	tx := dao.GetDb(ctx).
		Where("image_path = ? AND is_delete = ?", imagePath, false).
		First(&image)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.ImageNotFound, "镜像不存在", "Select image by image path")
		}
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Select image by image path")
	}
	return &image, nil
}

// GetMapByPrimaryKeys 根据主键列表批量查询镜像，返回map
func (bus ObjImageBusiness) GetMapByPrimaryKeys(ctx context.Context, ids []int64) (map[int64]*ObjImage, error) {
	var images []*ObjImage
	tx := dao.GetDb(ctx).
		Where("id IN ? AND is_delete = ?", ids, false).
		Find(&images)
	if tx.Error != nil {
		return nil, errcode.NewCustomErr(ctx, tx.Error, errcode.SQLSelectError, "Get map by primary keys")
	}

	result := make(map[int64]*ObjImage)
	for _, image := range images {
		result[image.ID] = image
	}
	return result, nil
}

// Update 更新镜像记录
func (bus ObjImageBusiness) Update(ctx context.Context, data *ObjImage) error {
	tx := dao.GetDb(ctx).Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", data.ID, false).
		Updates(map[string]any{
			"image_id":            data.ImageID,
			"image_path":          data.ImagePath,
			"image_description":   data.ImageDescription,
			"container_port":      data.ContainerPort,
			"container_env":       data.ContainerEnv,
			"container_command":   data.ContainerCommand,
			"container_args":      data.ContainerArgs,
			"container_mounts":    data.ContainerMounts,
			"container_resources": data.ContainerResources,
			"root_user":           data.RootUser,
		})
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLUpdateError, "Update image")
	}
	return nil
}

// UpdateInTx 在事务中更新镜像记录
func (bus ObjImageBusiness) UpdateInTx(ctx context.Context, tx *gorm.DB, data *ObjImage) error {
	result := tx.Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", data.ID, false).
		Updates(map[string]any{
			"image_id":            data.ImageID,
			"image_path":          data.ImagePath,
			"image_description":   data.ImageDescription,
			"container_port":      data.ContainerPort,
			"container_env":       data.ContainerEnv,
			"container_command":   data.ContainerCommand,
			"container_args":      data.ContainerArgs,
			"container_mounts":    data.ContainerMounts,
			"container_resources": data.ContainerResources,
		})
	if result.Error != nil {
		return errcode.NewCustomErr(ctx, result.Error, errcode.SQLUpdateError, "Update image in transaction")
	}
	return nil
}

// Delete 软删除镜像记录
func (bus ObjImageBusiness) Delete(ctx context.Context, id int64) error {
	tx := dao.GetDb(ctx).Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", id, false).
		Update("is_delete", true)
	if tx.Error != nil {
		return errcode.NewCustomErr(ctx, tx.Error, errcode.SQLDeleteError, "Delete image")
	}
	return nil
}

// DeleteInTx 在事务中软删除镜像记录
func (bus ObjImageBusiness) DeleteInTx(ctx context.Context, tx *gorm.DB, id int64) error {
	result := tx.Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", id, false).
		Update("is_delete", true)
	if result.Error != nil {
		return errcode.NewCustomErr(ctx, result.Error, errcode.SQLDeleteError, "Delete image in transaction")
	}
	return nil
}

// SelectByPage 分页查询镜像
func (bus ObjImageBusiness) SelectByPage(ctx context.Context, page, size int64) (int64, []*ObjImage, error) {
	query := dao.GetDb(ctx).Model(&ObjImage{}).
		Where("is_delete = ?", false)

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Select image by page")
	}

	// 分页查询
	var images []*ObjImage
	offset := (page - 1) * size
	err = query.Offset(int(offset)).Limit(int(size)).Find(&images).Error
	if err != nil {
		return 0, nil, errcode.NewCustomErr(ctx, err, errcode.SQLSelectError, "Select image by page")
	}

	return total, images, nil
}

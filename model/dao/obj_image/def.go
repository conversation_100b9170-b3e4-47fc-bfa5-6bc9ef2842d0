package dao

import (
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

type ObjImage struct {
	ID                 int64                 `gorm:"primaryKey;column:id;autoIncrement" json:"id"`
	ImageID            string                `gorm:"column:image_id;not null;default:'';uniqueIndex:uk_image_id" json:"image_id"`
	ImagePath          string                `gorm:"column:image_path;not null;default:''" json:"image_path"`
	ImageDescription   string                `gorm:"column:image_description" json:"image_description"`
	ContainerPort      int                   `gorm:"column:container_port;default:0" json:"container_port"`
	ContainerEnv       dao.JSONData          `gorm:"column:container_env" json:"container_env"`
	ContainerCommand   string                `gorm:"column:container_command;not null;default:''" json:"container_command"`
	ContainerArgs      dao.JSONArray[string] `gorm:"column:container_args" json:"container_args"`
	ContainerMounts    dao.JSONData          `gorm:"column:container_mounts" json:"container_mounts"`
	ContainerResources dao.JSONData          `gorm:"column:container_resources" json:"container_resources"`
	RootUser           bool                  `gorm:"column:root_user;default:false" json:"root_user"`
	CreatedAt          *time.Time            `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3)" json:"created_at"`
	UpdatedAt          *time.Time            `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)" json:"updated_at"`
	IsDelete           bool                  `gorm:"column:is_delete;default:false" json:"is_delete"`
}

func (c *ObjImage) TableName() string {
	return "obj_image"
}

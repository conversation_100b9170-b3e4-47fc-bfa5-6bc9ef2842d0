package dao

import (
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

type ObjMcpEnv struct {
	EnvID         int64        `gorm:"primaryKey;column:env_id;autoIncrement" json:"env_id"`
	EnvMd5        string       `gorm:"column:env_md5;unique;not null" json:"env_md5"`
	Name          string       `gorm:"column:name;default:''" json:"name"`
	Description   string       `gorm:"column:description;default:''" json:"description"`
	BosURL        string       `gorm:"column:bos_url;default:''" json:"bos_url"`
	EnvDependency dao.JSONData `gorm:"column:env_dependency" json:"env_dependency"`
	FileSize      int64        `gorm:"column:file_size" json:"file_size"`
	CreatedAt     *time.Time   `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3)" json:"created_at"`
	UpdatedAt     *time.Time   `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)" json:"updated_at"`
	IsDelete      bool         `gorm:"column:is_delete;default:false" json:"is_delete"`
}

func (c *ObjMcpEnv) TableName() string {
	return "obj_mcp_env"
}

package dao_test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll() {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll() {}

func setUp() {
	dao.TxMysqlMain = dao.CliMysqlMain.Begin()
}

func tearDown() {
	dao.TxMysqlMain.Rollback()
}

func TestMcpEnvLifecycle(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestCRUD", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		envDependency := base.JSONData{
			"python_version":  "3.9",
			"requirements":    []string{"pandas", "numpy", "requests"},
			"system_packages": []string{"git", "curl"},
		}
		fileSize := int64(1024 * 1024) // 1MB

		env := &dao_mcp_env.ObjMcpEnv{
			EnvMd5:        "abc123def456789012345678901234ab",
			Name:          "test_python_env",
			Description:   "Python数据分析环境",
			BosURL:        "https://bos.example.com/envs/python-datascience.tar.gz",
			EnvDependency: envDependency,
			FileSize:      fileSize,
		}

		// 测试插入
		id, err := dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 测试查询
		result, err := dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, env.EnvMd5, result.EnvMd5)
		assert.Equal(t, env.Name, result.Name)
		assert.Equal(t, env.BosURL, result.BosURL)

		// 测试按MD5查询
		resultByMd5, err := dao_mcp_env.McpEnvBusinessIns.SelectByEnvMd5(ctx, env.EnvMd5)
		assert.NoError(t, err)
		assert.NotNil(t, resultByMd5)
		assert.Equal(t, id, resultByMd5.EnvID)

		// 测试更新
		env.EnvID = id
		env.Description = "更新后的Python环境描述"
		env.Name = "updated_python_env"
		newFileSize := int64(2 * 1024 * 1024) // 2MB
		env.FileSize = newFileSize
		err = dao_mcp_env.McpEnvBusinessIns.Update(ctx, env)
		assert.NoError(t, err)

		// 验证更新结果
		updatedResult, err := dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, "更新后的Python环境描述", updatedResult.Description)
		assert.Equal(t, "updated_python_env", updatedResult.Name)
		assert.Equal(t, newFileSize, updatedResult.FileSize)

		// 测试删除
		err = dao_mcp_env.McpEnvBusinessIns.Delete(ctx, id)
		assert.NoError(t, err)

		// 验证删除
		_, err = dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.Error(t, err)
	})

	t.Run("TestInsertInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		envDependency := base.JSONData{
			"node_version": "18.0.0",
			"npm_packages": []string{"express", "lodash", "axios"},
		}
		fileSize := int64(512 * 1024) // 512KB

		env := &dao_mcp_env.ObjMcpEnv{
			EnvMd5:        "nodejs123456789012345678901234",
			Name:          "test_nodejs_env_tx",
			Description:   "Node.js测试环境（事务）",
			BosURL:        "https://bos.example.com/envs/nodejs-basic.tar.gz",
			EnvDependency: envDependency,
			FileSize:      fileSize,
		}

		// 开启事务
		tx := dao.GetDb(ctx)

		// 测试事务中插入
		id, err := dao_mcp_env.McpEnvBusinessIns.InsertInTx(ctx, tx, env)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 验证插入结果
		result, err := dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, env.EnvMd5, result.EnvMd5)
		assert.Equal(t, env.Name, result.Name)
	})

	t.Run("TestCheckEnvMd5Exist", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		envDependency := base.JSONData{
			"java_version":       "11",
			"maven_dependencies": []string{"spring-boot", "junit"},
		}
		fileSize := int64(256 * 1024) // 256KB

		env := &dao_mcp_env.ObjMcpEnv{
			EnvMd5:        "java123456789012345678901234567",
			Name:          "test_java_env",
			Description:   "Java开发环境",
			BosURL:        "https://bos.example.com/envs/java-dev.tar.gz",
			EnvDependency: envDependency,
			FileSize:      fileSize,
		}

		// 插入测试数据
		id, err := dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 测试场景1: 检查同MD5环境是否存在（不排除任何ID）
		exist, err := dao_mcp_env.McpEnvBusinessIns.CheckEnvMd5Exist(ctx, env.EnvMd5, 0)
		assert.NoError(t, err)
		assert.True(t, exist)

		// 测试场景2: 检查同MD5环境是否存在（排除当前ID）
		exist, err = dao_mcp_env.McpEnvBusinessIns.CheckEnvMd5Exist(ctx, env.EnvMd5, id)
		assert.NoError(t, err)
		assert.False(t, exist)

		// 测试场景3: 检查不存在的环境MD5
		exist, err = dao_mcp_env.McpEnvBusinessIns.CheckEnvMd5Exist(ctx, "non_existent_md5", 0)
		assert.NoError(t, err)
		assert.False(t, exist)
	})

	t.Run("TestUpdateInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		tx := dao.GetDb(ctx)
		now := time.Now().UnixNano()

		envDependency := base.JSONData{
			"go_version": "1.21",
			"modules":    []string{"gin", "gorm", "redis"},
		}
		fileSize := int64(128 * 1024) // 128KB

		env := &dao_mcp_env.ObjMcpEnv{
			EnvMd5:        fmt.Sprintf("go_tx_%d", now%1000000),
			Name:          fmt.Sprintf("test_go_env_tx_%d", now),
			Description:   "Go开发环境（事务测试）",
			BosURL:        "https://bos.example.com/envs/go-dev.tar.gz",
			EnvDependency: envDependency,
			FileSize:      fileSize,
		}

		id, err := dao_mcp_env.McpEnvBusinessIns.InsertInTx(ctx, tx, env)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		env.EnvID = id
		env.Description = "事务更新后的Go环境"
		env.Name = "updated_go_env_tx"
		err = dao_mcp_env.McpEnvBusinessIns.UpdateInTx(ctx, tx, env)
		assert.NoError(t, err)
	})
}

func TestMcpEnvGetMapByPrimaryKeys(t *testing.T) {
	setUpAll()
	defer tearDownAll()
	setUp()
	defer tearDown()

	// 创建多个测试环境
	envs := []*dao_mcp_env.ObjMcpEnv{
		{
			EnvMd5:      "multi_env_test_1",
			Name:        "test_env_multi_1",
			Description: "多环境测试1",
			BosURL:      "https://bos.example.com/env1.tar.gz",
		},
		{
			EnvMd5:      "multi_env_test_2",
			Name:        "test_env_multi_2",
			Description: "多环境测试2",
			BosURL:      "https://bos.example.com/env2.tar.gz",
		},
		{
			EnvMd5:      "multi_env_test_3",
			Name:        "test_env_multi_3",
			Description: "多环境测试3",
			BosURL:      "https://bos.example.com/env3.tar.gz",
		},
	}

	var envIDs []int64
	// 插入测试数据
	for _, env := range envs {
		id, err := dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))
		envIDs = append(envIDs, id)
	}

	// 测试场景1: 查询多个环境
	envsMap, err := dao_mcp_env.McpEnvBusinessIns.GetMapByPrimaryKeys(ctx, envIDs)
	assert.NoError(t, err)
	assert.Equal(t, 3, len(envsMap))
	assert.NotNil(t, envsMap[envIDs[0]])
	assert.NotNil(t, envsMap[envIDs[1]])
	assert.NotNil(t, envsMap[envIDs[2]])
	assert.Equal(t, envs[0].Name, envsMap[envIDs[0]].Name)
	assert.Equal(t, envs[1].Name, envsMap[envIDs[1]].Name)
	assert.Equal(t, envs[2].Name, envsMap[envIDs[2]].Name)

	// 测试场景2: 包含不存在的ID
	nonExistIDs := []int64{envIDs[0], 99999, envIDs[2]}
	envsMap2, err := dao_mcp_env.McpEnvBusinessIns.GetMapByPrimaryKeys(ctx, nonExistIDs)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(envsMap2))
	assert.NotNil(t, envsMap2[envIDs[0]])
	assert.Nil(t, envsMap2[99999])
	assert.NotNil(t, envsMap2[envIDs[2]])

	// 测试场景3: 空ID列表
	emptyIDs := []int64{}
	envsMap3, err := dao_mcp_env.McpEnvBusinessIns.GetMapByPrimaryKeys(ctx, emptyIDs)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(envsMap3))
}

func TestMcpEnvQueries(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestSelectAllActive", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试环境
		env := &dao_mcp_env.ObjMcpEnv{
			EnvMd5:      "active_env_test",
			Name:        "test_active_env",
			Description: "活跃环境测试",
			BosURL:      "https://bos.example.com/active-env.tar.gz",
		}

		id, err := dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
		assert.NoError(t, err)

		// 查询所有活跃环境
		envs, err := dao_mcp_env.McpEnvBusinessIns.SelectAllActive(ctx)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(envs), 1)

		// 验证包含新创建的环境
		found := false
		for _, e := range envs {
			if e.EnvID == id {
				found = true
				assert.Equal(t, env.Name, e.Name)
				break
			}
		}
		assert.True(t, found)
	})

	t.Run("TestSelectByPage", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试环境
		envPrefix := fmt.Sprintf("page_%d", time.Now().UnixNano()%1000000)
		for i := 0; i < 5; i++ {
			env := &dao_mcp_env.ObjMcpEnv{
				EnvMd5:      fmt.Sprintf("%s_%d", envPrefix, i),
				Name:        fmt.Sprintf("test_page_env_%s_%d", envPrefix, i),
				Description: fmt.Sprintf("分页测试环境%d", i),
				BosURL:      fmt.Sprintf("https://bos.example.com/page%d.tar.gz", i),
			}
			_, err := dao_mcp_env.McpEnvBusinessIns.Insert(ctx, env)
			assert.NoError(t, err)
		}

		// 测试分页查询（无关键词）
		total, envs, err := dao_mcp_env.McpEnvBusinessIns.SelectByPage(ctx, 1, 10, "")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
		assert.GreaterOrEqual(t, len(envs), 5)

		// 测试分页查询（带关键词）
		total, envs, err = dao_mcp_env.McpEnvBusinessIns.SelectByPage(ctx, 1, 10, "test_page_env")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))

		// 测试分页功能
		total, envs, err = dao_mcp_env.McpEnvBusinessIns.SelectByPage(ctx, 1, 3, "")
		assert.NoError(t, err)
		assert.LessOrEqual(t, len(envs), 3)

		// 测试关键词搜索
		total, envs, err = dao_mcp_env.McpEnvBusinessIns.SelectByPage(ctx, 1, 10, "分页测试")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
	})

	t.Run("TestDeleteInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试环境
		env := &dao_mcp_env.ObjMcpEnv{
			EnvMd5:      "delete_tx_env_test",
			Name:        "test_delete_tx_env",
			Description: "事务删除测试",
			BosURL:      "https://bos.example.com/delete-tx.tar.gz",
		}

		tx := dao.GetDb(ctx)
		id, err := dao_mcp_env.McpEnvBusinessIns.InsertInTx(ctx, tx, env)
		assert.NoError(t, err)

		// 在事务中删除
		err = dao_mcp_env.McpEnvBusinessIns.DeleteInTx(ctx, tx, id)
		assert.NoError(t, err)
	})
}

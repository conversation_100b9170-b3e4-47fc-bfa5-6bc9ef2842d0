package dao

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	mcp_env_dao "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
)

// PackageDirectoryResult 目录打包结果
type PackageDirectoryResult struct {
	MD5        string `json:"md5"`         // 压缩包的MD5值
	BosURL     string `json:"bos_url"`     // BOS下载链接
	FileSize   int64  `json:"file_size"`   // 文件大小（字节）
	BosKey     string `json:"bos_key"`     // BOS对象键
	EnvID      int64  `json:"env_id"`      // 环境ID（如果已存在）
	IsExisting bool   `json:"is_existing"` // 是否是已存在的环境
}

// PackageDirectoryToBos 将指定目录打包并上传到BOS，支持MD5去重
//
// 参数：
//   - dirPath: 要打包的目录路径
//   - envName: 环境名称（可选）
//   - envDescription: 环境描述（可选）
//   - envDependency: 环境依赖配置（可选）
//
// 返回：
//   - PackageDirectoryResult: 打包结果，包含MD5、BOS链接等信息
//   - error: 错误信息
func PackageDirectoryToBos(ctx context.Context, dirPath, envName, envDescription string, envDependency base.JSONData) (*PackageDirectoryResult, error) {
	// 1. 验证目录是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return nil, &lib_error.CustomErr{Code: errcode.SysFileNotInit, Msg: fmt.Sprintf("目录不存在: %s", dirPath)}
	}

	// 2. 创建临时文件用于存储压缩包
	tempFile, err := createTempTarGzFile()
	if err != nil {
		return nil, err
	}
	defer os.Remove(tempFile.Name()) // 确保清理临时文件
	defer tempFile.Close()

	// 3. 打包目录为tar.gz
	err = packDirectoryToTarGz(dirPath, tempFile)
	if err != nil {
		return nil, err
	}

	// 4. 计算压缩包的MD5
	md5Hash, fileSize, err := calculateFileMD5AndSize(tempFile.Name())
	if err != nil {
		return nil, err
	}

	// 5. 检查MD5是否已存在于env表中
	existingEnv, err := mcp_env_dao.McpEnvBusinessIns.SelectByEnvMd5(ctx, md5Hash)
	if err != nil {
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: fmt.Sprintf("检查环境MD5失败: %s", err.Error())}
	}

	// 6. 如果MD5已存在，直接返回现有记录
	if existingEnv != nil {
		resource.LoggerService.Notice(ctx, fmt.Sprintf("环境MD5已存在，复用现有记录: md5=%s, env_id=%d", md5Hash, existingEnv.EnvID))

		// 生成BOS访问链接
		bosKey := extractBosKeyFromURL(existingEnv.BosURL)
		bosURL := GenerateVisitURL(ctx, bosKey, -1)

		return &PackageDirectoryResult{
			MD5:        existingEnv.EnvMd5,
			BosURL:     bosURL,
			FileSize:   existingEnv.FileSize,
			BosKey:     bosKey,
			EnvID:      existingEnv.EnvID,
			IsExisting: true,
		}, nil
	}

	// 7. MD5不存在，需要上传新的压缩包
	bosKey := generateBosKey(md5Hash)

	// 8. 重新定位到文件开头并上传到BOS
	_, err = tempFile.Seek(0, 0)
	if err != nil {
		return nil, &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("文件定位失败: %s", err.Error())}
	}

	err = UploadObjectFromStream(ctx, bosKey, tempFile)
	if err != nil {
		return nil, err
	}

	// 9. 生成BOS访问链接
	bosURL := GenerateVisitURL(ctx, bosKey, -1)

	// 10. 保存到env表
	newEnv := &mcp_env_dao.ObjMcpEnv{
		EnvMd5:        md5Hash,
		Name:          envName,
		Description:   envDescription,
		BosURL:        bosURL,
		EnvDependency: envDependency,
		FileSize:      fileSize,
	}

	envID, err := mcp_env_dao.McpEnvBusinessIns.Insert(ctx, newEnv)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("保存环境记录失败: %s", err.Error()))
		// 即使保存失败，也返回打包结果，因为文件已经上传成功
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("目录打包上传成功: dir=%s, md5=%s, size=%d, bos_key=%s, env_id=%d",
		dirPath, md5Hash, fileSize, bosKey, envID))

	return &PackageDirectoryResult{
		MD5:        md5Hash,
		BosURL:     bosURL,
		FileSize:   fileSize,
		BosKey:     bosKey,
		EnvID:      envID,
		IsExisting: false,
	}, nil
}

// createTempTarGzFile 创建临时tar.gz文件
func createTempTarGzFile() (*os.File, error) {
	tempFile, err := os.CreateTemp("", "env_*.tar.gz")
	if err != nil {
		return nil, &lib_error.CustomErr{Code: errcode.SysFileCreateError, Msg: fmt.Sprintf("创建临时文件失败: %s", err.Error())}
	}
	return tempFile, nil
}

// packDirectoryToTarGz 将目录打包为tar.gz格式
func packDirectoryToTarGz(srcDir string, destFile *os.File) error {
	// 创建gzip writer
	gzipWriter := gzip.NewWriter(destFile)
	defer gzipWriter.Close()

	// 创建tar writer
	tarWriter := tar.NewWriter(gzipWriter)
	defer tarWriter.Close()

	// 遍历目录并添加到tar包
	return filepath.Walk(srcDir, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 创建tar header
		header, err := tar.FileInfoHeader(info, "")
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("创建tar header失败: %s", err.Error())}
		}

		// 设置文件在tar包中的路径（相对路径）
		relPath, err := filepath.Rel(srcDir, filePath)
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("获取相对路径失败: %s", err.Error())}
		}
		header.Name = relPath

		// 写入header
		if err := tarWriter.WriteHeader(header); err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileCreateError, Msg: fmt.Sprintf("写入tar header失败: %s", err.Error())}
		}

		// 如果是目录，不需要写入内容
		if info.IsDir() {
			return nil
		}

		// 如果是文件，写入文件内容
		file, err := os.Open(filePath)
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileOpenError, Msg: fmt.Sprintf("打开文件失败: %s", err.Error())}
		}
		defer file.Close()

		_, err = io.Copy(tarWriter, file)
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("拷贝文件内容失败: %s", err.Error())}
		}

		return nil
	})
}

// calculateFileMD5AndSize 计算文件的MD5值和大小
func calculateFileMD5AndSize(filePath string) (string, int64, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", 0, &lib_error.CustomErr{Code: errcode.SysFileOpenError, Msg: fmt.Sprintf("打开文件失败: %s", err.Error())}
	}
	defer file.Close()

	// 获取文件大小
	stat, err := file.Stat()
	if err != nil {
		return "", 0, &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("获取文件信息失败: %s", err.Error())}
	}
	fileSize := stat.Size()

	// 计算MD5
	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", 0, &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("读取文件计算MD5失败: %s", err.Error())}
	}

	md5Hash := fmt.Sprintf("%x", hash.Sum(nil))
	return md5Hash, fileSize, nil
}

// generateBosKey 根据MD5生成BOS对象键
func generateBosKey(md5Hash string) string {
	return fmt.Sprintf("mcp_env/%s.tar.gz", md5Hash)
}

// extractBosKeyFromURL 从BOS URL中提取对象键
func extractBosKeyFromURL(bosURL string) string {
	// 这里需要根据实际的BOS URL格式来提取键名
	// 假设URL格式类似: https://bucket.endpoint/path/to/object
	// 我们需要提取path/to/object部分

	// 简单实现：查找最后一个'/'之后的部分作为键名
	// 在实际使用中，可能需要更复杂的逻辑来正确解析BOS URL
	parts := strings.Split(bosURL, "/")
	if len(parts) > 0 {
		// 返回从域名后开始的路径部分
		for i, part := range parts {
			if strings.Contains(part, ".") && i < len(parts)-1 {
				return strings.Join(parts[i+1:], "/")
			}
		}
		return parts[len(parts)-1]
	}
	return ""
}

// DownloadEnvFromBos 从BOS下载环境压缩包到指定目录
//
// 参数：
//   - envMd5: 环境MD5值
//   - destDir: 目标解压目录
//
// 返回：
//   - error: 错误信息
func DownloadEnvFromBos(ctx context.Context, envMd5, destDir string) error {
	// 1. 根据MD5查询环境记录
	env, err := mcp_env_dao.McpEnvBusinessIns.SelectByEnvMd5(ctx, envMd5)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: fmt.Sprintf("查询环境记录失败: %s", err.Error())}
	}
	if env == nil {
		return &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: fmt.Sprintf("环境不存在: md5=%s", envMd5)}
	}

	// 2. 提取BOS键
	bosKey := extractBosKeyFromURL(env.BosURL)
	if bosKey == "" {
		return &lib_error.CustomErr{Code: errcode.BosGetObjectError, Msg: "无法从BOS URL提取对象键"}
	}

	// 3. 创建目标目录
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return &lib_error.CustomErr{Code: errcode.SysDirCreateError, Msg: fmt.Sprintf("创建目标目录失败: %s", err.Error())}
	}

	// 4. 下载文件到临时位置
	tempFilePath := filepath.Join(destDir, fmt.Sprintf("temp_%s.tar.gz", envMd5))
	err = GetObjectToFile(ctx, bosKey, tempFilePath)
	if err != nil {
		return err
	}
	defer os.Remove(tempFilePath) // 清理临时文件

	// 5. 解压文件
	err = extractTarGz(tempFilePath, destDir)
	if err != nil {
		return err
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("环境下载解压成功: md5=%s, dest_dir=%s", envMd5, destDir))
	return nil
}

// extractTarGz 解压tar.gz文件到指定目录
func extractTarGz(src, dest string) error {
	// 打开压缩文件
	file, err := os.Open(src)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.SysFileOpenError, Msg: fmt.Sprintf("打开压缩文件失败: %s", err.Error())}
	}
	defer file.Close()

	// 创建gzip reader
	gzipReader, err := gzip.NewReader(file)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("创建gzip reader失败: %s", err.Error())}
	}
	defer gzipReader.Close()

	// 创建tar reader
	tarReader := tar.NewReader(gzipReader)

	// 解压所有文件
	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break // 解压完成
		}
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("读取tar header失败: %s", err.Error())}
		}

		// 构建目标文件路径
		targetPath := filepath.Join(dest, header.Name)

		// 确保目标目录存在
		if header.Typeflag == tar.TypeDir {
			if err := os.MkdirAll(targetPath, os.FileMode(header.Mode)); err != nil {
				return &lib_error.CustomErr{Code: errcode.SysDirCreateError, Msg: fmt.Sprintf("创建目录失败: %s", err.Error())}
			}
			continue
		}

		// 确保父目录存在
		if err := os.MkdirAll(filepath.Dir(targetPath), 0755); err != nil {
			return &lib_error.CustomErr{Code: errcode.SysDirCreateError, Msg: fmt.Sprintf("创建父目录失败: %s", err.Error())}
		}

		// 创建文件
		targetFile, err := os.OpenFile(targetPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, os.FileMode(header.Mode))
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileCreateError, Msg: fmt.Sprintf("创建文件失败: %s", err.Error())}
		}

		// 拷贝文件内容
		_, err = io.Copy(targetFile, tarReader)
		targetFile.Close()
		if err != nil {
			return &lib_error.CustomErr{Code: errcode.SysFileCreateError, Msg: fmt.Sprintf("写入文件内容失败: %s", err.Error())}
		}
	}

	return nil
}

// CheckEnvExistsByMD5 检查指定MD5的环境是否存在
//
// 参数：
//   - md5Hash: 环境MD5值
//
// 返回：
//   - bool: 是否存在
//   - *mcp_env_dao.ObjMcpEnv: 环境对象（如果存在）
//   - error: 错误信息
func CheckEnvExistsByMD5(ctx context.Context, md5Hash string) (bool, *mcp_env_dao.ObjMcpEnv, error) {
	env, err := mcp_env_dao.McpEnvBusinessIns.SelectByEnvMd5(ctx, md5Hash)
	if err != nil {
		return false, nil, err
	}
	if env == nil {
		return false, nil, nil
	}
	return true, env, nil
}

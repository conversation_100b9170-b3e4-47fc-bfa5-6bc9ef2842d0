package environment

import (
	"context"
	"testing"
)

func TestURLStrategy_CanHandle(t *testing.T) {
	strategy := NewURLStrategy()

	tests := []struct {
		depType  string
		expected bool
	}{
		{"url", true},
		{"URL", false}, // 区分大小写
		{"file", false},
		{"directory", false},
		{"db", false},
		{"", false},
	}

	for _, test := range tests {
		t.Run(test.depType, func(t *testing.T) {
			result := strategy.CanHandle(test.depType)
			if result != test.expected {
				t.<PERSON>rrorf("CanHandle(%q) = %v, expected %v", test.depType, result, test.expected)
			}
		})
	}
}

func TestURLStrategy_GetName(t *testing.T) {
	strategy := NewURLStrategy()
	expected := "URLStrategy"

	result := strategy.GetName()
	if result != expected {
		t.Errorf("GetName() = %q, expected %q", result, expected)
	}
}

func TestURLStrategy_Initialize_EmptyContent(t *testing.T) {
	strategy := NewURLStrategy()
	dep := EnvironmentDependency{
		Type:    "url",
		Path:    "./test.txt",
		Content: "",
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err == nil {
		t.Error("Expected error for empty URL content, got nil")
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Success {
		t.Error("Expected Success to be false")
	}

	if result.ErrorMessage == "" {
		t.Error("Expected error message, got empty string")
	}
}

func TestURLStrategy_Initialize_InvalidURL(t *testing.T) {
	strategy := NewURLStrategy()
	dep := EnvironmentDependency{
		Type:    "url",
		Path:    "./test.txt",
		Content: "invalid-url",
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err == nil {
		t.Error("Expected error for invalid URL, got nil")
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Success {
		t.Error("Expected Success to be false")
	}

	if result.ErrorMessage == "" {
		t.Error("Expected error message, got empty string")
	}
}

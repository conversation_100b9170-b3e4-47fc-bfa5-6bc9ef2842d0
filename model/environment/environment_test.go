package environment

import (
	"context"
	"os"
	"testing"
	"time"
)

func TestDirectoryStrategy(t *testing.T) {
	strategy := NewDirectoryStrategy()

	// 测试能否处理目录类型
	if !strategy.CanHandle("directory") {
		t.<PERSON>rror("DirectoryStrategy应该能处理directory类型")
	}

	if !strategy.CanHandle("dir") {
		t.<PERSON>r("DirectoryStrategy应该能处理dir类型")
	}

	if strategy.CanHandle("file") {
		t.<PERSON>r("DirectoryStrategy不应该处理file类型")
	}

	// 测试目录创建
	testDir := "./test_dir_" + time.Now().Format("20060102150405")
	defer os.RemoveAll(testDir)

	dep := EnvironmentDependency{
		Path: testDir,
		Type: "directory",
	}

	result, err := strategy.Initialize(context.Background(), dep)
	if err != nil {
		t.Fatalf("目录创建失败: %v", err)
	}

	if !result.Success {
		t.<PERSON>("目录创建应该成功, 错误: %s", result.ErrorMessage)
	}

	// 验证目录是否真的被创建
	if info, err := os.Stat(testDir); err != nil || !info.IsDir() {
		t.Error("目录未被正确创建")
	}
}

func TestFileStrategy(t *testing.T) {
	strategy := NewFileStrategy()

	// 测试能否处理文件类型
	if !strategy.CanHandle("file") {
		t.Error("FileStrategy应该能处理file类型")
	}

	if strategy.CanHandle("directory") {
		t.Error("FileStrategy不应该处理directory类型")
	}

	// 测试文件创建
	testFile := "./test_file_" + time.Now().Format("20060102150405") + ".txt"
	defer os.Remove(testFile)

	testContent := "这是测试内容\n包含多行文本"
	dep := EnvironmentDependency{
		Path:    testFile,
		Type:    "file",
		Content: testContent,
	}

	result, err := strategy.Initialize(context.Background(), dep)
	if err != nil {
		t.Fatalf("文件创建失败: %v", err)
	}

	if !result.Success {
		t.Errorf("文件创建应该成功, 错误: %s", result.ErrorMessage)
	}

	// 验证文件是否真的被创建且内容正确
	content, err := os.ReadFile(testFile)
	if err != nil {
		t.Error("文件未被正确创建")
	}

	if string(content) != testContent {
		t.Errorf("文件内容不匹配, 期望: %s, 实际: %s", testContent, string(content))
	}
}

func TestDatabaseStrategy(t *testing.T) {
	strategy := NewDatabaseStrategy()

	// 测试能否处理数据库类型
	if !strategy.CanHandle("db") {
		t.Error("DatabaseStrategy应该能处理db类型")
	}

	if !strategy.CanHandle("database") {
		t.Error("DatabaseStrategy应该能处理database类型")
	}

	if strategy.CanHandle("file") {
		t.Error("DatabaseStrategy不应该处理file类型")
	}

	// 测试数据库创建
	testDB := "./test_db_" + time.Now().Format("20060102150405") + ".db"
	defer os.Remove(testDB)

	sqlContent := `
		-- 创建测试表
		CREATE TABLE users (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL,
			email TEXT UNIQUE
		);
		
		-- 插入测试数据
		INSERT INTO users (name, email) VALUES ('测试用户', '<EMAIL>');
	`

	dep := EnvironmentDependency{
		Path:    testDB,
		Type:    "db",
		Content: sqlContent,
	}

	result, err := strategy.Initialize(context.Background(), dep)
	if err != nil {
		t.Fatalf("数据库创建失败: %v", err)
	}

	if !result.Success {
		t.Errorf("数据库创建应该成功, 错误: %s", result.ErrorMessage)
	}

	// 验证数据库文件是否被创建
	if _, err := os.Stat(testDB); err != nil {
		t.Error("数据库文件未被创建")
	}
}

func TestManager(t *testing.T) {
	manager := DefaultManager()

	// 测试策略注册
	strategies := manager.GetRegisteredStrategies()
	if len(strategies) != 3 {
		t.Errorf("应该注册3个策略, 实际: %d", len(strategies))
	}

	// 测试混合环境初始化
	testDir := "./test_manager_" + time.Now().Format("20060102150405")
	defer os.RemoveAll(testDir)

	deps := []EnvironmentDependency{
		{
			Path: testDir,
			Type: "directory",
		},
		{
			Path:    testDir + "/test.txt",
			Type:    "file",
			Content: "测试文件内容",
		},
		{
			Path:    testDir + "/test.db",
			Type:    "db",
			Content: "CREATE TABLE test (id INTEGER PRIMARY KEY);",
		},
	}

	results, err := manager.Initialize(context.Background(), deps)
	if err != nil {
		t.Fatalf("环境初始化失败: %v", err)
	}

	if len(results) != 3 {
		t.Errorf("应该返回3个结果, 实际: %d", len(results))
	}

	for i, result := range results {
		if !result.Success {
			t.Errorf("第%d个初始化应该成功: %s", i+1, result.ErrorMessage)
		}
	}
}

func TestParseSQLStatements(t *testing.T) {
	strategy := NewDatabaseStrategy()

	sqlContent := `
		-- 这是注释
		CREATE TABLE users (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL
		);
		
		/* 这是块注释 */
		INSERT INTO users (name) VALUES ('用户1'); -- 行内注释
		INSERT INTO users (name) VALUES ('用户2');
		
		/* 多行
		   块注释 */
		SELECT * FROM users
	`

	statements := strategy.parseSQLStatements(sqlContent)

	expectedCount := 4 // CREATE, INSERT, INSERT, SELECT
	if len(statements) != expectedCount {
		t.Errorf("应该解析出%d条语句, 实际: %d", expectedCount, len(statements))
		for i, stmt := range statements {
			t.Logf("语句%d: %s", i+1, stmt)
		}
	}
}

package environment

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// URLStrategy URL下载策略
type URLStrategy struct{}

// NewURLStrategy 创建URL策略实例
func NewURLStrategy() *URLStrategy {
	return &URLStrategy{}
}

// CanHandle 判断是否能处理指定类型的依赖
func (s *URLStrategy) CanHandle(depType string) bool {
	return depType == "url"
}

// GetName 获取策略名称
func (s *URLStrategy) GetName() string {
	return "URLStrategy"
}

// Initialize 执行URL文件下载
func (s *URLStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	if dep.Content == "" {
		result.ErrorMessage = "URL内容不能为空"
		return result, fmt.Errorf("URL内容不能为空")
	}

	// 转换为相对路径（移除开头的斜杠）
	filePath := dep.Path
	if strings.HasPrefix(filePath, "/") {
		filePath = "." + filePath
	}

	// 检查文件是否已存在
	if info, err := os.Stat(filePath); err == nil {
		if info.IsDir() {
			return result, fmt.Errorf("路径存在但是目录，不是文件: %s", filePath)
		} else {
			result.Success = true
			result.Details = fmt.Sprintf("文件已存在: %s, 大小: %d bytes", filePath, info.Size())
			return result, nil
		}
	}

	// 创建目录（如果需要）
	dir := filepath.Dir(filePath)
	if dir != "." && dir != "" {
		if err := os.MkdirAll(dir, 0755); err != nil {
			result.ErrorMessage = fmt.Sprintf("创建文件目录失败: %v", err)
			return result, fmt.Errorf("创建文件目录失败 %s: %w", dir, err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", dep.Content, nil)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("创建HTTP请求失败: %v", err)
		return result, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "MCP-Environment/1.0")

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("下载文件失败: %v", err)
		return result, fmt.Errorf("下载文件失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		result.ErrorMessage = fmt.Sprintf("下载失败，HTTP状态码: %d", resp.StatusCode)
		return result, fmt.Errorf("下载失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 创建本地文件
	file, err := os.Create(filePath)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("创建本地文件失败: %v", err)
		return result, fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer file.Close()

	// 复制文件内容
	bytesWritten, err := io.Copy(file, resp.Body)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("写入文件失败: %v", err)
		// 清理失败的文件
		os.Remove(filePath)
		return result, fmt.Errorf("写入文件失败: %w", err)
	}

	// 验证文件是否下载成功
	if info, err := os.Stat(filePath); err != nil {
		result.ErrorMessage = "文件下载后验证失败"
		return result, fmt.Errorf("文件下载后验证失败: %w", err)
	} else if info.IsDir() {
		result.ErrorMessage = "下载的路径是目录，不是文件"
		return result, fmt.Errorf("下载的路径是目录，不是文件: %s", filePath)
	} else {
		result.Success = true
		contentType := resp.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "未知"
		}
		result.Details = fmt.Sprintf("成功下载文件: %s, 大小: %d bytes, 内容类型: %s, URL: %s",
			filePath, bytesWritten, contentType, dep.Content)
	}

	return result, nil
}

package environment

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// FileStrategy 文件创建策略
type FileStrategy struct{}

// NewFileStrategy 创建文件策略实例
func NewFileStrategy() *FileStrategy {
	return &FileStrategy{}
}

// CanHandle 判断是否能处理指定类型的依赖
func (s *FileStrategy) CanHandle(depType string) bool {
	return depType == "file"
}

// GetName 获取策略名称
func (s *FileStrategy) GetName() string {
	return "FileStrategy"
}

// Initialize 执行文件创建
func (s *FileStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	// 转换为相对路径（移除开头的斜杠）
	filePath := dep.Path
	if strings.HasPrefix(filePath, "/") {
		filePath = "." + filePath
	}

	// 检查文件是否已存在
	if info, err := os.Stat(filePath); err == nil {
		if info.IsDir() {
			return result, fmt.Errorf("路径存在但是目录，不是文件: %s", filePath)
		} else {
			result.Success = true
			result.Details = fmt.Sprintf("文件已存在: %s, 大小: %d bytes", filePath, info.Size())
			return result, nil
		}
	}

	// 创建目录（如果需要）
	dir := filepath.Dir(filePath)
	if dir != "." && dir != "" {
		if err := os.MkdirAll(dir, 0755); err != nil {
			result.ErrorMessage = fmt.Sprintf("创建文件目录失败: %v", err)
			return result, fmt.Errorf("创建文件目录失败 %s: %w", dir, err)
		}
	}

	// 创建文件
	content := []byte(dep.Content)
	if err := os.WriteFile(filePath, content, 0644); err != nil {
		result.ErrorMessage = fmt.Sprintf("创建文件失败: %v", err)
		return result, fmt.Errorf("创建文件失败 %s: %w", filePath, err)
	}

	// 验证文件是否创建成功
	if info, err := os.Stat(filePath); err != nil {
		result.ErrorMessage = "文件创建后验证失败"
		return result, fmt.Errorf("文件创建后验证失败: %w", err)
	} else if info.IsDir() {
		result.ErrorMessage = "创建的路径是目录，不是文件"
		return result, fmt.Errorf("创建的路径是目录，不是文件: %s", filePath)
	} else {
		result.Success = true
		result.Details = fmt.Sprintf("成功创建文件: %s, 大小: %d bytes, 权限: 0644",
			filePath, info.Size())
	}

	return result, nil
}

package environment

import "github.com/charmbracelet/log"

// DefaultManager 创建包含所有默认策略的管理器
func DefaultManager() *Manager {
	manager := NewManager()

	// 注册所有默认策略
	manager.RegisterStrategy(NewDirectoryStrategy())
	manager.RegisterStrategy(NewFileStrategy())
	manager.RegisterStrategy(NewDatabaseStrategy())
	manager.RegisterStrategy(NewURLStrategy())
	manager.RegisterStrategy(NewBinaryStrategy())

	return manager
}

// DefaultManagerWithLogger 创建包含所有默认策略的管理器，并设置日志记录器
func DefaultManagerWithLogger(logger *log.Logger) *Manager {
	manager := DefaultManager()
	manager.SetLogger(logger)
	return manager
}

// GetAvailableStrategies 获取所有可用策略的类型列表
func GetAvailableStrategies() map[string][]string {
	return map[string][]string{
		"DirectoryStrategy": {"directory", "dir"},
		"FileStrategy":      {"file"},
		"DatabaseStrategy":  {"db", "database"},
		"URLStrategy":       {"url"},
		"BinaryStrategy":    {"binary"},
	}
}

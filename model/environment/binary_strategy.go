package environment

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// BinaryStrategy 二进制文件创建策略
type BinaryStrategy struct{}

// NewBinaryStrategy 创建二进制策略实例
func NewBinaryStrategy() *BinaryStrategy {
	return &BinaryStrategy{}
}

// CanHandle 判断是否能处理指定类型的依赖
func (s *BinaryStrategy) CanHandle(depType string) bool {
	return depType == "binary"
}

// GetName 获取策略名称
func (s *BinaryStrategy) GetName() string {
	return "BinaryStrategy"
}

// Initialize 执行二进制文件创建
func (s *BinaryStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	if dep.Content == "" {
		result.ErrorMessage = "Base64内容不能为空"
		return result, fmt.Errorf("Base64内容不能为空")
	}

	// 转换为相对路径（移除开头的斜杠）
	filePath := dep.Path
	if strings.HasPrefix(filePath, "/") {
		filePath = "." + filePath
	}

	// 检查文件是否已存在
	if info, err := os.Stat(filePath); err == nil {
		if info.IsDir() {
			return result, fmt.Errorf("路径存在但是目录，不是文件: %s", filePath)
		} else {
			result.Success = true
			result.Details = fmt.Sprintf("文件已存在: %s, 大小: %d bytes", filePath, info.Size())
			return result, nil
		}
	}

	// 创建目录（如果需要）
	dir := filepath.Dir(filePath)
	if dir != "." && dir != "" {
		if err := os.MkdirAll(dir, 0755); err != nil {
			result.ErrorMessage = fmt.Sprintf("创建文件目录失败: %v", err)
			return result, fmt.Errorf("创建文件目录失败 %s: %w", dir, err)
		}
	}

	// 解码Base64内容
	binaryData, err := base64.StdEncoding.DecodeString(dep.Content)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("Base64解码失败: %v", err)
		return result, fmt.Errorf("Base64解码失败: %w", err)
	}

	// 创建二进制文件
	if err := os.WriteFile(filePath, binaryData, 0644); err != nil {
		result.ErrorMessage = fmt.Sprintf("创建二进制文件失败: %v", err)
		return result, fmt.Errorf("创建二进制文件失败 %s: %w", filePath, err)
	}

	// 验证文件是否创建成功
	if info, err := os.Stat(filePath); err != nil {
		result.ErrorMessage = "二进制文件创建后验证失败"
		return result, fmt.Errorf("二进制文件创建后验证失败: %w", err)
	} else if info.IsDir() {
		result.ErrorMessage = "创建的路径是目录，不是文件"
		return result, fmt.Errorf("创建的路径是目录，不是文件: %s", filePath)
	} else {
		result.Success = true
		originalSize := len(dep.Content)
		result.Details = fmt.Sprintf("成功创建二进制文件: %s, 解码后大小: %d bytes, Base64原始大小: %d bytes, 权限: 0644",
			filePath, info.Size(), originalSize)
	}

	return result, nil
}

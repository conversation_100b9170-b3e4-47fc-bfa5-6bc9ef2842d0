package environment

import (
	"context"
	"os"
	"testing"
)

func TestURLStrategyIntegration(t *testing.T) {
	// 测试URL策略是否正确注册到默认管理器中
	manager := DefaultManager()

	// 检查策略是否可用
	strategies := GetAvailableStrategies()
	urlStrategies, exists := strategies["URLStrategy"]
	if !exists {
		t.Error("URLStrategy未在可用策略中找到")
		return
	}

	expectedTypes := []string{"url"}
	if len(urlStrategies) != len(expectedTypes) {
		t.<PERSON><PERSON><PERSON>("URLStrategy支持的类型数量不符，期望 %d，实际 %d", len(expectedTypes), len(urlStrategies))
		return
	}

	for i, expectedType := range expectedTypes {
		if urlStrategies[i] != expectedType {
			t.Errorf("URLStrategy支持的类型不符，期望 %s，实际 %s", expectedType, urlStrategies[i])
		}
	}

	// 测试管理器能否找到URL策略
	dep := EnvironmentDependency{
		Type:    "url",
		Path:    "./test.txt",
		Content: "https://httpbin.org/status/404", // 这个URL会返回404，但足以测试策略找到
	}

	// 这里应该能找到策略，即使下载会失败
	results, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep})
	if err != nil {
		t.Errorf("管理器初始化失败: %v", err)
		return
	}

	if len(results) != 1 {
		t.Errorf("期望1个结果，实际得到 %d 个", len(results))
		return
	}

	result := results[0]
	if result.Type != "url" {
		t.Errorf("结果类型不符，期望 'url'，实际 '%s'", result.Type)
	}

	// 由于我们使用的是404 URL，下载应该失败，但策略应该被找到并执行
	if result.Success {
		t.Error("使用404 URL应该失败，但结果显示成功")
	}

	t.Logf("✅ URL策略集成测试通过")
	t.Logf("   - 策略已注册: %v", urlStrategies)
	t.Logf("   - 错误信息: %s", result.ErrorMessage)
}

func TestURLStrategyRealDownload(t *testing.T) {
	// 测试真实URL文件下载
	manager := DefaultManager()

	dep := EnvironmentDependency{
		Type:    "url",
		Path:    "./productivity_mock_data/task_1/第一季度财务报表.xlsx",
		Content: "https://office-mcp-dependency.bj.bcebos.com/productivity_mock_data/task_1/%E7%AC%AC%E4%B8%80%E5%AD%A3%E5%BA%A6%E8%B4%A2%E5%8A%A1%E6%8A%A5%E8%A1%A8.xlsx",
	}

	// 清理之前可能存在的文件
	defer os.RemoveAll("./productivity_mock_data")

	// 执行下载
	results, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep})
	if err != nil {
		t.Errorf("管理器初始化失败: %v", err)
		return
	}

	if len(results) != 1 {
		t.Errorf("期望1个结果，实际得到 %d 个", len(results))
		return
	}

	result := results[0]
	if result.Type != "url" {
		t.Errorf("结果类型不符，期望 'url'，实际 '%s'", result.Type)
	}

	// 验证下载是否成功
	if !result.Success {
		t.Errorf("文件下载失败: %s", result.ErrorMessage)
		return
	}

	// 验证文件是否存在
	if _, err := os.Stat(dep.Path); os.IsNotExist(err) {
		t.Errorf("下载的文件不存在: %s", dep.Path)
		return
	}

	// 验证文件信息
	fileInfo, err := os.Stat(dep.Path)
	if err != nil {
		t.Errorf("获取文件信息失败: %v", err)
		return
	}

	if fileInfo.Size() == 0 {
		t.Error("下载的文件大小为0")
		return
	}

	t.Logf("✅ 真实URL下载测试通过")
	t.Logf("   - 文件路径: %s", dep.Path)
	t.Logf("   - 文件大小: %d bytes", fileInfo.Size())
	t.Logf("   - 下载耗时: %v", result.Duration)
	t.Logf("   - 详情: %s", result.Details)
}

package environment

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/charmbracelet/log"
)

// EnvironmentDependency 表示环境依赖项
type EnvironmentDependency struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
	Name    string `json:"name"`
}

// InitializationResult 表示初始化结果
type InitializationResult struct {
	Success      bool          `json:"success"`
	Path         string        `json:"path"`
	Type         string        `json:"type"`
	Duration     time.Duration `json:"duration"`
	ErrorMessage string        `json:"error_message,omitempty"`
	Details      string        `json:"details,omitempty"`
}

// Strategy 环境初始化策略接口
type Strategy interface {
	// CanHandle 判断是否能处理指定类型的依赖
	CanHandle(depType string) bool

	// Initialize 执行初始化
	Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error)

	// GetName 获取策略名称
	GetName() string
}

// Manager 环境初始化管理器
type Manager struct {
	strategies []Strategy
	logger     *log.Logger
}

// NewManager 创建新的环境初始化管理器
func NewManager() *Manager {
	return &Manager{
		strategies: make([]Strategy, 0),
		logger:     log.Default(),
	}
}

// SetLogger 设置日志记录器
func (m *Manager) SetLogger(logger *log.Logger) {
	m.logger = logger
}

// RegisterStrategy 注册策略
func (m *Manager) RegisterStrategy(strategy Strategy) {
	m.strategies = append(m.strategies, strategy)
}

// Initialize 初始化所有环境依赖
func (m *Manager) Initialize(ctx context.Context, dependencies []EnvironmentDependency) ([]InitializationResult, error) {
	return m.InitializeInWorkDir(ctx, dependencies, "")
}

// InitializeInWorkDir 在指定工作目录下初始化所有环境依赖
func (m *Manager) InitializeInWorkDir(ctx context.Context, dependencies []EnvironmentDependency, workDir string) ([]InitializationResult, error) {
	m.logger.Info("开始初始化环境依赖", "count", len(dependencies), "workDir", workDir)

	// 如果指定了工作目录，则切换到该目录
	var originalDir string
	if workDir != "" {
		var err error
		originalDir, err = os.Getwd()
		if err != nil {
			return nil, fmt.Errorf("获取当前工作目录失败: %w", err)
		}
		if err := os.Chdir(workDir); err != nil {
			return nil, fmt.Errorf("切换到工作目录失败 %s: %w", workDir, err)
		}
		// 确保函数结束时恢复原始目录
		defer func() {
			if err := os.Chdir(originalDir); err != nil {
				m.logger.Error("恢复原始工作目录失败", "error", err, "originalDir", originalDir)
			}
		}()
	}

	results := make([]InitializationResult, 0, len(dependencies))

	for i, dep := range dependencies {
		m.logger.Info("处理环境依赖",
			"index", i+1,
			"type", dep.Type,
			"path", dep.Path,
			"workDir", workDir)

		strategy := m.findStrategy(dep.Type)
		if strategy == nil {
			continue
		}

		startTime := time.Now()
		result, err := strategy.Initialize(ctx, dep)
		duration := time.Since(startTime)

		if result == nil {
			result = &InitializationResult{
				Success:  false,
				Path:     dep.Path,
				Type:     dep.Type,
				Duration: duration,
			}
		}

		result.Duration = duration

		if err != nil {
			result.Success = false
			if result.ErrorMessage == "" {
				result.ErrorMessage = err.Error()
			}
			m.logger.Error("环境依赖初始化失败",
				"type", dep.Type,
				"path", dep.Path,
				"error", err,
				"duration_ms", duration.Milliseconds())
		} else {
			m.logger.Info("环境依赖初始化成功",
				"type", dep.Type,
				"path", dep.Path,
				"strategy", strategy.GetName(),
				"duration_ms", duration.Milliseconds())
		}

		results = append(results, *result)
	}

	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	m.logger.Info("环境依赖初始化完成",
		"total", len(dependencies),
		"success", successCount,
		"failed", len(dependencies)-successCount)

	return results, nil
}

// findStrategy 查找合适的策略
func (m *Manager) findStrategy(depType string) Strategy {
	for _, strategy := range m.strategies {
		if strategy.CanHandle(depType) {
			return strategy
		}
	}
	return nil
}

// GetRegisteredStrategies 获取已注册的策略列表
func (m *Manager) GetRegisteredStrategies() []string {
	names := make([]string, len(m.strategies))
	for i, strategy := range m.strategies {
		names[i] = strategy.GetName()
	}
	return names
}

# MCP压力测试配置 - 完整初始化（服务器+环境）

# API配置
# api_prefix: "http://localhost:8080/api/v1"
# api_prefix: "http://*************:8211/api/v1"
api_prefix: "http://*************:8001/api/v1"
timeout: 120

# MCP服务器初始化配置
server_conf:
  server_name: "sqlite_test"
  command: "uvx"
  args: ["--offline", "mcp-server-sqlite==2025.4.25", "--db-path", "./test.db"]
  description: "sqlite@2025.4.25，官方sqliteMCP服务，用于压力测试"
  type: "local"

# 环境初始化配置
env_conf:
  name: "sqlite-test-env"
  description: "SQLite数据库测试环境"
  environment_dependency:
    - path: "./test.db"
      type: "db"
      content: |
        -- 用户表
        CREATE TABLE users (
            user_id INTEGER PRIMARY KEY,
            username TEXT NOT NULL UNIQUE,
            password_hash TEXT NOT NULL,
            email TEXT UNIQUE,
            phone TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 商品表
        CREATE TABLE products (
            product_id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            price NUMERIC NOT NULL,
            stock_quantity INTEGER NOT NULL DEFAULT 0,
            category TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 订单表
        CREATE TABLE orders (
            order_id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            total_amount NUMERIC NOT NULL,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'shipped', 'delivered', 'cancelled')),
            FOREIGN KEY (user_id) REFERENCES users(user_id)
        );

        -- 初始化用户数据
        INSERT INTO users (username, password_hash, email, phone) VALUES
        ('john_doe', '$2a$10$xJwL5vGZzU3NwZbQ1MqXeO', '<EMAIL>', '13800138000'),
        ('jane_smith', '$2a$10$yH9Vp8vGzU3NwZbQ1MqXeO', '<EMAIL>', '13900139000'),
        ('bob_johnson', '$2a$10$zK8L6wGZzU3NwZbQ1MqXeO', '<EMAIL>', '13700137000');

        -- 初始化商品数据
        INSERT INTO products (name, description, price, stock_quantity, category) VALUES
        ('智能手机', '高端智能手机 128GB存储', 5999.00, 100, '电子产品'),
        ('无线耳机', '降噪无线蓝牙耳机', 899.00, 200, '电子产品'),
        ('智能手表', '运动健康监测手表', 1299.00, 150, '电子产品');

        -- 初始化订单数据
        INSERT INTO orders (user_id, total_amount, status) VALUES
        (1, 6898.00, 'paid'),
        (2, 2198.00, 'shipped'),
        (3, 5999.00, 'pending');

# 测试目标配置（将由初始化自动填充）
server_ids: []  # 将由服务器初始化后自动设置
env_id: 0       # 将由环境初始化后自动设置

# 会话创建配置
batch_size: 128
timeout_minutes: 10

session_creation:
  max_retries: 0
  retry_interval: 3s

# 工具调用配置
tool_call:
  tool_name: "sqlite_test__read_query"
  tool_input:
    query: "SELECT u.username, COUNT(o.order_id) as order_count FROM users u LEFT JOIN orders o ON u.user_id = o.user_id GROUP BY u.user_id, u.username;"
  timeout_seconds: 45
  max_retries: 3
  retry_interval: 2s
  
  # 轮询配置 - 测试多次查询
  polling_enabled: true
  polling_count: 30
  polling_interval: 1s

# 会话停止配置
session_stop:
  enabled: true                         # 启用会话停止
  message: "小负载压力测试完成，自动停止会话"  # 停止消息
  max_retries: 3                        # 最大重试次数
  retry_interval: 2s                    # 重试间隔
# 报告配置
report:
  output_dir: "benchmark/logs/full_init"
  detailed_logs: true
  include_errors: true

# 并发控制
concurrency:
  max_goroutines: 1024
  rate_limit: 1ms

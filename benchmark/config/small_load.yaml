# MCP压力测试配置 - 小负载测试

# API配置
api_prefix: "http://10.11.154.204:8211/api/v1"
timeout: 120



# 测试目标配置
server_ids: []  # 单个MCP服务器
env_id: 0

# 会话创建配置 - 小负载
batch_size: 512         # 并行创建5个会话
timeout_minutes: 10    # 会话超时时间

session_creation:
  max_retries: 2      # 减少重试次数
  retry_interval: 1s  # 减少重试间隔

# 会话停止配置
session_stop:
  enabled: true                         # 启用会话停止
  message: "小负载压力测试完成，自动停止会话"  # 停止消息
  max_retries: 3                        # 最大重试次数
  retry_interval: 2s                    # 重试间隔

# 工具调用配置
tool_call:
  tool_name: "sqlite__read_query"
  tool_input:
    # path: "/tmp/small_test.txt"
    query: "select * from users;"
  timeout_seconds: 15   # 减少超时时间
  max_retries: 2        # 减少重试次数
  retry_interval: 1s
    
  # 轮询配置
  polling_enabled: true
  polling_count: 30
  polling_interval: 2s

# 报告配置
report:
  output_dir: "benchmark/logs/small_load"
  detailed_logs: true
  include_errors: true

# 并发控制 - 保守设置
concurrency:
  max_goroutines: 256    # 限制并发数
  rate_limit: 20ms     # 增加速率限制

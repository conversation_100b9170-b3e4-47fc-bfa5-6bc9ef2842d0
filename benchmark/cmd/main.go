package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/initialization"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/report"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/toolcall"
)

// 命令行参数
var (
	configFile = flag.String("config", "", "配置文件路径")
	apiPrefix  = flag.String("api-prefix", "", "API端点前缀")
	serverIDs  = flag.String("server-ids", "", "MCP服务器ID列表，逗号分隔")
	envID      = flag.Int64("env-id", 0, "环境ID")
	batchSize  = flag.Int("batch-size", 0, "并行创建的会话数量")
	timeout    = flag.Int("timeout", 0, "请求超时时间（秒）")
	toolName   = flag.String("tool-name", "", "工具名称")
	toolInput  = flag.String("tool-input", "", "工具输入参数（JSON格式）")
	outputDir  = flag.String("output-dir", "", "输出目录")
	help       = flag.Bool("help", false, "显示帮助信息")
	verbose    = flag.Bool("verbose", false, "详细输出")
)

func main() {
	flag.Parse()

	if *help {
		printUsage()
		return
	}

	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	if *verbose {
		log.Printf("使用配置: API=%s, BatchSize=%d, ServerIDs=%v",
			cfg.APIPrefix, cfg.BatchSize, cfg.ServerIDs)
	}

	// 运行压力测试
	if err := runStressTest(cfg); err != nil {
		log.Fatalf("压力测试失败: %v", err)
	}
}

// loadConfig 加载配置
func loadConfig() (*config.Config, error) {
	var cfg *config.Config
	var err error

	// 从文件加载配置
	if *configFile != "" {
		cfg, err = config.LoadFromFile(*configFile)
		if err != nil {
			return nil, fmt.Errorf("从文件加载配置失败: %w", err)
		}
	} else {
		cfg = config.DefaultConfig()
	}

	// 命令行参数覆盖配置
	if *apiPrefix != "" {
		cfg.APIPrefix = *apiPrefix
	}

	if *serverIDs != "" {
		ids, err := parseServerIDs(*serverIDs)
		if err != nil {
			return nil, fmt.Errorf("解析服务器ID失败: %w", err)
		}
		cfg.ServerIDs = ids
	}

	if *envID != 0 {
		cfg.EnvID = *envID
	}

	if *batchSize != 0 {
		cfg.BatchSize = *batchSize
	}

	if *timeout != 0 {
		cfg.Timeout = *timeout
	}

	if *toolName != "" {
		cfg.ToolCall.ToolName = *toolName
	}

	if *toolInput != "" {
		// 这里简化处理，实际应该解析JSON
		cfg.ToolCall.ToolInput = map[string]interface{}{
			"raw_input": *toolInput,
		}
	}

	if *outputDir != "" {
		cfg.Report.OutputDir = *outputDir
	}

	return cfg, nil
}

// parseServerIDs 解析服务器ID列表
func parseServerIDs(s string) ([]int64, error) {
	parts := strings.Split(s, ",")
	var ids []int64

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		id, err := strconv.ParseInt(part, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("无效的服务器ID: %s", part)
		}

		ids = append(ids, id)
	}

	return ids, nil
}

// initializePrerequisites 初始化前置条件（服务器和环境）
func initializePrerequisites(ctx context.Context, cfg *config.Config, httpClient *session.DefaultHTTPClient) error {
	log.Println("开始初始化前置条件...")

	// 初始化MCP服务器
	if cfg.ServerConf != nil {
		log.Printf("初始化MCP服务器: %s", cfg.ServerConf.ServerName)
		serverInitializer := initialization.NewServerInitializer(cfg, httpClient)

		serverID, err := serverInitializer.InitializeServerIfNeeded(ctx)
		if err != nil {
			return fmt.Errorf("服务器初始化失败: %w", err)
		}

		if serverID > 0 {
			// 更新配置中的服务器ID列表
			cfg.ServerIDs = []int64{serverID}
			log.Printf("服务器初始化成功，更新配置: server_id=%d", serverID)
		}
	}

	// 初始化环境
	if cfg.EnvConf != nil {
		log.Printf("初始化环境: %s", cfg.EnvConf.Name)
		envInitializer := initialization.NewEnvInitializer(cfg, httpClient)

		envID, err := envInitializer.InitializeEnvironmentIfNeeded(ctx)
		if err != nil {
			return fmt.Errorf("环境初始化失败: %w", err)
		}

		if envID > 0 {
			// 更新配置中的环境ID
			cfg.EnvID = envID
			log.Printf("环境初始化成功，更新配置: env_id=%d", envID)
		}
	}

	// 验证初始化结果
	if cfg.ServerConf != nil && len(cfg.ServerIDs) == 0 {
		return fmt.Errorf("配置了server_conf但初始化完成后server_ids仍为空")
	}
	if cfg.EnvConf != nil && cfg.EnvID <= 0 {
		return fmt.Errorf("配置了env_conf但初始化完成后env_id仍无效")
	}

	log.Printf("前置条件初始化完成: server_ids=%v, env_id=%d", cfg.ServerIDs, cfg.EnvID)
	return nil
}

// runStressTest 运行压力测试
func runStressTest(cfg *config.Config) error {
	ctx := context.Background()
	startTime := time.Now()

	log.Println("开始MCP压力测试...")

	// 创建HTTP客户端
	httpClient := session.NewDefaultHTTPClient(cfg.GetHTTPTimeout())

	// 第零阶段：初始化服务器和环境（如果需要）
	if err := initializePrerequisites(ctx, cfg, httpClient); err != nil {
		return fmt.Errorf("初始化前置条件失败: %w", err)
	}

	// 第一阶段：并行创建会话
	log.Println("第一阶段：并行创建会话")
	sessionCreator := session.NewSessionCreator(cfg, httpClient)
	sessionResults, err := sessionCreator.CreateSessionsBatch(ctx)
	if err != nil {
		return fmt.Errorf("会话创建失败: %w", err)
	}

	// 获取成功的会话
	successfulSessions := sessionCreator.GetSuccessfulSessions(sessionResults)
	log.Printf("会话创建完成: 总数=%d, 成功=%d", len(sessionResults), len(successfulSessions))

	if len(successfulSessions) == 0 {
		return fmt.Errorf("没有成功创建的会话，无法继续工具调用测试")
	}

	// 第二阶段：并发调用工具
	log.Println("第二阶段：并发调用工具")
	toolCaller := toolcall.NewToolCaller(cfg, httpClient)
	toolCallResults, err := toolCaller.CallToolsOnSessions(ctx, successfulSessions)
	if err != nil {
		return fmt.Errorf("工具调用失败: %w", err)
	}

	// 第三阶段：停止会话（如果启用）
	var sessionStopResults []*session.SessionStopResult
	if cfg.SessionStop.Enabled {
		log.Println("第三阶段：停止会话")
		sessionStopper := session.NewSessionStopper(cfg, httpClient)
		sessionStopResults, err = sessionStopper.StopSessions(ctx, successfulSessions)
		if err != nil {
			log.Printf("会话停止失败: %v", err)
			// 不中断流程，继续生成报告
		}
	}

	totalDuration := time.Since(startTime)
	log.Printf("压力测试完成，总耗时: %s", totalDuration)

	// 生成报告
	reporter := report.NewReporter(cfg)
	testReport, err := reporter.GenerateReport(
		sessionCreator.GetMetrics(),
		toolCaller.GetMetrics(),
		sessionResults,
		toolCallResults,
		totalDuration,
	)
	if err != nil {
		return fmt.Errorf("生成报告失败: %w", err)
	}

	// 如果启用了会话停止，在日志中输出会话停止结果
	if cfg.SessionStop.Enabled && len(sessionStopResults) > 0 {
		successCount := 0
		failureCount := 0
		for _, result := range sessionStopResults {
			if result.Success {
				successCount++
			} else {
				failureCount++
			}
		}
		log.Printf("会话停止结果: 总数=%d, 成功=%d, 失败=%d",
			len(sessionStopResults), successCount, failureCount)
	}

	// 保存报告
	reportFile, err := reporter.SaveReport(testReport)
	if err != nil {
		return fmt.Errorf("保存报告失败: %w", err)
	}

	// 打印摘要
	reporter.PrintSummary(testReport)
	fmt.Printf("\n详细报告已保存到: %s\n", reportFile)

	return nil
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("MCP 压力测试工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run benchmark/cmd/main.go [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -config string")
	fmt.Println("        配置文件路径")
	fmt.Println("  -api-prefix string")
	fmt.Println("        API端点前缀 (默认: http://localhost:8080/api/v1)")
	fmt.Println("  -server-ids string")
	fmt.Println("        MCP服务器ID列表，逗号分隔 (例如: 1,2,3)")
	fmt.Println("  -env-id int")
	fmt.Println("        环境ID")
	fmt.Println("  -batch-size int")
	fmt.Println("        并行创建的会话数量 (默认: 10)")
	fmt.Println("  -timeout int")
	fmt.Println("        请求超时时间（秒） (默认: 30)")
	fmt.Println("  -tool-name string")
	fmt.Println("        工具名称 (默认: filesystem__read_file)")
	fmt.Println("  -tool-input string")
	fmt.Println("        工具输入参数（JSON格式）")
	fmt.Println("  -output-dir string")
	fmt.Println("        输出目录 (默认: logs)")
	fmt.Println("  -verbose")
	fmt.Println("        详细输出")
	fmt.Println("  -help")
	fmt.Println("        显示此帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 使用默认配置")
	fmt.Println("  go run benchmark/cmd/main.go")
	fmt.Println()
	fmt.Println("  # 使用自定义配置文件")
	fmt.Println("  go run benchmark/cmd/main.go -config benchmark/config/example.yaml")
	fmt.Println()
	fmt.Println("  # 覆盖特定参数")
	fmt.Println("  go run benchmark/cmd/main.go -api-prefix http://localhost:8080/api/v1 -batch-size 50")
	fmt.Println()
	fmt.Println("  # 测试特定服务器")
	fmt.Println("  go run benchmark/cmd/main.go -server-ids 1,2,3 -env-id 1")
}

package metrics

import (
	"sync"
	"time"
)

// ToolCallMetrics 工具调用性能指标
type ToolCallMetrics struct {
	mu sync.RWMutex

	// 基本统计
	totalAttempts int
	successCount  int
	failureCount  int

	// 响应时间统计
	responseTimes []time.Duration
	totalTime     time.Duration
	minTime       time.Duration
	maxTime       time.Duration

	// 错误统计
	errors map[string]int

	// 状态统计
	statusCounts map[string]int

	// 时间戳
	startTime time.Time
	endTime   time.Time
}

// NewToolCallMetrics 创建工具调用指标收集器
func NewToolCallMetrics() *ToolCallMetrics {
	return &ToolCallMetrics{
		errors:       make(map[string]int),
		statusCounts: make(map[string]int),
		startTime:    time.Now(),
		minTime:      time.Duration(0),
		maxTime:      time.Duration(0),
	}
}

// RecordToolCall 记录工具调用结果
func (tcm *ToolCallMetrics) RecordToolCall(success bool, responseTime time.Duration, err error) {
	tcm.mu.Lock()
	defer tcm.mu.Unlock()

	tcm.totalAttempts++
	tcm.responseTimes = append(tcm.responseTimes, responseTime)
	tcm.totalTime += responseTime

	// 更新最小/最大时间
	if tcm.minTime == 0 || responseTime < tcm.minTime {
		tcm.minTime = responseTime
	}
	if responseTime > tcm.maxTime {
		tcm.maxTime = responseTime
	}

	if success {
		tcm.successCount++
	} else {
		tcm.failureCount++
		if err != nil {
			errorKey := err.Error()
			tcm.errors[errorKey]++
		}
	}

	tcm.endTime = time.Now()
}

// RecordToolCallStatus 记录工具调用状态
func (tcm *ToolCallMetrics) RecordToolCallStatus(status string) {
	tcm.mu.Lock()
	defer tcm.mu.Unlock()

	tcm.statusCounts[status]++
}

// GetSuccessCount 获取成功次数
func (tcm *ToolCallMetrics) GetSuccessCount() int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()
	return tcm.successCount
}

// GetFailureCount 获取失败次数
func (tcm *ToolCallMetrics) GetFailureCount() int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()
	return tcm.failureCount
}

// GetTotalAttempts 获取总尝试次数
func (tcm *ToolCallMetrics) GetTotalAttempts() int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()
	return tcm.totalAttempts
}

// GetSuccessRate 获取成功率
func (tcm *ToolCallMetrics) GetSuccessRate() float64 {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	if tcm.totalAttempts == 0 {
		return 0
	}
	return float64(tcm.successCount) / float64(tcm.totalAttempts) * 100
}

// GetAverageResponseTime 获取平均响应时间
func (tcm *ToolCallMetrics) GetAverageResponseTime() time.Duration {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	if tcm.totalAttempts == 0 {
		return 0
	}
	return tcm.totalTime / time.Duration(tcm.totalAttempts)
}

// GetMinResponseTime 获取最小响应时间
func (tcm *ToolCallMetrics) GetMinResponseTime() time.Duration {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()
	return tcm.minTime
}

// GetMaxResponseTime 获取最大响应时间
func (tcm *ToolCallMetrics) GetMaxResponseTime() time.Duration {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()
	return tcm.maxTime
}

// GetPercentile 获取响应时间百分位数
func (tcm *ToolCallMetrics) GetPercentile(percentile float64) time.Duration {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	if len(tcm.responseTimes) == 0 {
		return 0
	}

	// 复制并排序响应时间
	times := make([]time.Duration, len(tcm.responseTimes))
	copy(times, tcm.responseTimes)

	// 简单排序
	for i := 0; i < len(times); i++ {
		for j := i + 1; j < len(times); j++ {
			if times[i] > times[j] {
				times[i], times[j] = times[j], times[i]
			}
		}
	}

	index := int(float64(len(times)) * percentile / 100)
	if index >= len(times) {
		index = len(times) - 1
	}

	return times[index]
}

// GetErrors 获取错误统计
func (tcm *ToolCallMetrics) GetErrors() map[string]int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	errors := make(map[string]int)
	for k, v := range tcm.errors {
		errors[k] = v
	}
	return errors
}

// GetStatusCounts 获取状态统计
func (tcm *ToolCallMetrics) GetStatusCounts() map[string]int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	counts := make(map[string]int)
	for k, v := range tcm.statusCounts {
		counts[k] = v
	}
	return counts
}

// GetDuration 获取总耗时
func (tcm *ToolCallMetrics) GetDuration() time.Duration {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	if tcm.endTime.IsZero() {
		return time.Since(tcm.startTime)
	}
	return tcm.endTime.Sub(tcm.startTime)
}

// GetThroughput 获取吞吐量（每秒成功次数）
func (tcm *ToolCallMetrics) GetThroughput() float64 {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	duration := tcm.GetDuration()
	if duration == 0 {
		return 0
	}

	return float64(tcm.successCount) / duration.Seconds()
}

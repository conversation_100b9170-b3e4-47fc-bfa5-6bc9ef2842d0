package metrics

import (
	"sync"
	"time"
)

// SessionMetrics 会话创建性能指标
type SessionMetrics struct {
	mu sync.RWMutex

	// 基本统计
	totalAttempts int
	successCount  int
	failureCount  int

	// 响应时间统计
	responseTimes []time.Duration
	totalTime     time.Duration
	minTime       time.Duration
	maxTime       time.Duration

	// 错误统计
	errors map[string]int

	// 时间戳
	startTime time.Time
	endTime   time.Time
}

// NewSessionMetrics 创建会话指标收集器
func NewSessionMetrics() *SessionMetrics {
	return &SessionMetrics{
		errors:    make(map[string]int),
		startTime: time.Now(),
		minTime:   time.Duration(0),
		maxTime:   time.Duration(0),
	}
}

// RecordSessionCreation 记录会话创建结果
func (sm *SessionMetrics) RecordSessionCreation(success bool, responseTime time.Duration, err error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.totalAttempts++
	sm.responseTimes = append(sm.responseTimes, responseTime)
	sm.totalTime += responseTime

	// 更新最小/最大时间
	if sm.minTime == 0 || responseTime < sm.minTime {
		sm.minTime = responseTime
	}
	if responseTime > sm.maxTime {
		sm.maxTime = responseTime
	}

	if success {
		sm.successCount++
	} else {
		sm.failureCount++
		if err != nil {
			errorKey := err.Error()
			sm.errors[errorKey]++
		}
	}

	sm.endTime = time.Now()
}

// GetSuccessCount 获取成功次数
func (sm *SessionMetrics) GetSuccessCount() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.successCount
}

// GetFailureCount 获取失败次数
func (sm *SessionMetrics) GetFailureCount() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.failureCount
}

// GetTotalAttempts 获取总尝试次数
func (sm *SessionMetrics) GetTotalAttempts() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.totalAttempts
}

// GetSuccessRate 获取成功率
func (sm *SessionMetrics) GetSuccessRate() float64 {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if sm.totalAttempts == 0 {
		return 0
	}
	return float64(sm.successCount) / float64(sm.totalAttempts) * 100
}

// GetAverageResponseTime 获取平均响应时间
func (sm *SessionMetrics) GetAverageResponseTime() time.Duration {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if sm.totalAttempts == 0 {
		return 0
	}
	return sm.totalTime / time.Duration(sm.totalAttempts)
}

// GetMinResponseTime 获取最小响应时间
func (sm *SessionMetrics) GetMinResponseTime() time.Duration {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.minTime
}

// GetMaxResponseTime 获取最大响应时间
func (sm *SessionMetrics) GetMaxResponseTime() time.Duration {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.maxTime
}

// GetPercentile 获取响应时间百分位数
func (sm *SessionMetrics) GetPercentile(percentile float64) time.Duration {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if len(sm.responseTimes) == 0 {
		return 0
	}

	// 复制并排序响应时间
	times := make([]time.Duration, len(sm.responseTimes))
	copy(times, sm.responseTimes)

	// 简单排序
	for i := 0; i < len(times); i++ {
		for j := i + 1; j < len(times); j++ {
			if times[i] > times[j] {
				times[i], times[j] = times[j], times[i]
			}
		}
	}

	index := int(float64(len(times)) * percentile / 100)
	if index >= len(times) {
		index = len(times) - 1
	}

	return times[index]
}

// GetErrors 获取错误统计
func (sm *SessionMetrics) GetErrors() map[string]int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	errors := make(map[string]int)
	for k, v := range sm.errors {
		errors[k] = v
	}
	return errors
}

// GetDuration 获取总耗时
func (sm *SessionMetrics) GetDuration() time.Duration {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if sm.endTime.IsZero() {
		return time.Since(sm.startTime)
	}
	return sm.endTime.Sub(sm.startTime)
}

// GetThroughput 获取吞吐量（每秒成功次数）
func (sm *SessionMetrics) GetThroughput() float64 {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	duration := sm.GetDuration()
	if duration == 0 {
		return 0
	}

	return float64(sm.successCount) / duration.Seconds()
}

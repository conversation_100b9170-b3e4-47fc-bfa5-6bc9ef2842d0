package report

import (
	"fmt"
	"math"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/bytedance/sonic"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/metrics"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/toolcall"
)

// Reporter 报告生成器
type Reporter struct {
	config *config.Config
}

// TestReport 测试报告
type TestReport struct {
	// 测试基本信息
	TestInfo TestInfo `json:"test_info"`

	// 会话创建结果
	SessionCreation SessionCreationReport `json:"session_creation"`

	// 工具调用结果
	ToolCalling ToolCallingReport `json:"tool_calling"`

	// 总体统计
	Summary SummaryReport `json:"summary"`
}

// TestInfo 测试基本信息
type TestInfo struct {
	Timestamp time.Time              `json:"timestamp"`
	Config    map[string]interface{} `json:"config"`   // 转换后的配置，时间字段为秒
	Duration  float64                `json:"duration"` // 秒，保留2位小数
	TotalTime string                 `json:"total_time"`
}

// SessionCreationReport 会话创建报告
type SessionCreationReport struct {
	TotalAttempts       int            `json:"total_attempts"`
	SuccessCount        int            `json:"success_count"`
	FailureCount        int            `json:"failure_count"`
	SuccessRate         float64        `json:"success_rate"`
	AverageResponseTime float64        `json:"average_response_time"` // 秒，保留2位小数
	MinResponseTime     float64        `json:"min_response_time"`     // 秒，保留2位小数
	MaxResponseTime     float64        `json:"max_response_time"`     // 秒，保留2位小数
	P50ResponseTime     float64        `json:"p50_response_time"`     // 秒，保留2位小数
	P95ResponseTime     float64        `json:"p95_response_time"`     // 秒，保留2位小数
	P99ResponseTime     float64        `json:"p99_response_time"`     // 秒，保留2位小数
	Throughput          float64        `json:"throughput"`
	Duration            float64        `json:"duration"` // 秒，保留2位小数
	Errors              map[string]int `json:"errors,omitempty"`
}

// ToolCallingReport 工具调用报告
type ToolCallingReport struct {
	TotalAttempts       int            `json:"total_attempts"`
	SuccessCount        int            `json:"success_count"`
	FailureCount        int            `json:"failure_count"`
	SuccessRate         float64        `json:"success_rate"`
	AverageResponseTime float64        `json:"average_response_time"` // 秒，保留2位小数
	MinResponseTime     float64        `json:"min_response_time"`     // 秒，保留2位小数
	MaxResponseTime     float64        `json:"max_response_time"`     // 秒，保留2位小数
	P50ResponseTime     float64        `json:"p50_response_time"`     // 秒，保留2位小数
	P95ResponseTime     float64        `json:"p95_response_time"`     // 秒，保留2位小数
	P99ResponseTime     float64        `json:"p99_response_time"`     // 秒，保留2位小数
	Throughput          float64        `json:"throughput"`
	Duration            float64        `json:"duration"` // 秒，保留2位小数
	StatusCounts        map[string]int `json:"status_counts,omitempty"`
	Errors              map[string]int `json:"errors,omitempty"`
}

// SummaryReport 总体报告
type SummaryReport struct {
	TotalSessions       int     `json:"total_sessions"`
	SuccessfulSessions  int     `json:"successful_sessions"`
	TotalToolCalls      int     `json:"total_tool_calls"`
	SuccessfulToolCalls int     `json:"successful_tool_calls"`
	OverallSuccessRate  float64 `json:"overall_success_rate"`
	TotalDuration       float64 `json:"total_duration"` // 秒，保留2位小数
	AverageThroughput   float64 `json:"average_throughput"`
}

// durationToSeconds 将 time.Duration 转换为秒，保留2位小数
func durationToSeconds(d time.Duration) float64 {
	seconds := d.Seconds()
	return math.Round(seconds*100) / 100
}

// convertConfigForReport 转换配置中的时间字段为秒
func convertConfigForReport(cfg *config.Config) map[string]interface{} {
	// 创建配置的副本，将时间字段转换为秒
	result := make(map[string]interface{})

	// 基本字段
	result["api_prefix"] = cfg.APIPrefix
	result["timeout"] = cfg.Timeout
	result["server_conf"] = cfg.ServerConf
	result["env_conf"] = cfg.EnvConf
	result["server_ids"] = cfg.ServerIDs
	result["env_id"] = cfg.EnvID
	result["batch_size"] = cfg.BatchSize
	result["timeout_minutes"] = cfg.TimeoutMinutes

	// 会话创建配置
	result["session_creation"] = map[string]interface{}{
		"max_retries":    cfg.SessionCreation.MaxRetries,
		"retry_interval": durationToSeconds(cfg.SessionCreation.RetryInterval),
	}

	// 会话停止配置
	result["session_stop"] = map[string]interface{}{
		"enabled":        cfg.SessionStop.Enabled,
		"message":        cfg.SessionStop.Message,
		"max_retries":    cfg.SessionStop.MaxRetries,
		"retry_interval": durationToSeconds(cfg.SessionStop.RetryInterval),
	}

	// 工具调用配置
	result["tool_call"] = map[string]interface{}{
		"tool_name":        cfg.ToolCall.ToolName,
		"tool_input":       cfg.ToolCall.ToolInput,
		"timeout_seconds":  cfg.ToolCall.TimeoutSecs,
		"max_retries":      cfg.ToolCall.MaxRetries,
		"retry_interval":   durationToSeconds(cfg.ToolCall.RetryInterval),
		"polling_enabled":  cfg.ToolCall.PollingEnabled,
		"polling_count":    cfg.ToolCall.PollingCount,
		"polling_interval": durationToSeconds(cfg.ToolCall.PollingInterval),
	}

	// 报告配置
	result["report"] = cfg.Report

	// 并发控制配置
	result["concurrency"] = map[string]interface{}{
		"max_goroutines": cfg.Concurrency.MaxGoroutines,
		"rate_limit":     durationToSeconds(cfg.Concurrency.RateLimit),
	}

	return result
}

// NewReporter 创建报告生成器
func NewReporter(cfg *config.Config) *Reporter {
	return &Reporter{
		config: cfg,
	}
}

// GenerateReport 生成测试报告
func (r *Reporter) GenerateReport(
	sessionMetrics *metrics.SessionMetrics,
	toolCallMetrics *metrics.ToolCallMetrics,
	sessionResults []*session.SessionResult,
	toolCallResults []*toolcall.ToolCallResult,
	totalDuration time.Duration,
) (*TestReport, error) {

	report := &TestReport{
		TestInfo: TestInfo{
			Timestamp: time.Now(),
			Config:    convertConfigForReport(r.config),
			Duration:  durationToSeconds(totalDuration),
			TotalTime: fmt.Sprintf("%.2fs", durationToSeconds(totalDuration)),
		},
		SessionCreation: SessionCreationReport{
			TotalAttempts:       sessionMetrics.GetTotalAttempts(),
			SuccessCount:        sessionMetrics.GetSuccessCount(),
			FailureCount:        sessionMetrics.GetFailureCount(),
			SuccessRate:         sessionMetrics.GetSuccessRate(),
			AverageResponseTime: durationToSeconds(sessionMetrics.GetAverageResponseTime()),
			MinResponseTime:     durationToSeconds(sessionMetrics.GetMinResponseTime()),
			MaxResponseTime:     durationToSeconds(sessionMetrics.GetMaxResponseTime()),
			P50ResponseTime:     durationToSeconds(sessionMetrics.GetPercentile(50)),
			P95ResponseTime:     durationToSeconds(sessionMetrics.GetPercentile(95)),
			P99ResponseTime:     durationToSeconds(sessionMetrics.GetPercentile(99)),
			Throughput:          sessionMetrics.GetThroughput(),
			Duration:            durationToSeconds(sessionMetrics.GetDuration()),
		},
		ToolCalling: ToolCallingReport{
			TotalAttempts:       toolCallMetrics.GetTotalAttempts(),
			SuccessCount:        toolCallMetrics.GetSuccessCount(),
			FailureCount:        toolCallMetrics.GetFailureCount(),
			SuccessRate:         toolCallMetrics.GetSuccessRate(),
			AverageResponseTime: durationToSeconds(toolCallMetrics.GetAverageResponseTime()),
			MinResponseTime:     durationToSeconds(toolCallMetrics.GetMinResponseTime()),
			MaxResponseTime:     durationToSeconds(toolCallMetrics.GetMaxResponseTime()),
			P50ResponseTime:     durationToSeconds(toolCallMetrics.GetPercentile(50)),
			P95ResponseTime:     durationToSeconds(toolCallMetrics.GetPercentile(95)),
			P99ResponseTime:     durationToSeconds(toolCallMetrics.GetPercentile(99)),
			Throughput:          toolCallMetrics.GetThroughput(),
			Duration:            durationToSeconds(toolCallMetrics.GetDuration()),
			StatusCounts:        toolCallMetrics.GetStatusCounts(),
		},
	}

	// 添加错误信息（如果配置允许）
	if r.config.Report.IncludeErrors {
		report.SessionCreation.Errors = sessionMetrics.GetErrors()
		report.ToolCalling.Errors = toolCallMetrics.GetErrors()
	}

	// 计算总体统计
	report.Summary = SummaryReport{
		TotalSessions:       len(sessionResults),
		SuccessfulSessions:  sessionMetrics.GetSuccessCount(),
		TotalToolCalls:      len(toolCallResults),
		SuccessfulToolCalls: toolCallMetrics.GetSuccessCount(),
		TotalDuration:       durationToSeconds(totalDuration),
	}

	// 计算总体成功率
	if len(sessionResults) > 0 && len(toolCallResults) > 0 {
		report.Summary.OverallSuccessRate = float64(sessionMetrics.GetSuccessCount()+toolCallMetrics.GetSuccessCount()) /
			float64(len(sessionResults)+len(toolCallResults)) * 100
	}

	// 计算平均吞吐量
	if totalDuration > 0 {
		report.Summary.AverageThroughput = float64(sessionMetrics.GetSuccessCount()+toolCallMetrics.GetSuccessCount()) /
			totalDuration.Seconds()
	}

	return report, nil
}

// SaveReport 保存报告到文件
func (r *Reporter) SaveReport(report *TestReport) (string, error) {
	// 确保输出目录存在
	if err := os.MkdirAll(r.config.Report.OutputDir, 0755); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 生成文件名
	timestamp := report.TestInfo.Timestamp.Format("20060102_150405")
	filename := fmt.Sprintf("stress_test_report_%s.json", timestamp)
	filepath := filepath.Join(r.config.Report.OutputDir, filename)

	// 序列化报告
	data, err := sonic.MarshalIndent(report, "", "  ")
	if err != nil {
		return "", fmt.Errorf("序列化报告失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(filepath, data, 0644); err != nil {
		return "", fmt.Errorf("写入报告文件失败: %w", err)
	}

	return filepath, nil
}

// PrintSummary 打印报告摘要
func (r *Reporter) PrintSummary(report *TestReport) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("MCP 压力测试报告摘要")
	fmt.Println(strings.Repeat("=", 80))

	fmt.Printf("测试时间: %s\n", report.TestInfo.Timestamp.Format("2006-01-02 15:04:05"))
	fmt.Printf("总耗时: %s\n", report.TestInfo.TotalTime)
	fmt.Printf("API地址: %s\n", report.TestInfo.Config["api_prefix"])
	fmt.Printf("批次大小: %v\n", report.TestInfo.Config["batch_size"])

	fmt.Println("\n会话创建结果:")
	fmt.Printf("  总尝试次数: %d\n", report.SessionCreation.TotalAttempts)
	fmt.Printf("  成功次数: %d\n", report.SessionCreation.SuccessCount)
	fmt.Printf("  失败次数: %d\n", report.SessionCreation.FailureCount)
	fmt.Printf("  成功率: %.2f%%\n", report.SessionCreation.SuccessRate)
	fmt.Printf("  平均响应时间: %s\n", report.SessionCreation.AverageResponseTime)
	fmt.Printf("  吞吐量: %.2f 会话/秒\n", report.SessionCreation.Throughput)

	fmt.Println("\n工具调用结果:")
	fmt.Printf("  总尝试次数: %d\n", report.ToolCalling.TotalAttempts)
	fmt.Printf("  成功次数: %d\n", report.ToolCalling.SuccessCount)
	fmt.Printf("  失败次数: %d\n", report.ToolCalling.FailureCount)
	fmt.Printf("  成功率: %.2f%%\n", report.ToolCalling.SuccessRate)
	fmt.Printf("  平均响应时间: %s\n", report.ToolCalling.AverageResponseTime)
	fmt.Printf("  吞吐量: %.2f 调用/秒\n", report.ToolCalling.Throughput)

	fmt.Println("\n总体统计:")
	fmt.Printf("  总体成功率: %.2f%%\n", report.Summary.OverallSuccessRate)
	fmt.Printf("  平均吞吐量: %.2f 操作/秒\n", report.Summary.AverageThroughput)

	fmt.Println(strings.Repeat("=", 80))
}

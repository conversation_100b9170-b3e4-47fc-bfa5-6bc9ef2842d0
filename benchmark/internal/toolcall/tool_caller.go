package toolcall

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/metrics"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/session"
)

// ToolCaller 工具调用器
type ToolCaller struct {
	config     *config.Config
	httpClient session.HTTPClient
	metrics    *metrics.ToolCallMetrics
}

// ToolCallRequest 工具调用请求
type ToolCallRequest struct {
	SessionID   int64  `json:"session_id"`
	Name        string `json:"name"`
	Arguments   string `json:"arguments"`
	CallID      string `json:"call_id"`
	TimeoutSecs int    `json:"timeout_seconds,omitempty"`
}

// ToolCallResponse 工具调用响应
type ToolCallResponse struct {
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    ToolCallResponseData `json:"data"`
}

// ToolCallResponseData 工具调用响应数据
type ToolCallResponseData struct {
	CallID       string `json:"call_id"`
	Status       string `json:"status"`
	Result       string `json:"result,omitempty"`
	OldEnvMD5    string `json:"old_env_md5,omitempty"`
	NewEnvMD5    string `json:"new_env_md5,omitempty"`
	OldEnvURL    string `json:"old_env_url,omitempty"`
	NewEnvURL    string `json:"new_env_url,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// ToolCallResult 工具调用结果
type ToolCallResult struct {
	SessionID    int64
	CallID       string
	Success      bool
	Error        error
	ResponseTime time.Duration
	Status       string
	Result       string
	ErrorMessage string
}

// NewToolCaller 创建工具调用器
func NewToolCaller(cfg *config.Config, httpClient session.HTTPClient) *ToolCaller {
	return &ToolCaller{
		config:     cfg,
		httpClient: httpClient,
		metrics:    metrics.NewToolCallMetrics(),
	}
}

// CallToolsOnSessions 在所有会话上并发调用工具
func (tc *ToolCaller) CallToolsOnSessions(ctx context.Context, sessions []*session.SessionResult) ([]*ToolCallResult, error) {
	log.Printf("开始在%d个会话上并发调用工具: tool_name=%s", len(sessions), tc.config.ToolCall.ToolName)

	// 过滤成功的会话
	var successfulSessions []*session.SessionResult
	for _, sess := range sessions {
		if sess.Success {
			successfulSessions = append(successfulSessions, sess)
		} else {
			log.Printf("跳过失败的会话: session_id=%d", sess.SessionID)
		}
	}

	if len(successfulSessions) == 0 {
		log.Printf("没有成功的会话可用于工具调用")
		return []*ToolCallResult{}, nil
	}

	log.Printf("将在%d个成功会话上并发调用工具", len(successfulSessions))

	// 创建结果通道
	resultChan := make(chan []*ToolCallResult, len(successfulSessions))

	// 使用WaitGroup等待所有goroutine完成
	var wg sync.WaitGroup

	// 限制并发数
	semaphore := make(chan struct{}, tc.config.Concurrency.MaxGoroutines)

	// 启动工具调用goroutines
	for i, sess := range successfulSessions {
		wg.Add(1)
		go func(sessionIndex int, session *session.SessionResult) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("开始并发调用工具: session_id=%d", session.SessionID)

			var sessionResults []*ToolCallResult

			// 支持轮询调用
			if tc.config.ToolCall.PollingEnabled && tc.config.ToolCall.PollingCount > 1 {
				sessionResults = tc.callToolWithPolling(ctx, session.SessionID, sessionIndex)
			} else {
				result := tc.callToolOnSession(ctx, session.SessionID, sessionIndex)
				sessionResults = []*ToolCallResult{result}
			}

			resultChan <- sessionResults
		}(i, sess)

		// 速率限制 - 控制启动goroutine的频率
		if tc.config.Concurrency.RateLimit > 0 && i < len(successfulSessions)-1 {
			time.Sleep(tc.config.Concurrency.RateLimit)
		}
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var allResults []*ToolCallResult
	for sessionResults := range resultChan {
		allResults = append(allResults, sessionResults...)
		// 记录每个结果的指标
		for _, result := range sessionResults {
			tc.metrics.RecordToolCall(result.Success, result.ResponseTime, result.Error)
		}
	}

	log.Printf("并发工具调用完成: 总数=%d, 成功=%d, 失败=%d",
		len(allResults), tc.metrics.GetSuccessCount(), tc.metrics.GetFailureCount())

	return allResults, nil
}

// callToolOnSession 在单个会话上调用工具
func (tc *ToolCaller) callToolOnSession(ctx context.Context, sessionID int64, index int) *ToolCallResult {
	callID := fmt.Sprintf("tool_call_%d_%s", index, uuid.New().String()[:8])

	log.Printf("调用工具 [%s]: session_id=%d, 开始", callID, sessionID)
	startTime := time.Now()

	// 序列化工具输入参数
	argumentsJSON, err := sonic.Marshal(tc.config.ToolCall.ToolInput)
	if err != nil {
		responseTime := time.Since(startTime)
		log.Printf("调用工具 [%s]: 序列化参数失败, 耗时=%v, 错误=%v", callID, responseTime, err)
		return &ToolCallResult{
			SessionID:    sessionID,
			CallID:       callID,
			Success:      false,
			Error:        fmt.Errorf("序列化工具参数失败: %w", err),
			ResponseTime: responseTime,
		}
	}

	// 构建请求
	request := &ToolCallRequest{
		SessionID:   sessionID,
		Name:        tc.config.ToolCall.ToolName,
		Arguments:   string(argumentsJSON),
		CallID:      callID,
		TimeoutSecs: tc.config.ToolCall.TimeoutSecs,
	}

	// 执行请求（带重试）
	var lastErr error
	for attempt := 0; attempt <= tc.config.ToolCall.MaxRetries; attempt++ {
		if attempt > 0 {
			log.Printf("调用工具 [%s]: 重试第%d次", callID, attempt)
			time.Sleep(tc.config.ToolCall.RetryInterval)
		}

		status, result, errorMessage, err := tc.executeToolCall(ctx, request)
		responseTime := time.Since(startTime)

		if err == nil {
			log.Printf("调用工具 [%s]: 成功, status=%s, 耗时=%v", callID, status, responseTime)
			return &ToolCallResult{
				SessionID:    sessionID,
				CallID:       callID,
				Success:      true,
				ResponseTime: responseTime,
				Status:       status,
				Result:       result,
				ErrorMessage: errorMessage,
			}
		}

		lastErr = err
		log.Printf("调用工具 [%s]: 失败 (尝试%d/%d): %v",
			callID, attempt+1, tc.config.ToolCall.MaxRetries+1, err)
	}

	responseTime := time.Since(startTime)
	log.Printf("调用工具 [%s]: 最终失败, 耗时=%v, 错误=%v", callID, responseTime, lastErr)

	return &ToolCallResult{
		SessionID:    sessionID,
		CallID:       callID,
		Success:      false,
		Error:        lastErr,
		ResponseTime: responseTime,
	}
}

// executeToolCall 执行工具调用请求
func (tc *ToolCaller) executeToolCall(ctx context.Context, request *ToolCallRequest) (string, string, string, error) {
	url := fmt.Sprintf("%s/mcp/tool/call", tc.config.APIPrefix)

	resp, err := tc.httpClient.DoRequest("POST", url, request)
	if err != nil {
		return "", "", "", fmt.Errorf("HTTP请求失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode, string(resp.Body))
	}

	var response ToolCallResponse
	if err := sonic.Unmarshal(resp.Body, &response); err != nil {
		return "", "", "", fmt.Errorf("解析响应失败: %w, 响应: %s", err, string(resp.Body))
	}

	if response.Code != 0 {
		return "", "", "", fmt.Errorf("API业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	// 检查工具调用状态
	if response.Data.Status == "error" || response.Data.Status == "failed" {
		return response.Data.Status, response.Data.Result, response.Data.ErrorMessage,
			fmt.Errorf("工具调用失败: status=%s, error=%s", response.Data.Status, response.Data.ErrorMessage)
	}

	return response.Data.Status, response.Data.Result, response.Data.ErrorMessage, nil
}

// callToolWithPolling 在单个会话上轮询调用工具
func (tc *ToolCaller) callToolWithPolling(ctx context.Context, sessionID int64, sessionIndex int) []*ToolCallResult {
	var results []*ToolCallResult

	log.Printf("开始轮询调用工具: session_id=%d, 轮询次数=%d, 间隔=%v",
		sessionID, tc.config.ToolCall.PollingCount, tc.config.ToolCall.PollingInterval)

	for i := 0; i < tc.config.ToolCall.PollingCount; i++ {
		log.Printf("轮询调用 [%d/%d]: session_id=%d", i+1, tc.config.ToolCall.PollingCount, sessionID)

		// 调用工具
		result := tc.callToolOnSession(ctx, sessionID, sessionIndex*1000+i) // 使用不同的索引避免冲突
		results = append(results, result)

		// 如果不是最后一次调用，等待轮询间隔
		if i < tc.config.ToolCall.PollingCount-1 {
			log.Printf("等待轮询间隔: %v", tc.config.ToolCall.PollingInterval)
			time.Sleep(tc.config.ToolCall.PollingInterval)
		}
	}

	log.Printf("轮询调用完成: session_id=%d, 总调用次数=%d", sessionID, len(results))
	return results
}

// GetMetrics 获取性能指标
func (tc *ToolCaller) GetMetrics() *metrics.ToolCallMetrics {
	return tc.metrics
}

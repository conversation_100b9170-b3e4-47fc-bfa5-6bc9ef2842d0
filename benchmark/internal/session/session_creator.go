package session

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/metrics"
)

// SessionCreator 会话创建器
type SessionCreator struct {
	config     *config.Config
	httpClient HTTPClient
	metrics    *metrics.SessionMetrics
}

// HTTPClient HTTP客户端接口
type HTTPClient interface {
	DoRequest(method, url string, body interface{}) (*HTTPResponse, error)
}

// HTTPResponse HTTP响应
type HTTPResponse struct {
	StatusCode int
	Body       []byte
}

// SessionInitRequest 会话初始化请求
type SessionInitRequest struct {
	ServerIDs      []int64 `json:"server_ids"`
	EnvID          int64   `json:"env_id"`
	TimeoutMinutes int     `json:"timeout_minutes,omitempty"`
}

// SessionInitResponse 会话初始化响应
type SessionInitResponse struct {
	Code    int                     `json:"code"`
	Message string                  `json:"message"`
	Data    SessionInitResponseData `json:"data"`
}

// SessionInitResponseData 会话初始化响应数据
type SessionInitResponseData struct {
	SessionID int64                    `json:"session_id"`
	TaskID    string                   `json:"task_id"`
	MCPTools  []map[string]interface{} `json:"mcp_tools"`
}

// SessionResult 会话创建结果
type SessionResult struct {
	SessionID    int64
	TaskID       string
	Success      bool
	Error        error
	ResponseTime time.Duration
	RequestID    string
}

// NewSessionCreator 创建会话创建器
func NewSessionCreator(cfg *config.Config, httpClient HTTPClient) *SessionCreator {
	return &SessionCreator{
		config:     cfg,
		httpClient: httpClient,
		metrics:    metrics.NewSessionMetrics(),
	}
}

// CreateSessionsBatch 批量创建会话
func (sc *SessionCreator) CreateSessionsBatch(ctx context.Context) ([]*SessionResult, error) {
	log.Printf("开始批量创建会话: batch_size=%d", sc.config.BatchSize)

	// 创建结果通道
	resultChan := make(chan *SessionResult, sc.config.BatchSize)

	// 使用WaitGroup等待所有goroutine完成
	var wg sync.WaitGroup

	// 限制并发数
	semaphore := make(chan struct{}, sc.config.Concurrency.MaxGoroutines)

	// 启动会话创建goroutines
	for i := 0; i < sc.config.BatchSize; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 速率限制
			if sc.config.Concurrency.RateLimit > 0 {
				time.Sleep(sc.config.Concurrency.RateLimit)
			}

			result := sc.createSingleSession(ctx, index)
			resultChan <- result
		}(i)
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var results []*SessionResult
	for result := range resultChan {
		results = append(results, result)
		sc.metrics.RecordSessionCreation(result.Success, result.ResponseTime, result.Error)
	}

	log.Printf("批量会话创建完成: 总数=%d, 成功=%d, 失败=%d",
		len(results), sc.metrics.GetSuccessCount(), sc.metrics.GetFailureCount())

	return results, nil
}

// createSingleSession 创建单个会话
func (sc *SessionCreator) createSingleSession(ctx context.Context, index int) *SessionResult {
	requestID := fmt.Sprintf("session_%d_%s", index, uuid.New().String()[:8])

	log.Printf("创建会话 [%s]: 开始", requestID)
	startTime := time.Now()

	// 构建请求
	request := &SessionInitRequest{
		ServerIDs:      sc.config.ServerIDs,
		EnvID:          sc.config.EnvID,
		TimeoutMinutes: sc.config.TimeoutMinutes,
	}

	// 执行请求（带重试）
	var lastErr error
	for attempt := 0; attempt <= sc.config.SessionCreation.MaxRetries; attempt++ {
		if attempt > 0 {
			log.Printf("创建会话 [%s]: 重试第%d次", requestID, attempt)
			time.Sleep(sc.config.SessionCreation.RetryInterval)
		}

		sessionID, taskID, err := sc.executeSessionCreation(ctx, request, requestID)
		responseTime := time.Since(startTime)

		if err == nil {
			log.Printf("创建会话 [%s]: 成功, session_id=%d, task_id=%s, 耗时=%v",
				requestID, sessionID, taskID, responseTime)
			return &SessionResult{
				SessionID:    sessionID,
				TaskID:       taskID,
				Success:      true,
				ResponseTime: responseTime,
				RequestID:    requestID,
			}
		}

		lastErr = err
		log.Printf("创建会话 [%s]: 失败 (尝试%d/%d): %v",
			requestID, attempt+1, sc.config.SessionCreation.MaxRetries+1, err)
	}

	responseTime := time.Since(startTime)
	log.Printf("创建会话 [%s]: 最终失败, 耗时=%v, 错误=%v", requestID, responseTime, lastErr)

	return &SessionResult{
		Success:      false,
		Error:        lastErr,
		ResponseTime: responseTime,
		RequestID:    requestID,
	}
}

// executeSessionCreation 执行会话创建请求
func (sc *SessionCreator) executeSessionCreation(ctx context.Context, request *SessionInitRequest, requestID string) (int64, string, error) {
	url := fmt.Sprintf("%s/mcp/session/init", sc.config.APIPrefix)

	resp, err := sc.httpClient.DoRequest("POST", url, request)
	if err != nil {
		return 0, "", fmt.Errorf("HTTP请求失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return 0, "", fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode, string(resp.Body))
	}

	var response SessionInitResponse
	if err := sonic.Unmarshal(resp.Body, &response); err != nil {
		return 0, "", fmt.Errorf("解析响应失败: %w, 响应: %s", err, string(resp.Body))
	}

	if response.Code != 0 {
		return 0, "", fmt.Errorf("API业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	return response.Data.SessionID, response.Data.TaskID, nil
}

// GetMetrics 获取性能指标
func (sc *SessionCreator) GetMetrics() *metrics.SessionMetrics {
	return sc.metrics
}

// GetSuccessfulSessions 获取成功创建的会话列表
func (sc *SessionCreator) GetSuccessfulSessions(results []*SessionResult) []*SessionResult {
	var successful []*SessionResult
	for _, result := range results {
		if result.Success {
			successful = append(successful, result)
		}
	}
	return successful
}

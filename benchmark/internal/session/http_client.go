package session

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/bytedance/sonic"
)

// DefaultHTTPClient 默认HTTP客户端实现
type DefaultHTTPClient struct {
	client *http.Client
}

// NewDefaultHTTPClient 创建默认HTTP客户端
func NewDefaultHTTPClient(timeout time.Duration) *DefaultHTTPClient {
	return &DefaultHTTPClient{
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// DoRequest 执行HTTP请求
func (c *DefaultHTTPClient) DoRequest(method, url string, body interface{}) (*HTTPResponse, error) {
	var bodyReader io.Reader

	if body != nil {
		bodyBytes, err := sonic.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
		bodyReader = strings.NewReader(string(bodyBytes))
	}

	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	return &HTTPResponse{
		StatusCode: resp.StatusCode,
		Body:       responseBody,
	}, nil
}

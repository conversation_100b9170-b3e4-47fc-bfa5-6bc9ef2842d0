package session

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/bytedance/sonic"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
)

// SessionStopper 会话停止器
type SessionStopper struct {
	config     *config.Config
	httpClient HTTPClient
	metrics    *SessionStopMetrics
}

// SessionStopRequest 会话停止请求
type SessionStopRequest struct {
	SessionID int64  `json:"session_id"`
	Message   string `json:"message,omitempty"`
}

// SessionStopResponse 会话停止响应
type SessionStopResponse struct {
	Code    int                     `json:"code"`
	Message string                  `json:"message"`
	Data    SessionStopResponseData `json:"data"`
}

// SessionStopResponseData 会话停止响应数据
type SessionStopResponseData struct {
	SessionID int64  `json:"session_id"`
	Status    string `json:"status"`
}

// SessionStopResult 会话停止结果
type SessionStopResult struct {
	SessionID    int64
	Success      bool
	Error        error
	ResponseTime time.Duration
	Status       string
}

// SessionStopMetrics 会话停止性能指标
type SessionStopMetrics struct {
	mu sync.RWMutex

	totalAttempts int
	successCount  int
	failureCount  int

	responseTimes []time.Duration
	totalTime     time.Duration
	minTime       time.Duration
	maxTime       time.Duration

	errors map[string]int
}

// NewSessionStopMetrics 创建会话停止指标收集器
func NewSessionStopMetrics() *SessionStopMetrics {
	return &SessionStopMetrics{
		errors:  make(map[string]int),
		minTime: time.Duration(0),
		maxTime: time.Duration(0),
	}
}

// RecordSessionStop 记录会话停止结果
func (ssm *SessionStopMetrics) RecordSessionStop(success bool, responseTime time.Duration, err error) {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	ssm.totalAttempts++
	ssm.responseTimes = append(ssm.responseTimes, responseTime)
	ssm.totalTime += responseTime

	if ssm.minTime == 0 || responseTime < ssm.minTime {
		ssm.minTime = responseTime
	}
	if responseTime > ssm.maxTime {
		ssm.maxTime = responseTime
	}

	if success {
		ssm.successCount++
	} else {
		ssm.failureCount++
		if err != nil {
			errorKey := err.Error()
			ssm.errors[errorKey]++
		}
	}
}

// GetSuccessCount 获取成功次数
func (ssm *SessionStopMetrics) GetSuccessCount() int {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()
	return ssm.successCount
}

// GetFailureCount 获取失败次数
func (ssm *SessionStopMetrics) GetFailureCount() int {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()
	return ssm.failureCount
}

// GetSuccessRate 获取成功率
func (ssm *SessionStopMetrics) GetSuccessRate() float64 {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()

	if ssm.totalAttempts == 0 {
		return 0
	}
	return float64(ssm.successCount) / float64(ssm.totalAttempts) * 100
}

// GetAverageResponseTime 获取平均响应时间
func (ssm *SessionStopMetrics) GetAverageResponseTime() time.Duration {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()

	if ssm.totalAttempts == 0 {
		return 0
	}
	return ssm.totalTime / time.Duration(ssm.totalAttempts)
}

// GetErrors 获取错误统计
func (ssm *SessionStopMetrics) GetErrors() map[string]int {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()

	errors := make(map[string]int)
	for k, v := range ssm.errors {
		errors[k] = v
	}
	return errors
}

// NewSessionStopper 创建会话停止器
func NewSessionStopper(cfg *config.Config, httpClient HTTPClient) *SessionStopper {
	return &SessionStopper{
		config:     cfg,
		httpClient: httpClient,
		metrics:    NewSessionStopMetrics(),
	}
}

// StopSessions 停止所有会话
func (ss *SessionStopper) StopSessions(ctx context.Context, sessions []*SessionResult) ([]*SessionStopResult, error) {
	if !ss.config.SessionStop.Enabled {
		log.Printf("会话停止功能未启用，跳过停止操作")
		return []*SessionStopResult{}, nil
	}

	// 过滤成功的会话
	var successfulSessions []*SessionResult
	for _, sess := range sessions {
		if sess.Success {
			successfulSessions = append(successfulSessions, sess)
		}
	}

	if len(successfulSessions) == 0 {
		log.Printf("没有成功的会话需要停止")
		return []*SessionStopResult{}, nil
	}

	log.Printf("开始并发停止%d个会话", len(successfulSessions))

	// 创建结果通道
	resultChan := make(chan *SessionStopResult, len(successfulSessions))

	// 使用WaitGroup等待所有goroutine完成
	var wg sync.WaitGroup

	// 限制并发数
	semaphore := make(chan struct{}, ss.config.Concurrency.MaxGoroutines)

	// 启动会话停止goroutines
	for i, sess := range successfulSessions {
		wg.Add(1)
		go func(sessionIndex int, session *SessionResult) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("开始停止会话: session_id=%d", session.SessionID)

			result := ss.stopSingleSession(ctx, session.SessionID, sessionIndex)
			resultChan <- result
		}(i, sess)

		// 速率限制
		if ss.config.Concurrency.RateLimit > 0 && i < len(successfulSessions)-1 {
			time.Sleep(ss.config.Concurrency.RateLimit)
		}
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var results []*SessionStopResult
	for result := range resultChan {
		results = append(results, result)
		ss.metrics.RecordSessionStop(result.Success, result.ResponseTime, result.Error)
	}

	log.Printf("会话停止完成: 总数=%d, 成功=%d, 失败=%d",
		len(results), ss.metrics.GetSuccessCount(), ss.metrics.GetFailureCount())

	return results, nil
}

// stopSingleSession 停止单个会话
func (ss *SessionStopper) stopSingleSession(ctx context.Context, sessionID int64, index int) *SessionStopResult {
	log.Printf("停止会话 [%d]: session_id=%d, 开始", index, sessionID)
	startTime := time.Now()

	// 构建请求
	request := &SessionStopRequest{
		SessionID: sessionID,
		Message:   ss.config.SessionStop.Message,
	}

	// 执行请求（带重试）
	var lastErr error
	for attempt := 0; attempt <= ss.config.SessionStop.MaxRetries; attempt++ {
		if attempt > 0 {
			log.Printf("停止会话 [%d]: 重试第%d次", index, attempt)
			time.Sleep(ss.config.SessionStop.RetryInterval)
		}

		status, err := ss.executeSessionStop(ctx, request)
		responseTime := time.Since(startTime)

		if err == nil {
			log.Printf("停止会话 [%d]: 成功, session_id=%d, status=%s, 耗时=%v",
				index, sessionID, status, responseTime)
			return &SessionStopResult{
				SessionID:    sessionID,
				Success:      true,
				ResponseTime: responseTime,
				Status:       status,
			}
		}

		lastErr = err
		log.Printf("停止会话 [%d]: 失败 (尝试%d/%d): %v",
			index, attempt+1, ss.config.SessionStop.MaxRetries+1, err)
	}

	responseTime := time.Since(startTime)
	log.Printf("停止会话 [%d]: 最终失败, session_id=%d, 耗时=%v, 错误=%v",
		index, sessionID, responseTime, lastErr)

	return &SessionStopResult{
		SessionID:    sessionID,
		Success:      false,
		Error:        lastErr,
		ResponseTime: responseTime,
	}
}

// executeSessionStop 执行会话停止请求
func (ss *SessionStopper) executeSessionStop(ctx context.Context, request *SessionStopRequest) (string, error) {
	url := fmt.Sprintf("%s/mcp/session/stop", ss.config.APIPrefix)

	resp, err := ss.httpClient.DoRequest("POST", url, request)
	if err != nil {
		return "", fmt.Errorf("HTTP请求失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode, string(resp.Body))
	}

	var response SessionStopResponse
	if err := sonic.Unmarshal(resp.Body, &response); err != nil {
		return "", fmt.Errorf("解析响应失败: %w, 响应: %s", err, string(resp.Body))
	}

	if response.Code != 0 {
		return "", fmt.Errorf("API业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	return response.Data.Status, nil
}

// GetMetrics 获取性能指标
func (ss *SessionStopper) GetMetrics() *SessionStopMetrics {
	return ss.metrics
}

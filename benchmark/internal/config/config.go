package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// ServerConfig MCP服务器配置
type ServerConfig struct {
	ServerName           string                 `yaml:"server_name" json:"server_name"`
	Type                 string                 `yaml:"type,omitempty" json:"type,omitempty"`
	Command              string                 `yaml:"command,omitempty" json:"command,omitempty"`
	Args                 []string               `yaml:"args,omitempty" json:"args,omitempty"`
	Url                  string                 `yaml:"url,omitempty" json:"url,omitempty"`
	Headers              map[string]interface{} `yaml:"headers,omitempty" json:"headers,omitempty"`
	Description          string                 `yaml:"description,omitempty" json:"description,omitempty"`
	MockMcpServerCodeURL string                 `yaml:"mock_mcp_server_code_url,omitempty" json:"mock_mcp_server_code_url,omitempty"`
}

// EnvConfig 环境配置
type EnvConfig struct {
	Name                  string                  `yaml:"name" json:"name"`
	Description           string                  `yaml:"description" json:"description"`
	EnvironmentDependency []EnvironmentDependency `yaml:"environment_dependency" json:"environment_dependency"`
}

// EnvironmentDependency 环境依赖配置
type EnvironmentDependency struct {
	Path    string `yaml:"path" json:"path"`
	Type    string `yaml:"type" json:"type"` // directory, file, db, url
	Content string `yaml:"content,omitempty" json:"content,omitempty"`
}

// Config 压力测试配置结构
type Config struct {
	// API配置
	APIPrefix string `yaml:"api_prefix" json:"api_prefix"`
	Timeout   int    `yaml:"timeout" json:"timeout"` // 请求超时时间（秒）

	// 初始化配置
	ServerConf *ServerConfig `yaml:"server_conf,omitempty" json:"server_conf,omitempty"` // MCP服务器配置
	EnvConf    *EnvConfig    `yaml:"env_conf,omitempty" json:"env_conf,omitempty"`       // 环境配置

	// 测试目标配置
	ServerIDs []int64 `yaml:"server_ids" json:"server_ids"` // MCP服务器ID列表
	EnvID     int64   `yaml:"env_id" json:"env_id"`         // 环境ID

	// 会话创建配置
	BatchSize       int `yaml:"batch_size" json:"batch_size"`           // 并行创建的会话数量
	TimeoutMinutes  int `yaml:"timeout_minutes" json:"timeout_minutes"` // 会话超时时间（分钟）
	SessionCreation struct {
		MaxRetries    int           `yaml:"max_retries" json:"max_retries"`       // 最大重试次数
		RetryInterval time.Duration `yaml:"retry_interval" json:"retry_interval"` // 重试间隔
	} `yaml:"session_creation" json:"session_creation"`

	// 会话停止配置
	SessionStop struct {
		Enabled       bool          `yaml:"enabled" json:"enabled"`               // 是否启用会话停止
		Message       string        `yaml:"message" json:"message"`               // 停止消息
		MaxRetries    int           `yaml:"max_retries" json:"max_retries"`       // 最大重试次数
		RetryInterval time.Duration `yaml:"retry_interval" json:"retry_interval"` // 重试间隔
	} `yaml:"session_stop" json:"session_stop"`

	// 工具调用配置
	ToolCall struct {
		ToolName      string                 `yaml:"tool_name" json:"tool_name"`             // 工具名称
		ToolInput     map[string]interface{} `yaml:"tool_input" json:"tool_input"`           // 工具输入参数
		TimeoutSecs   int                    `yaml:"timeout_seconds" json:"timeout_seconds"` // 工具调用超时时间（秒）
		MaxRetries    int                    `yaml:"max_retries" json:"max_retries"`         // 最大重试次数
		RetryInterval time.Duration          `yaml:"retry_interval" json:"retry_interval"`   // 重试间隔

		// 轮询配置
		PollingEnabled  bool          `yaml:"polling_enabled" json:"polling_enabled"`   // 是否启用轮询调用
		PollingCount    int           `yaml:"polling_count" json:"polling_count"`       // 轮询调用次数
		PollingInterval time.Duration `yaml:"polling_interval" json:"polling_interval"` // 轮询间隔时间
	} `yaml:"tool_call" json:"tool_call"`

	// 报告配置
	Report struct {
		OutputDir     string `yaml:"output_dir" json:"output_dir"`         // 输出目录
		DetailedLogs  bool   `yaml:"detailed_logs" json:"detailed_logs"`   // 是否生成详细日志
		IncludeErrors bool   `yaml:"include_errors" json:"include_errors"` // 是否包含错误详情
	} `yaml:"report" json:"report"`

	// 并发控制
	Concurrency struct {
		MaxGoroutines int           `yaml:"max_goroutines" json:"max_goroutines"` // 最大并发goroutine数
		RateLimit     time.Duration `yaml:"rate_limit" json:"rate_limit"`         // 请求速率限制
	} `yaml:"concurrency" json:"concurrency"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		APIPrefix:      "http://localhost:8080/api/v1",
		Timeout:        30,
		ServerIDs:      []int64{1},
		EnvID:          1,
		BatchSize:      10,
		TimeoutMinutes: 5,
		SessionCreation: struct {
			MaxRetries    int           `yaml:"max_retries" json:"max_retries"`
			RetryInterval time.Duration `yaml:"retry_interval" json:"retry_interval"`
		}{
			MaxRetries:    3,
			RetryInterval: 2 * time.Second,
		},
		SessionStop: struct {
			Enabled       bool          `yaml:"enabled" json:"enabled"`
			Message       string        `yaml:"message" json:"message"`
			MaxRetries    int           `yaml:"max_retries" json:"max_retries"`
			RetryInterval time.Duration `yaml:"retry_interval" json:"retry_interval"`
		}{
			Enabled:       false,
			Message:       "压力测试完成，自动停止会话",
			MaxRetries:    3,
			RetryInterval: 2 * time.Second,
		},
		ToolCall: struct {
			ToolName      string                 `yaml:"tool_name" json:"tool_name"`
			ToolInput     map[string]interface{} `yaml:"tool_input" json:"tool_input"`
			TimeoutSecs   int                    `yaml:"timeout_seconds" json:"timeout_seconds"`
			MaxRetries    int                    `yaml:"max_retries" json:"max_retries"`
			RetryInterval time.Duration          `yaml:"retry_interval" json:"retry_interval"`

			// 轮询配置
			PollingEnabled  bool          `yaml:"polling_enabled" json:"polling_enabled"`
			PollingCount    int           `yaml:"polling_count" json:"polling_count"`
			PollingInterval time.Duration `yaml:"polling_interval" json:"polling_interval"`
		}{
			ToolName: "filesystem__read_file",
			ToolInput: map[string]interface{}{
				"path": "/tmp/test.txt",
			},
			TimeoutSecs:     30,
			MaxRetries:      3,
			RetryInterval:   1 * time.Second,
			PollingEnabled:  false,
			PollingCount:    1,
			PollingInterval: 5 * time.Second,
		},
		Report: struct {
			OutputDir     string `yaml:"output_dir" json:"output_dir"`
			DetailedLogs  bool   `yaml:"detailed_logs" json:"detailed_logs"`
			IncludeErrors bool   `yaml:"include_errors" json:"include_errors"`
		}{
			OutputDir:     "logs",
			DetailedLogs:  true,
			IncludeErrors: true,
		},
		Concurrency: struct {
			MaxGoroutines int           `yaml:"max_goroutines" json:"max_goroutines"`
			RateLimit     time.Duration `yaml:"rate_limit" json:"rate_limit"`
		}{
			MaxGoroutines: 100,
			RateLimit:     100 * time.Millisecond,
		},
	}
}

// LoadFromFile 从文件加载配置
func LoadFromFile(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	config := DefaultConfig()
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return config, nil
}

// SaveToFile 保存配置到文件
func (c *Config) SaveToFile(filename string) error {
	data, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// Validate 验证配置有效性
func (c *Config) Validate() error {
	if c.APIPrefix == "" {
		return fmt.Errorf("api_prefix不能为空")
	}

	if c.Timeout <= 0 {
		return fmt.Errorf("timeout必须大于0")
	}

	// 如果有server_conf配置，则允许server_ids为空（将在初始化后自动设置）
	// 否则server_ids不能为空
	if len(c.ServerIDs) == 0 && c.ServerConf == nil {
		return fmt.Errorf("server_ids不能为空，或者需要配置server_conf进行服务器初始化")
	}

	// 如果有env_conf配置，则允许env_id为0（将在初始化后自动设置）
	// 否则env_id必须大于0
	if c.EnvID <= 0 && c.EnvConf == nil {
		return fmt.Errorf("env_id必须大于0，或者需要配置env_conf进行环境初始化")
	}

	if c.BatchSize <= 0 {
		return fmt.Errorf("batch_size必须大于0")
	}

	if c.ToolCall.ToolName == "" {
		return fmt.Errorf("tool_call.tool_name不能为空")
	}

	if c.ToolCall.TimeoutSecs <= 0 {
		return fmt.Errorf("tool_call.timeout_seconds必须大于0")
	}

	if c.Report.OutputDir == "" {
		return fmt.Errorf("report.output_dir不能为空")
	}

	return nil
}

// GetHTTPTimeout 获取HTTP客户端超时时间
func (c *Config) GetHTTPTimeout() time.Duration {
	return time.Duration(c.Timeout) * time.Second
}

// GetToolCallTimeout 获取工具调用超时时间
func (c *Config) GetToolCallTimeout() time.Duration {
	return time.Duration(c.ToolCall.TimeoutSecs) * time.Second
}

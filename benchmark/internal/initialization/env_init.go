package initialization

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/bytedance/sonic"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/session"
)

// EnvInitializer 环境初始化器
type EnvInitializer struct {
	config     *config.Config
	httpClient session.HTTPClient
}

// EnvInitRequest 环境初始化请求
type EnvInitRequest struct {
	Name                  string                 `json:"name"`
	Description           string                 `json:"description"`
	EnvironmentDependency []EnvDependencyRequest `json:"environment_dependency"`
}

// EnvDependencyRequest 环境依赖请求
type EnvDependencyRequest struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content,omitempty"`
}

// EnvInitResponse 环境初始化响应
type EnvInitResponse struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    EnvInitResponseData `json:"data"`
}

// EnvInitResponseData 环境初始化响应数据
type EnvInitResponseData struct {
	EnvID    int64  `json:"env_id"`
	BosURL   string `json:"bos_url"`
	MD5      string `json:"md5"`
	FileSize int64  `json:"file_size"`
}

// NewEnvInitializer 创建环境初始化器
func NewEnvInitializer(cfg *config.Config, httpClient session.HTTPClient) *EnvInitializer {
	return &EnvInitializer{
		config:     cfg,
		httpClient: httpClient,
	}
}

// InitializeEnvironment 初始化环境
func (ei *EnvInitializer) InitializeEnvironment(ctx context.Context) (int64, error) {
	if ei.config.EnvConf == nil {
		return 0, fmt.Errorf("环境配置为空，跳过环境初始化")
	}

	log.Printf("开始初始化环境: %s", ei.config.EnvConf.Name)

	// 构建请求
	request := &EnvInitRequest{
		Name:        ei.config.EnvConf.Name,
		Description: ei.config.EnvConf.Description,
	}

	// 转换环境依赖
	for _, dep := range ei.config.EnvConf.EnvironmentDependency {
		request.EnvironmentDependency = append(request.EnvironmentDependency, EnvDependencyRequest{
			Path:    dep.Path,
			Type:    dep.Type,
			Content: dep.Content,
		})
	}

	// 执行请求
	envID, bosURL, err := ei.executeEnvInit(ctx, request)
	if err != nil {
		return 0, fmt.Errorf("环境初始化失败: %w", err)
	}

	log.Printf("环境初始化成功: env_id=%d, name=%s, bos_url=%s", envID, ei.config.EnvConf.Name, bosURL)
	return envID, nil
}

// executeEnvInit 执行环境初始化请求
func (ei *EnvInitializer) executeEnvInit(ctx context.Context, request *EnvInitRequest) (int64, string, error) {
	url := fmt.Sprintf("%s/mcp/env/init", ei.config.APIPrefix)

	resp, err := ei.httpClient.DoRequest("POST", url, request)
	if err != nil {
		return 0, "", fmt.Errorf("HTTP请求失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return 0, "", fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode, string(resp.Body))
	}

	var response EnvInitResponse
	if err := sonic.Unmarshal(resp.Body, &response); err != nil {
		return 0, "", fmt.Errorf("解析响应失败: %w, 响应: %s", err, string(resp.Body))
	}

	if response.Code != 0 {
		return 0, "", fmt.Errorf("API业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	return response.Data.EnvID, response.Data.BosURL, nil
}

// CheckEnvironmentExists 检查环境是否已存在
func (ei *EnvInitializer) CheckEnvironmentExists(ctx context.Context, envName string) (bool, int64, error) {
	// 这里可以实现检查环境是否已存在的逻辑
	// 目前简化处理，总是返回不存在
	return false, 0, nil
}

// InitializeEnvironmentIfNeeded 如果需要则初始化环境
func (ei *EnvInitializer) InitializeEnvironmentIfNeeded(ctx context.Context) (int64, error) {
	if ei.config.EnvConf == nil {
		log.Printf("未配置环境初始化，跳过")
		return 0, nil
	}

	// 检查环境是否已存在
	exists, existingID, err := ei.CheckEnvironmentExists(ctx, ei.config.EnvConf.Name)
	if err != nil {
		return 0, fmt.Errorf("检查环境存在性失败: %w", err)
	}

	if exists {
		log.Printf("环境已存在: env_id=%d, name=%s", existingID, ei.config.EnvConf.Name)
		return existingID, nil
	}

	// 初始化新环境
	return ei.InitializeEnvironment(ctx)
}

// WaitForEnvironmentReady 等待环境就绪
func (ei *EnvInitializer) WaitForEnvironmentReady(ctx context.Context, envID int64) error {
	log.Printf("等待环境就绪: env_id=%d", envID)

	// 这里可以实现等待环境就绪的逻辑
	// 目前简化处理，等待一段时间
	time.Sleep(5 * time.Second)

	log.Printf("环境已就绪: env_id=%d", envID)
	return nil
}

package initialization

import (
	"context"
	"fmt"
	"log"

	"github.com/bytedance/sonic"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/session"
)

// ServerInitializer MCP服务器初始化器
type ServerInitializer struct {
	config     *config.Config
	httpClient session.HTTPClient
}

// ServerInitRequest 服务器初始化请求
type ServerInitRequest struct {
	ServerName           string                 `json:"server_name"`
	Type                 string                 `json:"type,omitempty"`
	Command              string                 `json:"command,omitempty"`
	Args                 []string               `json:"args,omitempty"`
	Url                  string                 `json:"url,omitempty"`
	Headers              map[string]interface{} `json:"headers,omitempty"`
	Description          string                 `json:"description,omitempty"`
	MockMcpServerCodeURL string                 `json:"mock_mcp_server_code_url,omitempty"`
}

// ServerInitResponse 服务器初始化响应
type ServerInitResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    ServerInitResponseData `json:"data"`
}

// ServerInitResponseData 服务器初始化响应数据
type ServerInitResponseData struct {
	ServerID   int64  `json:"server_id"`
	ServerName string `json:"server_name"`
	Status     string `json:"status"`
}

// NewServerInitializer 创建服务器初始化器
func NewServerInitializer(cfg *config.Config, httpClient session.HTTPClient) *ServerInitializer {
	return &ServerInitializer{
		config:     cfg,
		httpClient: httpClient,
	}
}

// InitializeServer 初始化MCP服务器
func (si *ServerInitializer) InitializeServer(ctx context.Context) (int64, error) {
	if si.config.ServerConf == nil {
		return 0, fmt.Errorf("服务器配置为空，跳过服务器初始化")
	}

	log.Printf("开始初始化MCP服务器: %s", si.config.ServerConf.ServerName)

	// 构建请求
	request := &ServerInitRequest{
		ServerName:           si.config.ServerConf.ServerName,
		Type:                 si.config.ServerConf.Type,
		Command:              si.config.ServerConf.Command,
		Args:                 si.config.ServerConf.Args,
		Url:                  si.config.ServerConf.Url,
		Headers:              si.config.ServerConf.Headers,
		Description:          si.config.ServerConf.Description,
		MockMcpServerCodeURL: si.config.ServerConf.MockMcpServerCodeURL,
	}

	// 执行请求
	serverID, err := si.executeServerInit(ctx, request)
	if err != nil {
		return 0, fmt.Errorf("服务器初始化失败: %w", err)
	}

	log.Printf("MCP服务器初始化成功: server_id=%d, server_name=%s", serverID, si.config.ServerConf.ServerName)
	return serverID, nil
}

// executeServerInit 执行服务器初始化请求
func (si *ServerInitializer) executeServerInit(ctx context.Context, request *ServerInitRequest) (int64, error) {
	url := fmt.Sprintf("%s/mcp/server/register", si.config.APIPrefix)

	resp, err := si.httpClient.DoRequest("POST", url, request)
	if err != nil {
		return 0, fmt.Errorf("HTTP请求失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return 0, fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode, string(resp.Body))
	}

	var response ServerInitResponse
	if err := sonic.Unmarshal(resp.Body, &response); err != nil {
		return 0, fmt.Errorf("解析响应失败: %w, 响应: %s", err, string(resp.Body))
	}

	if response.Code != 0 {
		return 0, fmt.Errorf("API业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	return response.Data.ServerID, nil
}

// CheckServerExists 检查服务器是否已存在
func (si *ServerInitializer) CheckServerExists(ctx context.Context, serverName string) (bool, int64, error) {
	// 这里可以实现检查服务器是否已存在的逻辑
	// 目前简化处理，总是返回不存在
	return false, 0, nil
}

// InitializeServerIfNeeded 如果需要则初始化服务器
func (si *ServerInitializer) InitializeServerIfNeeded(ctx context.Context) (int64, error) {
	if si.config.ServerConf == nil {
		log.Printf("未配置服务器初始化，跳过")
		return 0, nil
	}

	// 检查服务器是否已存在
	exists, existingID, err := si.CheckServerExists(ctx, si.config.ServerConf.ServerName)
	if err != nil {
		return 0, fmt.Errorf("检查服务器存在性失败: %w", err)
	}

	if exists {
		log.Printf("服务器已存在: server_id=%d, server_name=%s", existingID, si.config.ServerConf.ServerName)
		return existingID, nil
	}

	// 初始化新服务器
	return si.InitializeServer(ctx)
}

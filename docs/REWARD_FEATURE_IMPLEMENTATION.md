# Session Reward 功能实现文档

## 功能概述

本次实现为 MCP Online Server 添加了 session reward 功能，主要包括：

1. 在 session 表中添加 reward 字段（text 类型）
2. 添加 rewarding 和 stopping 状态到 ContainerStatus 枚举
3. 修改 session stop 接口，实现异步停止逻辑
4. 修改 calc_reward 接口，支持轮询机制和异步计算

## 实现细节

### 1. 数据库表结构修改

**文件**: `script/db/db.sql`

- 在 `obj_session` 表中添加 `reward TEXT NULL COMMENT '奖励值，计算完成后存储'` 字段
- 更新 `container_status` 字段注释，添加 `rewarding-计算奖励中` 和 `stopping-停止中` 状态

### 2. 数据模型修改

**文件**: `model/dao/session/def.go`

- 在 `ObjSession` 结构体中添加 `Reward *string` 字段
- 添加 `ContainerStatusRewarding ContainerStatus = "rewarding"` 状态枚举
- 添加 `ContainerStatusStopping ContainerStatus = "stopping"` 状态枚举
- 更新 `fillStopAt` 函数，让 stopping 状态也设置 stopped_at 时间

### 3. Metrics 支持

**文件**: `library/metrics_helper/metrics_helper.go`

- 添加 `RecordSessionRewarding()` 方法记录 rewarding 状态变更
- 添加 `RecordSessionStopping()` 方法记录 stopping 状态变更

**文件**: `model/dao/session/business.go`

- 在所有状态更新方法中添加对 rewarding 和 stopping 状态的 metrics 记录

### 7. Session Stop 接口修改

**文件**: `model/service/session/session_stop.go`

#### 主要变更：
- **异步执行**: 接口调用后立即返回，实际停止逻辑在后台异步执行
- **使用公共计算器**：调用 `reward_calculator.NewRewardCalculator().CalculateReward()` 进行同步计算
- **状态管理**:
  - 如果已经是 `stopped` 状态，直接返回
  - 如果已经是 `stopping` 状态，直接返回（幂等性）
  - 其他状态先更新为 `stopping`，然后启动异步任务
- **异步任务流程**:
  1. 使用公共计算器计算奖励值
  2. 更新数据库 reward 字段
  3. 记录会话日志到 BOS
  4. 调用 k8s 删除容器
  5. 更新状态为 `stopped`

#### 错误处理策略：
- 计算奖励失败：记录日志，继续执行后续步骤
- k8s 删除失败：记录错误信息到 err_msg，但仍更新状态为 stopped

### 5. 公共奖励计算库

**新增文件**: `library/reward_calculator/reward_calculator.go`

#### 设计目的：
- **代码复用**：避免在多个地方重复实现奖励计算逻辑
- **统一接口**：提供统一的奖励计算方法供不同模块调用
- **易于维护**：奖励计算逻辑集中管理，便于后续修改和优化
- **库级别模块**：作为通用工具库，可被多个 service 和 background 任务使用

#### 主要方法：
- `CalculateReward(ctx, session)`: 同步计算奖励值，返回结果字符串
- `CalculateRewardAsync(ctx, sessionID, session)`: 异步计算奖励值并更新到数据库

#### 目录结构优势：
- **符合项目规范**：library 目录用于存放通用工具和库
- **依赖关系清晰**：service 层依赖 library 层，符合分层架构
- **易于扩展**：后续可以添加更多奖励相关的工具方法

### 6. Calc Reward 接口重构

**文件**: `model/service/calcreward/calculate_reward.go`

#### 主要变更：
- **同步计算**：running 状态的 session 直接进行同步计算，立即返回结果
- **使用公共计算器**：调用 `reward_calculator.NewRewardCalculator()` 进行同步计算
- **状态优先级逻辑**:
  1. 如果数据库 reward 字段不为空，直接返回该值
  2. 如果 session 状态为 `stopping`，返回特殊响应
  3. 如果 session 状态为 `running`，同步计算奖励并更新数据库，返回计算结果

- **输出结构修改**:
  ```go
  type CalculateRewardOutputData struct {
      Reward        string `json:"reward"`
      SessionStatus string `json:"session_status,omitempty"`
  }
  ```

- **同步计算流程**:
  1. 调用公共计算器同步执行奖励计算
  2. 计算完成后更新 reward 字段到数据库
  3. 立即返回计算结果

- **返回逻辑**:
  - 从数据库读取时：只返回 reward 值
  - stopping 状态时：返回空 reward 和对应的 session_status
  - running 状态时：同步计算并返回实际的 reward 值

### 8. Session Monitor 增强

**文件**: `model/background/session_monitor.go`

#### 主要变更：
- **监控范围扩展**: 监控 `running`、`rewarding` 和 `stopping` 状态的 session
- **职责专注化**: 专注于 K8s 任务状态监控，不再承担业务逻辑处理
- **统一状态检查**: 所有状态的 session 都使用同一个 `checkK8sTaskStatus` 方法
- **逻辑简化**:
  - 合并了重复的状态检查逻辑
  - 根据 K8s 任务状态统一更新 session 状态
  - 避免了代码重复

## 测试用例

### Calc Reward 接口测试

**文件**: `model/service/calcreward/calculate_reward_test.go`

新增测试用例：
- `TestCalculateReward_Execute_FromDatabase`: 测试从数据库读取 reward 字段
- `TestCalculateReward_Execute_RunningStatus`: 测试 running 状态返回特殊响应
- `TestCalculateReward_Execute_StoppingStatus`: 测试 stopping 状态返回特殊响应
- `TestCalculateReward_Execute_RewardingStatus`: 测试 rewarding 状态返回轮询响应
- `TestCalculateReward_Execute_RunningToRewarding`: 测试 running 状态转为 rewarding 状态的逻辑

### Session Stop 接口测试

**文件**: `model/service/session/session_stop_test.go`

新增测试用例：
- `TestSessionStop_Execute_AsyncStopping`: 测试异步停止逻辑
- `TestSessionStop_Execute_AlreadyStopping`: 测试重复调用 stopping 状态的幂等性

## 部署注意事项

1. **数据库迁移**: 需要执行 SQL 脚本添加 reward 字段
2. **向后兼容**: 现有 session 记录的 reward 字段为 NULL，代码已正确处理
3. **异步任务**: 使用 goroutine 实现，服务重启时由 session monitor 接管
4. **监控指标**: 新增 rewarding 和 stopping 状态的 metrics 记录

## API 行为变更

### Session Stop 接口
- **之前**: 同步执行，返回 `stopped` 状态
- **现在**: 异步执行，首次调用返回 `stopping` 状态，重复调用保持幂等性

### Calc Reward 接口
- **之前**: 总是执行计算逻辑
- **现在**:
  - 优先从数据库读取 reward 字段
  - running 状态时同步计算并立即返回结果
  - stopping 状态返回特殊响应格式
  - 计算结果会保存到数据库，避免重复计算

## 使用示例

### Calc Reward 接口同步机制

```bash
# 调用 calc_reward 接口 - running 状态的 session
curl -X POST /api/v1/mcp/calc_reward \
  -H "Content-Type: application/json" \
  -d '{"session_id": 123}'

# 同步计算完成后直接返回结果：{"reward": "0.85"}

# 重复调用已计算过的 session
curl -X POST /api/v1/mcp/calc_reward \
  -H "Content-Type: application/json" \
  -d '{"session_id": 123}'

# 从数据库直接返回：{"reward": "0.85"}
```

### Session Stop 接口异步机制

```bash
# 第一次调用
curl -X POST /api/v1/mcp/session/stop \
  -H "Content-Type: application/json" \
  -d '{"session_id": 123}'

# 响应：{"session_id": 123, "status": "stopping"}

# 重复调用（幂等）
curl -X POST /api/v1/mcp/session/stop \
  -H "Content-Type: application/json" \
  -d '{"session_id": 123}'

# 响应：{"session_id": 123, "status": "stopping"}
```

## 调用关系

```
calc_reward 接口 (同步)
    ↓
library/reward_calculator.CalculateReward()

session_stop 接口 (同步)
    ↓
library/reward_calculator.CalculateReward()

session monitor (K8s状态监控)
    ↓
rpc_k8s_proxy.K8sProxyClientIns.GetTaskState()
    ↓
更新 session 状态为 stopped (如果 K8s 任务已停止)
```

## 代码重构优势

### 1. 代码复用
- **统一计算逻辑**：所有奖励计算都使用 `library/reward_calculator.RewardCalculator`
- **避免重复代码**：消除了在多个地方重复实现相同逻辑的问题
- **易于维护**：奖励计算逻辑的修改只需要在一个地方进行
- **目录结构合理**：作为通用库放在 library 目录，符合项目架构规范

### 2. 接口清晰
- **同步接口**：`CalculateReward()` 用于需要立即获得结果的场景（如 session_stop）
- **异步接口**：`CalculateRewardAsync()` 用于后台计算场景（如 calc_reward 接口）
- **职责分离**：计算逻辑与业务逻辑分离

### 3. 测试友好
- **独立测试**：公共计算器可以独立进行单元测试
- **Mock 简化**：其他模块测试时可以轻松 mock 计算器
- **覆盖率提升**：核心计算逻辑的测试覆盖率更高

## 性能影响

- **正面影响**:
  - Session stop 接口响应更快（异步执行）
  - Calc reward 接口同步执行，用户立即获得结果
  - 代码复用减少了内存占用
  - 避免重复计算，提高系统效率
- **资源使用**:
  - 每个 session stop 会启动一个 goroutine，但生命周期较短
  - calc_reward 接口同步执行，不占用额外的 goroutine
- **数据库**: 新增 reward 字段，对查询性能影响微小

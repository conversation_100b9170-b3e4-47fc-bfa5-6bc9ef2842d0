# 错误码体系结构设计

## 1. 设计原则

- **分层结构**: 错误码按模块和功能进行分层，便于扩展和维护。
- **清晰可读**: 错误码的命名和分组应清晰地反映其含义。
- **信息分离**: 对外暴露的错误信息 (`message`) 应简洁通用，不包含敏感的内部实现细节。详细的内部错误信息 (`internalError`) 应记录在日志中，用于调试。
- **向后兼容**: 尽可能保留现有的错误码，以减少对现有客户端的影响。

## 2. 错误码结构

错误码将采用以下格式：

`33<模块代码><错误序号>`

- `33`: 项目基础代码 (ProjectBase)，保持不变。
- `<模块代码>`: 2位数字，用于标识不同的功能模块。
- `<错误序号>`: 2位数字，用于标识模块内的具体错误。

## 3. 模块代码分配

| 模块代码 | 模块名称 | 说明 |
|---|---|---|
| 00 | 系统通用 (System) | 数据库、文件、配置等核心系统错误 |
| 01 | 输入验证 (Validation) | 用户输入、业务规则验证错误 |
| 02 | 认证授权 (Auth) | 用户认证、权限校验错误 |
| 03 | 会话管理 (Session) | 会话生命周期管理错误 |
| 04 | MCP服务 (MCP Service) | MCP服务注册和发现错误 |
| 05 | 环境管理 (Environment) | 运行时环境管理错误 |
| 06 | 对象存储 (BOS) | BOS相关操作错误 |
| 07 | K8s容器 (K8s) | K8s容器操作错误 |
| 08 | 工具调用 (Tool Call) | 工具执行相关错误 |
| 09 | 镜像管理 (Image) | 镜像管理错误 |
| 10 | 计费 (Reward) | 计费相关错误 |
| 11 | 限流 (Rate Limit) | 接口限流错误 |
| 12 | 业务逻辑 (Business) | 特定业务逻辑错误 |

## 4. 错误码定义示例

### 模块: 00 - 系统通用 (System)

| 错误码 | 常量名 | 对外信息 |
|---|---|---|
| 330001 | `SQLSelectError` | "数据库查询失败" |
| 330002 | `SQLInsertError` | "数据库插入失败" |
| ... | ... | ... |
| 330021 | `SysFileReadError` | "文件读取失败" |

### 模块: 01 - 输入验证 (Validation)

| 错误码 | 常量名 | 对外信息 |
|---|---|---|
| 330101 | `InvalidInput` | "无效的输入参数" |
| 330102 | `RequiredFieldMissing` | "缺少必要的参数" |
| 330103 | `InvalidFieldFormat` | "参数格式错误" |

## 5. 新的错误处理流程

1.  在 `library/errors` 包中创建一个 `New(code int, internalErr error) error` 辅助函数。
2.  该函数会：
    *   根据 `code` 从 `library/errcode/messages.go` 中查找对应的对外错误信息 `message`。
    *   创建一个 `*lib_error.CustomErr` 实例。
    *   将 `internalErr.Error()` 赋值给 `ExtData` 字段，用于日志记录和调试。
    *   返回创建的 `*lib_error.CustomErr`。
3.  在业务逻辑中，使用 `errors.New(errcode.SQLInsertError, err)` 代替 `&lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: tx.Error.Error()}`。

## 6. Mermaid 流程图

```mermaid
graph TD
    A[业务逻辑中发生错误, e.g., a database error] --> B{调用 `errors.New(errcode.SQLInsertError, err)`};
    B --> C{`errors.New` 函数};
    C --> D[1. 根据 `errcode` 获取对外 message];
    C --> E[2. 创建 `lib_error.CustomErr`];
    E --> F[设置 `Code`];
    D --> F;
    C --> G[3. 将 `internalErr` 存入 `ExtData`];
    G --> F;
    F --> H{返回 `*lib_error.CustomErr`};
    H --> I[API 接口层];
    I --> J{框架解析错误};
    J --> K[返回 JSON 响应: `{"code": 330002, "message": "数据库插入失败"}`];
    G --> L[日志记录 `ExtData` 的详细错误];
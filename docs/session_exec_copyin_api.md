# Session Exec 和 Session Copyin 接口文档

本文档描述了新增的两个 Session 相关接口：`session_exec` 和 `session_copyin`。

## 1. Session Exec 接口

### 接口描述
在指定的 Session 容器中执行命令。

### 请求信息
- **URL**: `POST /api/v1/mcp/session/exec`
- **Content-Type**: `application/json`

### 请求参数
```json
{
  "session_id": 123,                    // 可选，Session ID
  "session_code": "session-abc-123",    // 可选，Session 代码（session_id 和 session_code 必须提供一个）
  "command": ["echo", "hello world"]    // 必需，要执行的命令数组
}
```

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "session_id": 123,
    "output": "hello world\n"
  }
}
```

### 错误码
- `33014`: 输入参数无效（session_id 或 session_code 必须提供一个）
- `33604`: Session 失败（Session 状态不是运行中）
- `33605`: Session 不存在
- `33401`: K8s 容器操作错误（命令执行失败）

### 使用示例
```bash
curl -X POST "http://localhost:8080/api/v1/mcp/session/exec" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": 123,
    "command": ["ls", "-la", "/tmp"]
  }'
```

## 2. Session Copyin 接口

### 接口描述
将文件从指定的 URL 复制到 Session 容器中。

### 请求信息
- **URL**: `POST /api/v1/mcp/session/copyin`
- **Content-Type**: `application/json`

### 请求参数
```json
{
  "session_id": 123,                    // 可选，Session ID
  "session_code": "session-abc-123",    // 可选，Session 代码（session_id 和 session_code 必须提供一个）
  "files": [                            // 必需，文件信息数组，至少包含一个文件
    {
      "file_url": "https://example.com/file1.txt",  // 必需，文件的 URL
      "dest_path": "/tmp/file1.txt"                 // 必需，容器中的目标路径
    },
    {
      "file_url": "https://example.com/file2.txt",
      "dest_path": "/tmp/file2.txt"
    }
  ]
}
```

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "session_id": 123,
    "success": true,
    "message": "成功上传 2 个文件"
  }
}
```

### 错误码
- `33014`: 输入参数无效（session_id 或 session_code 必须提供一个，或文件列表为空）
- `33604`: Session 失败（Session 状态不是运行中）
- `33605`: Session 不存在
- `33401`: K8s 容器操作错误（文件上传失败）

### 使用示例
```bash
curl -X POST "http://localhost:8080/api/v1/mcp/session/copyin" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": 123,
    "files": [
      {
        "file_url": "https://bos.example.com/data/input.csv",
        "dest_path": "/workspace/input.csv"
      },
      {
        "file_url": "https://bos.example.com/scripts/process.py",
        "dest_path": "/workspace/process.py"
      }
    ]
  }'
```

## 3. 注意事项

1. **Session 状态检查**: 两个接口都会检查 Session 的状态，只有状态为 `running` 的 Session 才能执行命令或复制文件。

2. **容器任务关联**: Session 必须有关联的 Kubernetes 任务（JobID 不为空）才能执行操作。

3. **文件 URL 格式**: `session_copyin` 接口中的 `file_url` 应该是可公开访问的 HTTP/HTTPS URL，Kubernetes 集群需要能够下载这些文件。

4. **路径权限**: 确保目标路径在容器中是可写的，建议使用 `/tmp` 或 `/workspace` 等目录。

5. **命令格式**: `session_exec` 接口中的 `command` 参数是字符串数组，第一个元素是命令名，后续元素是参数。

## 4. 实现细节

- 两个接口都通过调用 K8s Proxy 服务来实现实际的容器操作
- `session_exec` 调用 `/api/v1/internal/dataeng_k8s/exec_command` 接口
- `session_copyin` 调用 `/api/v1/internal/dataeng_k8s/upload_file` 接口
- 支持通过 `session_id` 或 `session_code` 来标识 Session
- 包含完整的错误处理和状态检查

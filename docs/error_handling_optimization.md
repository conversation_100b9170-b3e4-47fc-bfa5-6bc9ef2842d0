# MCP Online Server 错误处理优化指南

## 概述

本文档描述了 MCP Online Server 项目的错误处理优化方案，旨在提供更安全、更一致、更用户友好的错误处理机制。

## 优化目标

1. **安全性**：防止敏感信息（如SQL错误、文件路径、堆栈跟踪）泄露给外部客户端
2. **一致性**：统一错误码使用和错误消息格式
3. **可追踪性**：提供错误关联ID和详细日志用于调试
4. **用户友好性**：提供清晰、可操作的错误消息

## 核心组件

### 1. 增强的错误码结构 (`library/errcode/code.go`)

#### 新增错误类别：

- **认证/授权 (50-99)**：身份验证和权限相关错误
- **限流 (900-999)**：速率限制和配额相关错误  
- **业务逻辑 (1000+)**：业务规则和工作流相关错误

#### 重新组织的系统错误：

- **数据库操作 (1-20)**：SQL相关错误，增加连接、事务、约束等细分类型
- **文件操作 (21-40)**：文件系统操作错误
- **输入验证 (41-60)**：增强的验证错误分类
- **配置/系统 (61-80)**：配置和系统初始化错误

### 2. 安全错误消息框架 (`library/errcode/messages.go`)

#### 核心功能：

- **安全消息生成器**：根据错误码自动生成对外安全的错误消息
- **错误上下文追踪**：提供错误关联ID和时间戳用于调试
- **辅助创建函数**：简化CustomErr创建过程

#### 主要函数：

```go
// 创建带有安全消息的CustomErr
func NewCustomErr(errorCode int, details string, operation ...string) *lib_error.CustomErr

// 创建带有自定义安全消息的CustomErr  
func NewCustomErrWithMessage(errorCode int, safeMessage string, details string, operation ...string) *lib_error.CustomErr

// 根据错误码获取安全错误消息
func GetSafeErrorMessage(errorCode int) string
```

## 迁移指南

### 旧模式 vs 新模式

#### 旧模式（不安全）：
```go
if tx.Error != nil {
    resource.LoggerService.Warning(ctx, tx.Error.Error())
    return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: tx.Error.Error()}
}
```

#### 新模式（安全）：
```go
if tx.Error != nil {
    resource.LoggerService.Warning(ctx, fmt.Sprintf("Insert MCP environment failed: %v", tx.Error))
    return 0, errcode.NewCustomErr(errcode.SQLInsertError, tx.Error.Error(), "Insert MCP environment")
}
```

### 迁移步骤

1. **导入新的errcode包**：
   ```go
   import "icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
   ```

2. **替换CustomErr创建**：
   - 使用 `errcode.NewCustomErr()` 替代直接创建 `&lib_error.CustomErr{}`
   - 在日志中记录详细错误信息，在API响应中返回安全消息

3. **更新错误码使用**：
   - 使用更具体的错误码（如 `RequiredFieldMissing` 而不是 `UserInputError`）
   - 为新功能使用新增的错误类别

## 错误消息安全性

### 安全原则

1. **不暴露内部实现细节**：避免SQL错误、文件路径、堆栈跟踪
2. **提供可操作的信息**：告诉用户如何解决问题
3. **保持一致性**：相同类型的错误使用相同的消息格式
4. **支持国际化**：使用中文错误消息，便于后续国际化

### 示例对比

#### 不安全的错误消息：
```
Error 1062: Duplicate entry '<EMAIL>' for key 'users.email_unique'
```

#### 安全的错误消息：
```
邮箱地址已存在，请使用其他邮箱
```

## 错误追踪和调试

### 错误关联ID

每个错误都会生成唯一的关联ID，用于：
- 关联用户报告的错误与服务器日志
- 跨服务追踪错误传播
- 调试和问题排查

### 日志记录

- **详细日志**：记录完整的错误信息、操作上下文、参数等
- **结构化日志**：使用一致的日志格式便于分析
- **分级记录**：根据错误严重程度选择合适的日志级别

## 最佳实践

### DAO层

1. **数据库错误处理**：
   ```go
   if tx.Error != nil {
       if tx.Error == gorm.ErrRecordNotFound {
           return nil, errcode.NewCustomErr(errcode.ResourceNotFound, 
               fmt.Sprintf("Resource not found: %s", resourceType), operation)
       }
       resource.LoggerService.Warning(ctx, fmt.Sprintf("%s failed: %v", operation, tx.Error))
       return nil, errcode.NewCustomErr(errcode.SQLSelectError, tx.Error.Error(), operation)
   }
   ```

2. **验证错误处理**：
   ```go
   if field == "" {
       return errcode.NewCustomErr(errcode.RequiredFieldMissing, 
           fmt.Sprintf("Required field missing: %s", fieldName), "Validate input")
   }
   ```

### Service层

1. **业务逻辑错误**：
   ```go
   if !isValidState {
       return errcode.NewCustomErr(errcode.WorkflowStateError, 
           fmt.Sprintf("Invalid state transition: %s -> %s", currentState, targetState), 
           "State validation")
   }
   ```

2. **外部服务错误**：
   ```go
   if err != nil {
       return errcode.NewCustomErr(errcode.K8sServiceError, 
           fmt.Sprintf("K8s API call failed: %v", err), "Create container")
   }
   ```

### Controller层

Controller层通常不需要修改，因为使用了统一的 `controller.CommonExecute()` 函数处理错误。

## 向后兼容性

- 所有现有错误码保持不变
- 现有API响应格式不变
- 渐进式迁移，不影响现有功能

## 监控和告警

建议为以下错误类型设置监控：
- 高频率的数据库错误
- 认证失败率异常
- 外部服务调用失败
- 业务规则违反频率

## 总结

通过实施这套错误处理优化方案，我们可以：
- 提高系统安全性，防止敏感信息泄露
- 改善用户体验，提供清晰的错误信息
- 增强系统可维护性，统一错误处理模式
- 提升问题排查效率，通过错误关联ID快速定位问题

建议按模块逐步迁移，优先处理面向用户的API接口，然后是核心业务逻辑，最后是内部工具和管理接口。

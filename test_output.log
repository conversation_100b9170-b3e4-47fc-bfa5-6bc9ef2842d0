# 打印 相关 环境信息，若编译异常是可辅助排查
git version # 低于 2.17.1 可能不能正常工作
git version 2.39.5 (Apple Git-154)
/Users/<USER>/.local/share/mise/installs/go/1.23.0/bin/go env
GO111MODULE='on'
GOARCH='arm64'
GOBIN='/Users/<USER>/code/baidu/dataeng/mcp-online-server/../../gobin/'
GOCACHE='/Users/<USER>/Library/Caches/go-build'
GOENV='/Users/<USER>/code/baidu/dataeng/mcp-online-server/go-darwin.env'
GOEXE=''
GOEXPERIMENT=''
GOFLAGS=''
GOHOSTARCH='arm64'
GOHOSTOS='darwin'
GOINSECURE=''
GOMODCACHE='/Users/<USER>/go/pkg/mod'
GONOPROXY='*.baidu.com*,*.sensetime.com*'
GONOSUMDB='*'
GOOS='darwin'
GOPATH='/Users/<USER>/go'
GOPRIVATE='*.baidu.com,*.sensetime.com'
GOPROXY='https://goproxy.baidu-int.com'
GOROOT='/Users/<USER>/.local/share/mise/installs/go/1.23.0'
GOSUMDB='sum.golang.org'
GOTMPDIR=''
GOTOOLCHAIN='auto'
GOTOOLDIR='/Users/<USER>/.local/share/mise/installs/go/1.23.0/pkg/tool/darwin_arm64'
GOVCS=''
GOVERSION='go1.23.0'
GODEBUG=''
GOTELEMETRY='on'
GOTELEMETRYDIR='/Users/<USER>/Library/Application Support/go/telemetry'
GCCGO='gccgo'
GOARM64='v8.0'
AR='ar'
CC='clang'
CXX='clang++'
CGO_ENABLED='1'
GOMOD='/Users/<USER>/code/baidu/dataeng/mcp-online-server/go.mod'
GOWORK=''
CGO_CFLAGS='-O2 -g'
CGO_CPPFLAGS=''
CGO_CXXFLAGS='-O2 -g'
CGO_FFLAGS='-O2 -g'
CGO_LDFLAGS='-O2 -g'
PKG_CONFIG='pkg-config'
GOGCCFLAGS='-fPIC -arch arm64 -pthread -fno-caret-diagnostics -Qunused-arguments -fmessage-length=0 -ffile-prefix-map=/var/folders/nk/5hrdp2394h3891gtynzmllkw0000gn/T/go-build3006448822=/tmp/go-build -gno-record-gcc-switches -fno-common'
/Users/<USER>/.local/share/mise/installs/go/1.23.0/bin/go test -race -v -cover $(/Users/<USER>/.local/share/mise/installs/go/1.23.0/bin/go list ./...| grep -vE "vendor") -gcflags="-N -l"
	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/cmd		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/metrics		coverage: 0.0% of statements
FAIL	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/report [build failed]
	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/config		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/initialization		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/toolcall		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/benchmark/internal/session		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver/controller/api		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/library/config		coverage: 0.0% of statements
?   	icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource	[no test files]
	icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper		coverage: 0.0% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/library/mcphost		coverage: 0.0% of statements
=== RUN   TestMCPRuntimeManager_UseServerPrefix
=== RUN   TestMCPRuntimeManager_UseServerPrefix/使用服务器前缀
=== RUN   TestMCPRuntimeManager_UseServerPrefix/不使用服务器前缀
--- PASS: TestMCPRuntimeManager_UseServerPrefix (0.00s)
    --- PASS: TestMCPRuntimeManager_UseServerPrefix/使用服务器前缀 (0.00s)
    --- PASS: TestMCPRuntimeManager_UseServerPrefix/不使用服务器前缀 (0.00s)
=== RUN   TestMCPRuntimeManager_CacheInitialization
--- PASS: TestMCPRuntimeManager_CacheInitialization (0.00s)
=== RUN   TestParseFlags
--- PASS: TestParseFlags (0.00s)
=== RUN   TestDownloadServerCode
=== RUN   TestDownloadServerCode/测试文件格式检测
=== RUN   TestDownloadServerCode/测试文件格式检测/ZIP文件
=== RUN   TestDownloadServerCode/测试文件格式检测/TAR.GZ文件
=== RUN   TestDownloadServerCode/测试文件格式检测/TGZ文件
=== RUN   TestDownloadServerCode/测试文件格式检测/未知格式
=== RUN   TestDownloadServerCode/测试目录路径构建
--- PASS: TestDownloadServerCode (0.00s)
    --- PASS: TestDownloadServerCode/测试文件格式检测 (0.00s)
        --- PASS: TestDownloadServerCode/测试文件格式检测/ZIP文件 (0.00s)
        --- PASS: TestDownlo
        adServerCode/测试文件格式检测/TAR.GZ文件 (0.00s)
        --- PASS: TestDownloadServerCode/测试文件格式检测/TGZ文件 (0.00s)
        --- PASS: TestDownloadServerCode/测试文件格式检测/未知格式 (0.00s)
    --- PASS: TestDownloadServerCode/测试目录路径构建 (0.00s)
=== RUN   TestLoadMCPServersWithServerCode
--- PASS: TestLoadMCPServersWithServerCode (0.00s)
=== RUN   TestExtractFileNameFromURL
=== RUN   TestExtractFileNameFromURL/简单文件名
=== RUN   TestExtractFileNameFromURL/压缩文件
=== RUN   TestExtractFileNameFromURL/带查询参数的URL
=== RUN   TestExtractFileNameFromURL/带片段的URL
=== RUN   TestExtractFileNameFromURL/复杂URL
=== RUN   TestExtractFileNameFromURL/无文件名的URL
=== RUN   TestExtractFileNameFromURL/根路径
=== RUN   TestExtractFileNameFromURL/无扩展名文件
--- PASS: TestExtractFileNameFromURL (0.00s)
    --- PASS: TestExtractFileNameFromURL/简单文件名 (0.00s)
    --- PASS: TestExtractFileNameFromURL/压缩文件 (0.00s)
    --- PASS: TestExtractFileNameFromURL/带查询参数的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/带片段的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/复杂URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/无文件名的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/根路径 (0.00s)
    --- PASS: TestExtractFileNameFromURL/无扩展名文件 (0.00s)
=== RUN   TestIsCompressedFile
=== RUN   TestIsCompressedFile/ZIP文件
=== RUN   TestIsCompressedFile/TAR.GZ文件
=== RUN   TestIsCompressedFile/TGZ文件
=== RUN   TestIsCompressedFile/TAR文件
=== RUN   TestIsCompressedFile/GZ文件
=== RUN   TestIsCompressedFile/RAR文件
=== RUN   TestIsCompressedFile/7Z文件
=== RUN   TestIsCompressedFile/Python脚本
=== RUN   TestIsCompressedFile/Shell脚本
=== RUN   TestIsCompressedFile/二进制文件
=== RUN   TestIsCompressedFile/文本文件
=== RUN   TestIsCompressedFile/JavaScript文件
=== RUN   TestIsCompressedFile/大写扩展名
=== RUN   TestIsCompressedFile/混合大小写
--- PASS: TestIsCompressedFile (0.00s)
    --- PASS: TestIsCompressedFile/ZIP文件 (0.00s)
    --- PASS: TestIsCompressedFile/TAR.GZ文件 (0.00s)
    --- PASS: TestIsCompressedFile/TGZ文件 (0.00s)
    --- PASS: TestIsCompressedFile/TAR文件 (0.00s)
    --- PASS: TestIsCompressedFile/GZ文件 (0.00s)
    --- PASS: TestIsCompressedFile/RAR文件 (0.00s)
    --- PASS: TestIsCompressedFile/7Z文件 (0.00s)
    --- PASS: TestIsCompressedFile/Python脚本 (0.00s)
    --- PASS: TestIsCompressedFile/Shell脚本 (0.00s)
    --- PASS: TestIsCompressedFile/二进制文件 (0.00s)
    --- PASS: TestIsCompressedFile/文本文件 (0.00s)
    --- PASS: TestIsCompressedFile/JavaScript文件 (0.00s)
    --- PASS: TestIsCompressedFile/大写扩展名 (0.00s)
    --- PASS: TestIsCompressedFile/混合大小写 (0.00s)
PASS
coverage: 1.1% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/cmd	1.512s	coverage: 1.1% of statements
?   	icode.baidu.com/baidu/dataeng/mcp-online-server/library/types	[no test files]
=== RUN   TestRegisterAPI
--- PASS: TestRegisterAPI (0.00s)
PASS
coverage: 50.0% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/httpserver/module	2.068s	coverage: 50.0% of statements
=== RUN   TestNewClient
=== RUN   TestNewClient/TestNewClient
=== RUN   TestNewClient/TestNewClientWithTimeout
--- PASS: TestNewClient (0.00s)
    --- PASS: TestNewClient/TestNewClient (0.00s)
    --- PASS: TestNewClient/TestNewClientWithTimeout (0.00s)
=== RUN   TestAPIError
=== RUN   TestAPIError/TestAPIErrorCreation
=== RUN   TestAPIError/TestHTTPErrorCreation
=== RUN   TestAPIError/TestIsAPIError
--- PASS: TestAPIError (0.00s)
    --- PASS: TestAPIError/TestAPIErrorCreation (0.00s)
    --- PASS: TestAPIError/TestHTTPErrorCreation (0.00s)
    --- PASS: TestAPIError/TestIsAPIError (0.00s)
=== RUN   TestRequestTypes
=== RUN   TestRequestTypes/TestSessionReadyRequest
=== RUN   TestRequestTypes/TestSessionStopRequest
=== RUN   TestRequestTypes/TestClaimTaskRequest
=== RUN   TestRequestTypes/TestCompleteTaskRequest
--- PASS: TestRequestTypes (0.00s)
    --- PASS: TestRequestTypes/TestSessionReadyRequest (0.00s)
    --- PASS: TestRequestTypes/TestSessionStopRequest (0.00s)
    --- PASS: TestRequestTypes/TestClaimTaskRequest (0.00s)
    --- PASS: TestRequestTypes/TestCompleteTaskRequest (0.00s)
=== RUN   TestResponseTypes
=== RUN   TestResponseTypes/TestStandardAPIResponse
=== RUN   TestResponseTypes/TestClaimTaskResponseData
=== RUN   TestResponseTypes/TestCompleteTaskResponseData
--- PASS: TestResponseTypes (0.00s)
    --- PASS: TestResponseTypes/TestStandardAPIResponse (0.00s)
    --- PASS: TestResponseTypes/TestClaimTaskResponseData (0.00s)
    --- PASS: TestResponseTypes/TestCompleteTaskResponseData (0.00s)
PASS
coverage: 9.8% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/library/apiclient	1.547s	coverage: 9.8% of statements
=== RUN   TestNewCustomErr
=== RUN   TestNewCustomErr/数据库插入错误
=== RUN   TestNewCustomErr/数据库查询错误
=== RUN   TestNewCustomErr/文件读取错误
=== RUN   TestNewCustomErr/无原始错误，仅错误码
=== RUN   TestNewCustomErr/未定义错误码使用详细信息
=== RUN   TestNewCustomErr/CustomErr透传测试
=== RUN   TestNewCustomErr/认证令牌无效错误
=== RUN   TestNewCustomErr/K8s限流错误
--- PASS: TestNewCustomErr (0.00s)
    --- PASS: TestNewCustomErr/数据库插入错误 (0.00s)
    --- PASS: TestNewCustomErr/数据库查询错误 (0.00s)
    --- PASS: TestNewCustomErr/文件读取错误 (0.00s)
    --- PASS: TestNewCustomErr/无原始错误，仅错误码 (0.00s)
    --- PASS: TestNewCustomErr/未定义错误码使用详细信息 (0.00s)
    --- PASS: TestNewCustomErr/CustomErr透传测试 (0.00s)
    --- PASS: TestNewCustomErr/认证令牌无效错误 (0.00s)
    --- PASS: TestNewCustomErr/K8s限流错误 (0.00s)
=== RUN   TestNewCustomErr_ErrorMessageMapping
=== RUN   TestNewCustomErr_ErrorMessageMapping/SQL插入错误
=== RUN   TestNewCustomErr_ErrorMessageMapping/SQL更新错误
=== RUN   TestNewCustomErr_ErrorMessageMapping/SQL删除错误
=== RUN   TestNewCustomErr_ErrorMessageMapping/SQL连接错误
=== RUN   TestNewCustomErr_ErrorMessageMapping/文件读取错误
=== RUN   TestNewCustomErr_ErrorMessageMapping/文件创建错误
=== RUN   TestNewCustomErr_ErrorMessageMapping/目录创建错误
--- PASS: TestNewCustomErr_ErrorMessageMapping (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/SQL插入错误 (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/SQL更新错误 (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/SQL删除错误 (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/SQL连接错误 (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/文件读取错误 (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/文件创建错误 (0.00s)
    --- PASS: TestNewCustomErr_ErrorMessageMapping/目录创建错误 (0.00s)
=== RUN   TestNewCustomErr_FilePathLogging
--- PASS: TestNewCustomErr_FilePathLogging (0.00s)
PASS
coverage: 71.1% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode	2.402s	coverage: 71.1% of statements
=== RUN   TestNewRewardCalculator
--- PASS: TestNewRewardCalculator (0.00s)
PASS
coverage: 2.1% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/library/reward_calculator	1.337s	coverage: 2.1% of statements
=== RUN   TestEnvManagerPackaging
2025/08/01 14:51:19 INFO 开始打包环境 working_dir=./test_env_20250801145119 tar_file=./test_env_20250801145119/test_env.tar.gz
2025/08/01 14:51:19 INFO 执行tar命令 args="[-cf /var/folders/nk/5hrdp2394h3891gtynzmllkw0000gn/T/env_package_1246444949/env_temp.tar --owner=root --group=root --numeric-owner --exclude=mcp_runtime --exclude=mcp_runtime.go -C ./test_env_20250801145119 .]" working_dir=./test_env_20250801145119
2025/08/01 14:51:19 INFO tar命令执行成功 output_length=0
2025/08/01 14:51:19 INFO 开始压缩tar文件 input=/var/folders/nk/5hrdp2394h3891gtynzmllkw0000gn/T/env_package_1246444949/env_temp.tar
2025/08/01 14:51:19 INFO 环境打包完成 output=""
    environment_manager_test.go:66: 环境打包测试成功
--- PASS: TestEnvManagerPackaging (0.03s)
=== RUN   TestSafeRemoveFile
=== RUN   TestSafeRemoveFile/删除存在的文件
=== RUN   TestSafeRemoveFile/删除不存在的文件
=== RUN   TestSafeRemoveFile/删除只读文件
--- PASS: TestSafeRemoveFile (0.00s)
    --- PASS: TestSafeRemoveFile/删除存在的文件 (0.00s)
    --- PASS: TestSafeRemoveFile/删除不存在的文件 (0.00s)
    --- PASS: TestSafeRemoveFile/删除只读文件 (0.00s)
=== RUN   TestSafeRemoveFileWithPermissionError
2025/08/01 14:51:19 WARN 删除临时文件失败 file=/var/folders/nk/5hrdp2394h3891gtynzmllkw0000gn/T/test_permission_807325047/test_file.txt error="remove /var/folders/nk/5hrdp2394h3891gtynzmllkw0000gn/T/test_permission_807325047/test_file.txt: permission denied"
--- PASS: TestSafeRemoveFileWithPermissionError (0.00s)
=== RUN   TestExtractFileNameFromURL
=== RUN   TestExtractFileNameFromURL/简单文件名
=== RUN   TestExtractFileNameFromURL/压缩文件
=== RUN   TestExtractFileNameFromURL/带查询参数的URL
=== RUN   TestExtractFileNameFromURL/带片段的URL
=== RUN   TestExtractFileNameFromURL/复杂URL
=== RUN   TestExtractFileNameFromURL/无文件名的URL
=== RUN   TestExtractFileNameFromURL/根路径
=== RUN   TestExtractFileNameFromURL/无扩展名文件
=== RUN   TestExtractFileNameFromURL/带端口的URL
=== RUN   TestExtractFileNameFromURL/本地文件路径
--- PASS: TestExtractFileNameFromURL (0.00s)
    --- PASS: TestExtractFileNameFromURL/简单文件名 (0.00s)
    --- PASS: TestExtractFileNameFromURL/压缩文件 (0.00s)
    --- PASS: TestExtractFileNameFromURL/带查询参数的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/带片段的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/复杂URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/无文件名的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/根路径 (0.00s)
    --- PASS: TestExtractFileNameFromURL/无扩展名文件 (0.00s)
    --- PASS: TestExtractFileNameFromURL/带端口的URL (0.00s)
    --- PASS: TestExtractFileNameFromURL/本地文件路径 (0.00s)
=== RUN   TestIsCompressedFile
=== RUN   TestIsCompressedFile/ZIP文件
=== RUN   TestIsCompressedFile/TAR.GZ文件
=== RUN   TestIsCompressedFile/TGZ文件
=== RUN   TestIsCompressedFile/TAR文件
=== RUN   TestIsCompressedFile/GZ文件
=== RUN   TestIsCompressedFile/RAR文件
=== RUN   TestIsCompressedFile/7Z文件
=== RUN   TestIsCompressedFile/Python脚本
=== RUN   TestIsCompressedFile/Shell脚本
=== RUN   TestIsCompressedFile/二进制文件
=== RUN   TestIsCompressedFile/文本文件
=== RUN   TestIsCompressedFile/JavaScript文件
=== RUN   TestIsCompressedFile/大写扩展名
=== RUN   TestIsCompressedFile/混合大小写
=== RUN   TestIsCompressedFile/空文件名
=== RUN   TestIsCompressedFile/只有扩展名
--- PASS: TestIsCompressedFile (0.00s)
    --- PASS: TestIsCompressedFile/ZIP文件 (0.00s)
    --- PASS: TestIsCompressedFile/TAR.GZ文件 (0.00s)
    --- PASS: TestIsCompressedFile/TGZ文件 (0.00s)
    --- PASS: TestIsCompressedFile/TAR文件 (0.00s)
    --- PASS: TestIsCompressedFile/GZ文件 (0.00s)
    --- PASS: TestIsCompressedFile/RAR文件 (0.00s)
    --- PASS: TestIsCompressedFile/7Z文件 (0.00s)
    --- PASS: TestIsCompressedFile/Python脚本 (0.00s)
    --- PASS: TestIsCompressedFile/Shell脚本 (0.00s)
    --- PASS: TestIsCompressedFile/二进制文件 (0.00s)
    --- PASS: TestIsCompressedFile/文本文件 (0.00s)
    --- PASS: TestIsCompressedFile/JavaScript文件 (0.00s)
    --- PASS: TestIsCompressedFile/大写扩展名 (0.00s)
    --- PASS: TestIsCompressedFile/混合大小写 (0.00s)
    --- PASS: TestIsCompressedFile/空文件名 (0.00s)
    --- PASS: TestIsCompressedFile/只有扩展名 (0.00s)
=== RUN   TestConvertMCPToolsToJSONData
--- PASS: TestConvertMCPToolsToJSONData (0.00s)
=== RUN   TestConvertJSONDataToMCPTools
--- PASS: TestConvertJSONDataToMCPTools (0.00s)
=== RUN   TestConvertRoundTrip
--- PASS: TestConvertRoundTrip (0.00s)
=== RUN   TestValidateMCPTool
--- PASS: TestValidateMCPTool (0.00s)
=== RUN   TestValidateMCPTools
--- PASS: TestValidateMCPTools (0.00s)
PASS
coverage: 32.7% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils	(cached)	coverage: 32.7% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao		coverage: 0.0% of statements
=== RUN   TestTaskPollingService_BasicFunctionality
--- PASS: TestTaskPollingService_BasicFunctionality (0.64s)
=== RUN   TestTaskPollingService_WaitForTasks_Timeout
--- PASS: TestTaskPollingService_WaitForTasks_Timeout (0.20s)
=== RUN   TestTaskPollingService_ConcurrentRequests
--- PASS: TestTaskPollingService_ConcurrentRequests (0.10s)
=== RUN   TestTaskPollingService_GlobalInstance
--- PASS: TestTaskPollingService_GlobalInstance (0.00s)
PASS
coverage: 30.5% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/background	2.487s	coverage: 30.5% of statements
=== RUN   TestMcpEnvLifecycle
=== RUN   TestMcpEnvLifecycle/TestCRUD
=== RUN   TestMcpEnvLifecycle/TestInsertInTx
=== RUN   TestMcpEnvLifecycle/TestCheckEnvMd5Exist
=== RUN   TestMcpEnvLifecycle/TestUpdateInTx
--- PASS: TestMcpEnvLifecycle (0.65s)
    --- PASS: TestMcpEnvLifecycle/TestCRUD (0.17s)
    --- PASS: TestMcpEnvLifecycle/TestInsertInTx (0.02s)
    --- PASS: TestMcpEnvLifecycle/TestCheckEnvMd5Exist (0.05s)
    --- PASS: TestMcpEnvLifecycle/TestUpdateInTx (0.02s)
=== RUN   TestMcpEnvGetMapByPrimaryKeys
--- PASS: TestMcpEnvGetMapByPrimaryKeys (0.02s)
=== RUN   TestMcpEnvQueries
=== RUN   TestMcpEnvQueries/TestSelectAllActive
=== RUN   TestMcpEnvQueries/TestSelectByPage
=== RUN   TestMcpEnvQueries/TestDeleteInTx
--- PASS: TestMcpEnvQueries (0.06s)
    --- PASS: TestMcpEnvQueries/TestSelectAllActive (0.02s)
    --- PASS: TestMcpEnvQueries/TestSelectByPage (0.03s)
    --- PASS: TestMcpEnvQueries/TestDeleteInTx (0.01s)
PASS
coverage: 80.5% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env	2.346s	coverage: 80.5% of statements
=== RUN   TestObjImageLifecycle
=== RUN   TestObjImageLifecycle/TestCRUD
=== RUN   TestObjImageLifecycle/TestBatchQuery
=== RUN   TestObjImageLifecycle/TestPagination
=== RUN   TestObjImageLifecycle/TestCustomImageID
--- PASS: TestObjImageLifecycle (0.49s)
    --- PASS: TestObjImageLifecycle/TestCRUD (0.06s)
    --- PASS: TestObjImageLifecycle/TestBatchQuery (0.01s)
    --- PASS: TestObjImageLifecycle/TestPagination (0.02s)
    --- PASS: TestObjImageLifecycle/TestCustomImageID (0.01s)
PASS
coverage: 60.9% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/obj_image	2.028s	coverage: 60.9% of statements
=== RUN   TestRegisterMcpServerLifecycle
=== RUN   TestRegisterMcpServerLifecycle/TestCRUD
=== RUN   TestRegisterMcpServerLifecycle/TestInsertInTx
=== RUN   TestRegisterMcpServerLifecycle/TestUpdateInTx
--- PASS: TestRegisterMcpServerLifecycle (0.42s)
    --- PASS: TestRegisterMcpServerLifecycle/TestCRUD (0.04s)
    --- PASS: TestRegisterMcpServerLifecycle/TestInsertInTx (0.01s)
    --- PASS: TestRegisterMcpServerLifecycle/TestUpdateInTx (0.01s)
=== RUN   TestGetMapByPrimaryKeys
--- PASS: TestGetMapByPrimaryKeys (0.03s)
=== RUN   TestRegisterMcpServerQueries
=== RUN   TestRegisterMcpServerQueries/TestSelectAllActive
=== RUN   TestRegisterMcpServerQueries/TestSelectByPage
=== RUN   TestRegisterMcpServerQueries/TestDeleteInTx
--- PASS: TestRegisterMcpServerQueries (0.04s)
    --- PASS: TestRegisterMcpServerQueries/TestSelectAllActive (0.01s)
    --- PASS: TestRegisterMcpServerQueries/TestSelectByPage (0.02s)
    --- PASS: TestRegisterMcpServerQueries/TestDeleteInTx (0.01s)
PASS
coverage: 82.5% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server	2.008s	coverage: 82.5% of statements
=== RUN   TestUploadFile
    pkg_test.go:61: fileName: test_upload_file.txt, permanentURL: https://mcp-env.bj.bcebos.com/tmp/test_upload_file.txt?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-01T09%3A00%3A11Z%2F-1%2Fhost%2Fe0feeac4fa113139fba8108a0dec913e5f647f2650a4b069eabafced0f5fba4c
--- PASS: TestUploadFile (0.80s)
=== RUN   TestBosBasicOperations
=== RUN   TestBosBasicOperations/TestUploadAndDownloadString
=== RUN   TestBosBasicOperations/TestUploadStringList
=== RUN   TestBosBasicOperations/TestGenerateVisitURL
    pkg_test.go:118: 生成的BOS访问URL: https://mcp-env.bj.bcebos.com/mcp_online_server/test/sample_object.txt?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-01T09%3A00%3A11Z%2F3600%2Fhost%2F7c76da43d017205147661705c0929e0d0dc9fff4320e1155f6650450f202e7ca
--- PASS: TestBosBasicOperations (0.21s)
    --- PASS: TestBosBasicOperations/TestUploadAndDownloadString (0.11s)
    --- PASS: TestBosBasicOperations/TestUploadStringList (0.10s)
    --- PASS: TestBosBasicOperations/TestGenerateVisitURL (0.00s)
=== RUN   TestBosBusinessOperations
=== RUN   TestBosBusinessOperations/TestPackageDirectoryToBos
    pkg_test.go:175: 目录打包结果: MD5=945e0dc4163686f5c13660dbf4f68249, Size=356, URL=https://mcp-env.bj.bcebos.com/mcp_env/945e0dc4163686f5c13660dbf4f68249.tar.gz?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-01T09%3A00%3A11Z%2F-1%2Fhost%2Fc9843c9cc37f1a70d57c2f5fd225cad63faff8b9b73dbb0848f7dc7f5735bf01
    pkg_test.go:192: MD5去重测试通过: 第二次打包复用了现有记录
=== RUN   TestBosBusinessOperations/TestCheckEnvExistsByMD5
=== RUN   TestBosBusinessOperations/TestDownloadEnvFromBos
    pkg_test.go:259: 下载环境失败（预期，因为可能没有真实的BOS文件）: [Code: NoSuchKey; Message: The specified key does not exist.; RequestId: 87f70501-0cf7-495f-b7ba-cca96e13217f]
--- PASS: TestBosBusinessOperations (0.39s)
    --- PASS: TestBosBusinessOperations/TestPackageDirectoryToBos (0.16s)
    --- PASS: TestBosBusinessOperations/TestCheckEnvExistsByMD5 (0.02s)
    --- PASS: TestBosBusinessOperations/TestDownloadEnvFromBos (0.21s)
=== RUN   TestBosFileOperations
=== RUN   TestBosFileOperations/TestGetFileSizeOperations
--- PASS: TestBosFileOperations (0.15s)
    --- PASS: TestBosFileOperations/TestGetFileSizeOperations (0.15s)
=== RUN   TestBosErrorHandling
=== RUN   TestBosErrorHandling/TestNonExistentOperations
=== RUN   TestBosErrorHandling/TestInvalidDirectoryPackaging
--- PASS: TestBosErrorHandling (0.12s)
    --- PASS: TestBosErrorHandling/TestNonExistentOperations (0.12s)
    --- PASS: TestBosErrorHandling/TestInvalidDirectoryPackaging (0.00s)
PASS
coverage: 61.9% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos	3.132s	coverage: 61.9% of statements
=== RUN   TestCreateTask_Success
--- PASS: TestCreateTask_Success (0.39s)
=== RUN   TestCreateTask_RALError
--- PASS: TestCreateTask_RALError (0.00s)
=== RUN   TestCreateTask_InvalidInput
--- PASS: TestCreateTask_InvalidInput (0.00s)
=== RUN   TestCreateTask_MarshalError
--- PASS: TestCreateTask_MarshalError (0.00s)
=== RUN   TestGetTaskState_Success
--- PASS: TestGetTaskState_Success (0.00s)
=== RUN   TestGetTaskState_TaskNotFound
--- PASS: TestGetTaskState_TaskNotFound (0.00s)
=== RUN   TestGetTaskLogStream_Success
--- PASS: TestGetTaskLogStream_Success (0.00s)
=== RUN   TestDeleteJob_Success
--- PASS: TestDeleteJob_Success (0.00s)
=== RUN   TestGetPodCount_Success
--- PASS: TestGetPodCount_Success (0.00s)
=== RUN   TestCreateTask_TableDriven
=== RUN   TestCreateTask_TableDriven/Valid_MCP_Task
=== RUN   TestCreateTask_TableDriven/Valid_Dataeng_Task
=== RUN   TestCreateTask_TableDriven/Resource_Limit_Error
=== RUN   TestCreateTask_TableDriven/Image_Not_Found
=== RUN   TestCreateTask_TableDriven/Network_Error
--- PASS: TestCreateTask_TableDriven (0.00s)
    --- PASS: TestCreateTask_TableDriven/Valid_MCP_Task (0.00s)
    --- PASS: TestCreateTask_TableDriven/Valid_Dataeng_Task (0.00s)
    --- PASS: TestCreateTask_TableDriven/Resource_Limit_Error (0.00s)
    --- PASS: TestCreateTask_TableDriven/Image_Not_Found (0.00s)
    --- PASS: TestCreateTask_TableDriven/Network_Error (0.00s)
=== RUN   TestCreateTask_BoundaryConditions
=== RUN   TestCreateTask_BoundaryConditions/Empty_SessionID
=== RUN   TestCreateTask_BoundaryConditions/Very_Large_SessionID
=== RUN   TestCreateTask_BoundaryConditions/Empty_ImagePath
=== RUN   TestCreateTask_BoundaryConditions/Empty_Command
=== RUN   TestCreateTask_BoundaryConditions/Zero_Timeout
=== RUN   TestCreateTask_BoundaryConditions/Negative_Timeout
=== RUN   TestCreateTask_BoundaryConditions/Very_Large_Timeout
--- PASS: TestCreateTask_BoundaryConditions (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Empty_SessionID (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Very_Large_SessionID (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Empty_ImagePath (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Empty_Command (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Zero_Timeout (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Negative_Timeout (0.00s)
    --- PASS: TestCreateTask_BoundaryConditions/Very_Large_Timeout (0.00s)
=== RUN   TestK8sProxyClient_IntegrationWorkflow
=== RUN   TestK8sProxyClient_IntegrationWorkflow/Complete_Workflow
--- PASS: TestK8sProxyClient_IntegrationWorkflow (0.00s)
    --- PASS: TestK8sProxyClient_IntegrationWorkflow/Complete_Workflow (0.00s)
PASS
coverage: 52.5% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy	2.145s	coverage: 52.5% of statements
=== RUN   TestRewardDataConversion
=== RUN   TestRewardDataConversion/TestRewardDataToJSONData
=== RUN   TestRewardDataConversion/TestRewardDataToJSONDataWithEmptyValues
--- PASS: TestRewardDataConversion (0.00s)
    --- PASS: TestRewardDataConversion/TestRewardDataToJSONData (0.00s)
    --- PASS: TestRewardDataConversion/TestRewardDataToJSONDataWithEmptyValues (0.00s)
=== RUN   TestSessionLifecycle
=== RUN   TestSessionLifecycle/TestCRUD
=== RUN   TestSessionLifecycle/TestInsertInTx
--- PASS: TestSessionLifecycle (0.71s)
    --- PASS: TestSessionLifecycle/TestCRUD (0.08s)
    --- PASS: TestSessionLifecycle/TestInsertInTx (0.01s)
=== RUN   TestSessionQueries
=== RUN   TestSessionQueries/TestGetMapByPrimaryKeys
=== RUN   TestSessionQueries/TestUpdate
=== RUN   TestSessionQueries/TestUpdateReward
=== RUN   TestSessionQueries/TestUpdateRewardWithError
=== RUN   TestSessionQueries/TestSelectTimeoutSessions
=== RUN   TestSessionQueries/TestSelectByStatus
=== RUN   TestSessionQueries/TestSelectActiveSessions
=== RUN   TestSessionQueries/TestSelectByPage
--- PASS: TestSessionQueries (0.15s)
    --- PASS: TestSessionQueries/TestGetMapByPrimaryKeys (0.01s)
    --- PASS: TestSessionQueries/TestUpdate (0.01s)
    --- PASS: TestSessionQueries/TestUpdateReward (0.02s)
    --- PASS: TestSessionQueries/TestUpdateRewardWithError (0.02s)
    --- PASS: TestSessionQueries/TestSelectTimeoutSessions (0.04s)
    --- PASS: TestSessionQueries/TestSelectByStatus (0.02s)
    --- PASS: TestSessionQueries/TestSelectActiveSessions (0.01s)
    --- PASS: TestSessionQueries/TestSelectByPage (0.02s)
=== RUN   TestSessionErrorHandling
=== RUN   TestSessionErrorHandling/TestUpdateStatusWithError
--- PASS: TestSessionErrorHandling (0.01s)
    --- PASS: TestSessionErrorHandling/TestUpdateStatusWithError (0.01s)
PASS
coverage: 48.7% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session	2.652s	coverage: 48.7% of statements
	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/env		coverage: 0.0% of statements
=== RUN   TestToolCallTaskLifecycle
=== RUN   TestToolCallTaskLifecycle/TestCRUD
=== RUN   TestToolCallTaskLifecycle/TestInsertInTx
--- PASS: TestToolCallTaskLifecycle (0.69s)
    --- PASS: TestToolCallTaskLifecycle/TestCRUD (0.05s)
    --- PASS: TestToolCallTaskLifecycle/TestInsertInTx (0.02s)
=== RUN   TestToolCallTaskQueries
=== RUN   TestToolCallTaskQueries/TestGetMapByPrimaryKeys
=== RUN   TestToolCallTaskQueries/TestUpdate
=== RUN   TestToolCallTaskQueries/TestUpdateResult
=== RUN   TestToolCallTaskQueries/TestDeleteInTx
=== RUN   TestToolCallTaskQueries/TestSelectByToolName
=== RUN   TestToolCallTaskQueries/TestSelectBySessionID
=== RUN   TestToolCallTaskQueries/TestSelectByStatus
=== RUN   TestToolCallTaskQueries/TestSelectByPage
=== RUN   TestToolCallTaskQueries/TestSelectPendingTasks
=== RUN   TestToolCallTaskQueries/TestSelectTaskStatistics
--- PASS: TestToolCallTaskQueries (0.15s)
    --- PASS: TestToolCallTaskQueries/TestGetMapByPrimaryKeys (0.01s)
    --- PASS: TestToolCallTaskQueries/TestUpdate (0.01s)
    --- PASS: TestToolCallTaskQueries/TestUpdateResult (0.01s)
    --- PASS: TestToolCallTaskQueries/TestDeleteInTx (0.01s)
    --- PASS: TestToolCallTaskQueries/TestSelectByToolName (0.02s)
    --- PASS: TestToolCallTaskQueries/TestSelectBySessionID (0.03s)
    --- PASS: TestToolCallTaskQueries/TestSelectByStatus (0.01s)
    --- PASS: TestToolCallTaskQueries/TestSelectByPage (0.01s)
    --- PASS: TestToolCallTaskQueries/TestSelectPendingTasks (0.01s)
    --- PASS: TestToolCallTaskQueries/TestSelectTaskStatistics (0.02s)
PASS
coverage: 65.8% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task	2.513s	coverage: 65.8% of statements
FAIL	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/image [build failed]
=== RUN   TestBinaryStrategy_CanHandle
=== RUN   TestBinaryStrategy_CanHandle/binary
=== RUN   TestBinaryStrategy_CanHandle/Binary
=== RUN   TestBinaryStrategy_CanHandle/file
=== RUN   TestBinaryStrategy_CanHandle/directory
=== RUN   TestBinaryStrategy_CanHandle/db
=== RUN   TestBinaryStrategy_CanHandle/url
=== RUN   TestBinaryStrategy_CanHandle/#00
--- PASS: TestBinaryStrategy_CanHandle (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/binary (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/Binary (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/file (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/directory (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/db (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/url (0.00s)
    --- PASS: TestBinaryStrategy_CanHandle/#00 (0.00s)
=== RUN   TestBinaryStrategy_GetName
--- PASS: TestBinaryStrategy_GetName (0.00s)
=== RUN   TestBinaryStrategy_Initialize_EmptyContent
--- PASS: TestBinaryStrategy_Initialize_EmptyContent (0.00s)
=== RUN   TestBinaryStrategy_Initialize_InvalidBase64
--- PASS: TestBinaryStrategy_Initialize_InvalidBase64 (0.00s)
=== RUN   TestBinaryStrategy_Initialize_Success
--- PASS: TestBinaryStrategy_Initialize_Success (0.00s)
=== RUN   TestBinaryStrategy_Initialize_ExistingFile
--- PASS: TestBinaryStrategy_Initialize_ExistingFile (0.00s)
=== RUN   TestBinaryStrategy_Initialize_CreateDirectory
--- PASS: TestBinaryStrategy_Initialize_CreateDirectory (0.00s)
=== RUN   TestDirectoryStrategy
--- PASS: TestDirectoryStrategy (0.00s)
=== RUN   TestFileStrategy
--- PASS: TestFileStrategy (0.00s)
=== RUN   TestDatabaseStrategy
--- PASS: TestDatabaseStrategy (0.02s)
=== RUN   TestManager
    environment_test.go:152: 应该注册3个策略, 实际: 5
2025/08/01 17:00:14 INFO 开始初始化环境依赖 count=3 workDir=""
2025/08/01 17:00:14 INFO 处理环境依赖 index=1 type=directory path=./test_manager_20250801170014 workDir=""
2025/08/01 17:00:14 INFO 环境依赖初始化成功 type=directory path=./test_manager_20250801170014 strategy=DirectoryStrategy duration_ms=0
2025/08/01 17:00:14 INFO 处理环境依赖 index=2 type=file path=./test_manager_20250801170014/test.txt workDir=""
2025/08/01 17:00:14 INFO 环境依赖初始化成功 type=file path=./test_manager_20250801170014/test.txt strategy=FileStrategy duration_ms=0
2025/08/01 17:00:14 INFO 处理环境依赖 index=3 type=db path=./test_manager_20250801170014/test.db workDir=""
2025/08/01 17:00:14 INFO 环境依赖初始化成功 type=db path=./test_manager_20250801170014/test.db strategy=DatabaseStrategy duration_ms=3
2025/08/01 17:00:14 INFO 环境依赖初始化完成 total=3 success=3 failed=0
--- FAIL: TestManager (0.01s)
=== RUN   TestParseSQLStatements
--- PASS: TestParseSQLStatements (0.00s)
=== RUN   TestURLStrategyIntegration
2025/08/01 17:00:14 INFO 开始初始化环境依赖 count=1 workDir=""
2025/08/01 17:00:14 INFO 处理环境依赖 index=1 type=url path=./test.txt workDir=""
2025/08/01 17:00:19 ERRO 环境依赖初始化失败 type=url path=./test.txt error="下载失败，HTTP状态码: 404" duration_ms=4732
2025/08/01 17:00:19 INFO 环境依赖初始化完成 total=1 success=0 failed=1
    integration_test.go:62: ✅ URL策略集成测试通过
    integration_test.go:63:    - 策略已注册: [url]
    integration_test.go:64:    - 错误信息: 下载失败，HTTP状态码: 404
--- PASS: TestURLStrategyIntegration (4.73s)
=== RUN   TestURLStrategyRealDownload
2025/08/01 17:00:19 INFO 开始初始化环境依赖 count=1 workDir=""
2025/08/01 17:00:19 INFO 处理环境依赖 index=1 type=url path=./productivity_mock_data/task_1/第一季度财务报表.xlsx workDir=""
2025/08/01 17:00:19 INFO 环境依赖初始化成功 type=url path=./productivity_mock_data/task_1/第一季度财务报表.xlsx strategy=URLStrategy duration_ms=177
2025/08/01 17:00:19 INFO 环境依赖初始化完成 total=1 success=1 failed=0
    integration_test.go:121: ✅ 真实URL下载测试通过
    integration_test.go:122:    - 文件路径: ./productivity_mock_data/task_1/第一季度财务报表.xlsx
    integration_test.go:123:    - 文件大小: 5036 bytes
    integration_test.go:124:    - 下载耗时: 177.292ms
    integration_test.go:125:    - 详情: 成功下载文件: ./productivity_mock_data/task_1/第一季度财务报表.xlsx, 大小: 5036 bytes, 内容类型: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, URL: https://office-mcp-dependency.bj.bcebos.com/productivity_mock_data/task_1/%E7%AC%AC%E4%B8%80%E5%AD%A3%E5%BA%A6%E8%B4%A2%E5%8A%A1%E6%8A%A5%E8%A1%A8.xlsx
--- PASS: TestURLStrategyRealDownload (0.18s)
=== RUN   TestURLStrategy_CanHandle
=== RUN   TestURLStrategy_CanHandle/url
=== RUN   TestURLStrategy_CanHandle/URL
=== RUN   TestURLStrategy_CanHandle/file
=== RUN   TestURLStrategy_CanHandle/directory
=== RUN   TestURLStrategy_CanHandle/db
=== RUN   TestURLStrategy_CanHandle/#00
--- PASS: TestURLStrategy_CanHandle (0.00s)
    --- PASS: TestURLStrategy_CanHandle/url (0.00s)
    --- PASS: TestURLStrategy_CanHandle/URL (0.00s)
    --- PASS: TestURLStrategy_CanHandle/file (0.00s)
    --- PASS: TestURLStrategy_CanHandle/directory (0.00s)
    --- PASS: TestURLStrategy_CanHandle/db (0.00s)
    --- PASS: TestURLStrategy_CanHandle/#00 (0.00s)
=== RUN   TestURLStrategy_GetName
--- PASS: TestURLStrategy_GetName (0.00s)
=== RUN   TestURLStrategy_Initialize_EmptyContent
--- PASS: TestURLStrategy_Initialize_EmptyContent (0.00s)
=== RUN   TestURLStrategy_Initialize_InvalidURL
--- PASS: TestURLStrategy_Initialize_InvalidURL (0.00s)
=== RUN   ExampleDefaultManager
--- FAIL: ExampleDefaultManager (0.01s)
got:
INFO [ENV-EXAMPLE] : 开始初始化环境依赖 count=4 workDir=""
INFO [ENV-EXAMPLE] : 处理环境依赖 index=1 type=directory path=./example_data workDir=""
INFO [ENV-EXAMPLE] : 环境依赖初始化成功 type=directory path=./example_data strategy=DirectoryStrategy duration_ms=0
INFO [ENV-EXAMPLE] : 处理环境依赖 index=2 type=file path=./example_data/config.txt workDir=""
INFO [ENV-EXAMPLE] : 环境依赖初始化成功 type=file path=./example_data/config.txt strategy=FileStrategy duration_ms=0
INFO [ENV-EXAMPLE] : 处理环境依赖 index=3 type=db path=./example_data/app.db workDir=""
INFO [ENV-EXAMPLE] : 环境依赖初始化成功 type=db path=./example_data/app.db strategy=DatabaseStrategy duration_ms=6
INFO [ENV-EXAMPLE] : 处理环境依赖 index=4 type=binary path=./example_data/logo.png workDir=""
INFO [ENV-EXAMPLE] : 环境依赖初始化成功 type=binary path=./example_data/logo.png strategy=BinaryStrategy duration_ms=0
INFO [ENV-EXAMPLE] : 环境依赖初始化完成 total=4 success=4 failed=0

=== 环境初始化结果 ===
✅ [1] ./example_data (directory)
   耗时: 59.542µs
   详情: 成功创建目录: ./example_data, 权限: 0755

✅ [2] ./example_data/config.txt (file)
   耗时: 81.25µs
   详情: 成功创建文件: ./example_data/config.txt, 大小: 36 bytes, 权限: 0644

✅ [3] ./example_data/app.db (db)
   耗时: 6.42875ms
   详情: 成功执行 4/4 条SQL语句:
语句 1: 成功 (1.88475ms) - 影响行数: 0
语句 2: 成功 (1.448667ms) - 影响行数: 2
语句 3: 成功 (1.212375ms) - 影响行数: 2
语句 4: 成功 (603.625µs) - 影响行数: 2

✅ [4] ./example_data/logo.png (binary)
   耗时: 91.459µs
   详情: 成功创建二进制文件: ./example_data/logo.png, 解码后大小: 70 bytes, Base64原始大小: 96 bytes, 权限: 0644

=== 验证结果 ===
✅ 目录创建成功
✅ 文件创建成功，大小: 36 bytes
✅ 数据库创建成功
示例文件已清理
want:
=== 环境初始化结果 ===
✅ [1] ./example_data (directory)
✅ [2] ./example_data/config.txt (file)
✅ [3] ./example_data/logo.png (binary)
✅ [4] ./example_data/app.db (db)
FAIL
coverage: 69.6% of statements
FAIL	icode.baidu.com/baidu/dataeng/mcp-online-server/model/environment	5.806s
=== RUN   TestSessionMetricsRegistration
--- PASS: TestSessionMetricsRegistration (0.00s)
=== RUN   TestToolCallMetricsRegistration
--- PASS: TestToolCallMetricsRegistration (0.00s)
=== RUN   TestSessionStatusMetricsUpdate
--- PASS: TestSessionStatusMetricsUpdate (0.00s)
=== RUN   TestToolCallStatusMetricsUpdate
--- PASS: TestToolCallStatusMetricsUpdate (0.00s)
=== RUN   TestSessionTransitionRecording
--- PASS: TestSessionTransitionRecording (0.00s)
=== RUN   TestToolCallTransitionRecording
--- PASS: TestToolCallTransitionRecording (0.00s)
=== RUN   TestInstrumentationHelpers
--- PASS: TestInstrumentationHelpers (0.00s)
PASS
coverage: 26.9% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/metrics	2.345s	coverage: 26.9% of statements
=== RUN   TestCalculateReward_Execute_Success
--- PASS: TestCalculateReward_Execute_Success (0.79s)
=== RUN   TestCalculateReward_Execute_WithSessionCode
    calculate_reward_test.go:242: 
        	Error Trace:	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/calcreward/calculate_reward_test.go:242
        	Error:      	Received unexpected error:
        	            	Session状态为stopped，不支持计算奖励
        	Test:       	TestCalculateReward_Execute_WithSessionCode
    calculate_reward_test.go:243: 
        	Error Trace:	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/calcreward/calculate_reward_test.go:243
        	Error:      	Expected value not to be nil.
        	Test:       	TestCalculateReward_Execute_WithSessionCode
    calculate_reward_test.go:246: 
        	Error Trace:	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/calcreward/calculate_reward_test.go:246
        	Error:      	Should be true
        	Test:       	TestCalculateReward_Execute_WithSessionCode
--- FAIL: TestCalculateReward_Execute_WithSessionCode (0.00s)
panic: runtime error: invalid memory address or nil pointer dereference [recovered]
	panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x2 addr=0x0 pc=0x1065f0384]

goroutine 124 [running]:
testing.tRunner.func1.2({0x106a7f080, 0x107c30e30})
	/Users/<USER>/.local/share/mise/installs/go/1.23.0/src/testing/testing.go:1632 +0x2c4
testing.tRunner.func1()
	/Users/<USER>/.local/share/mise/installs/go/1.23.0/src/testing/testing.go:1635 +0x47c
panic({0x106a7f080?, 0x107c30e30?})
	/Users/<USER>/.local/share/mise/installs/go/1.23.0/src/runtime/panic.go:785 +0x124
icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/calcreward_test.TestCalculateReward_Execute_WithSessionCode(0xc0006e56c0)
	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/calcreward/calculate_reward_test.go:247 +0x1344
testing.tRunner(0xc0006e56c0, 0x106cafed8)
	/Users/<USER>/.local/share/mise/installs/go/1.23.0/src/testing/testing.go:1690 +0x188
created by testing.(*T).Run in goroutine 1
	/Users/<USER>/.local/share/mise/installs/go/1.23.0/src/testing/testing.go:1743 +0x5e4
FAIL	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/calcreward	1.393s
=== RUN   TestSessionStop_Execute
=== RUN   TestSessionStop_Execute/TestValidSessionIDFormat
--- PASS: TestSessionStop_Execute (0.00s)
    --- PASS: TestSessionStop_Execute/TestValidSessionIDFormat (0.00s)
=== RUN   TestSessionStopInputData_Validation
=== RUN   TestSessionStopInputData_Validation/TestRequiredSessionID
--- PASS: TestSessionStopInputData_Validation (0.00s)
    --- PASS: TestSessionStopInputData_Validation/TestRequiredSessionID (0.00s)
=== RUN   TestSessionStopOutputData_Structure
=== RUN   TestSessionStopOutputData_Structure/TestOutputStructure
=== RUN   TestSessionStopOutputData_Structure/TestPartialFailureOutput
--- PASS: TestSessionStopOutputData_Structure (0.00s)
    --- PASS: TestSessionStopOutputData_Structure/TestOutputStructure (0.00s)
    --- PASS: TestSessionStopOutputData_Structure/TestPartialFailureOutput (0.00s)
=== RUN   TestSessionStopIdempotency
=== RUN   TestSessionStopIdempotency/TestAlreadyStoppedSession
--- PASS: TestSessionStopIdempotency (0.00s)
    --- PASS: TestSessionStopIdempotency/TestAlreadyStoppedSession (0.00s)
=== RUN   TestSessionStopErrorHandling
=== RUN   TestSessionStopErrorHandling/TestK8sDeleteFailure
=== RUN   TestSessionStopErrorHandling/TestDatabaseUpdateFailure
--- PASS: TestSessionStopErrorHandling (0.00s)
    --- PASS: TestSessionStopErrorHandling/TestK8sDeleteFailure (0.00s)
    --- PASS: TestSessionStopErrorHandling/TestDatabaseUpdateFailure (0.00s)
=== RUN   TestSessionReady_Execute_Success
--- PASS: TestSessionReady_Execute_Success (0.48s)
=== RUN   TestSessionReady_Execute_SessionNotFound
    pkg_test.go:113: 
        	Error Trace:	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/intern/pkg_test.go:113
        	Error:      	"record not foundSession not found: sessionID=999999" does not contain "Session不存在"
        	Test:       	TestSessionReady_Execute_SessionNotFound
--- FAIL: TestSessionReady_Execute_SessionNotFound (0.00s)
=== RUN   TestSessionInfo_Execute
=== RUN   TestSessionInfo_Execute/TestInvalidSessionID
=== RUN   TestSessionInfo_Execute/TestValidSessionIDFormat
--- PASS: TestSessionInfo_Execute (0.00s)
    --- PASS: TestSessionInfo_Execute/TestInvalidSessionID (0.00s)
    --- PASS: TestSessionInfo_Execute/TestValidSessionIDFormat (0.00s)
=== RUN   TestSessionInfoInputData_Validation
=== RUN   TestSessionInfoInputData_Validation/TestRequiredFields
--- PASS: TestSessionInfoInputData_Validation (0.00s)
    --- PASS: TestSessionInfoInputData_Validation/TestRequiredFields (0.00s)
=== RUN   TestSessionInfoOutputData_Structure
=== RUN   TestSessionInfoOutputData_Structure/TestOutputStructure
--- PASS: TestSessionInfoOutputData_Structure (0.00s)
    --- PASS: TestSessionInfoOutputData_Structure/TestOutputStructure (0.00s)
=== RUN   TestToolComplete_Execute_Success
--- PASS: TestToolComplete_Execute_Success (0.03s)
=== RUN   TestToolComplete_Execute_Failed_WithErrorMessage
--- PASS: TestToolComplete_Execute_Failed_WithErrorMessage (0.01s)
=== RUN   TestToolComplete_Execute_Failed_WithoutErrorMessage
--- PASS: TestToolComplete_Execute_Failed_WithoutErrorMessage (0.01s)
=== RUN   TestToolComplete_Execute_InvalidStatus
--- PASS: TestToolComplete_Execute_InvalidStatus (0.00s)
=== RUN   TestToolComplete_Execute_TaskNotFound
--- PASS: TestToolComplete_Execute_TaskNotFound (0.00s)
=== RUN   TestToolComplete_Execute_SessionIDMismatch
--- PASS: TestToolComplete_Execute_SessionIDMismatch (0.01s)
=== RUN   TestToolComplete_Execute_TaskNotRunning
--- PASS: TestToolComplete_Execute_TaskNotRunning (0.01s)
FAIL
coverage: 32.6% of statements
FAIL	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/intern	1.031s
=== RUN   TestRegister_Execute_Success
--- PASS: TestRegister_Execute_Success (0.45s)
PASS
coverage: 77.8% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/mcpservice	2.081s	coverage: 77.8% of statements
=== RUN   TestSessionInit_Execute_Success_00_WithMocks
    pkg_test.go:306: ✅ SessionInit执行成功，SessionID=1001，工具数量=2
--- PASS: TestSessionInit_Execute_Success_00_WithMocks (8.42s)
=== RUN   TestSessionInit_Execute_ServerNotFound
--- PASS: TestSessionInit_Execute_ServerNotFound (0.02s)
=== RUN   TestSessionInit_Execute_EnvNotFound
2025/08/01 17:00:29.331866 session_init.go:92: 查询环境失败: record not foundEnvironment not found: envID=999999 Select environment by primary key
--- PASS: TestSessionInit_Execute_EnvNotFound (0.03s)
=== RUN   TestSessionInit_Execute_EmptyServerIDs
--- PASS: TestSessionInit_Execute_EmptyServerIDs (0.02s)
=== RUN   TestSessionInit_Execute_ZeroEnvID
--- PASS: TestSessionInit_Execute_ZeroEnvID (9.37s)
=== RUN   TestSessionInit_Execute_EmptyServerIDsAndZeroEnvID
--- PASS: TestSessionInit_Execute_EmptyServerIDsAndZeroEnvID (0.00s)
=== RUN   TestSessionInit_Execute_ContainerTimeout
--- PASS: TestSessionInit_Execute_ContainerTimeout (9.02s)
=== RUN   TestFileDownload
saved to: downloads/file_editor
--- PASS: TestFileDownload (0.32s)
=== RUN   TestSessionCopyin_Execute_Success
--- PASS: TestSessionCopyin_Execute_Success (0.02s)
=== RUN   TestSessionCopyin_Execute_SessionNotFound
2025/08/01 17:00:48.094559 session_copyin.go:58: 通过SessionID查询Session失败: record not foundSession not found: sessionID=99999
--- PASS: TestSessionCopyin_Execute_SessionNotFound (0.00s)
=== RUN   TestSessionCopyin_Execute_SessionNotRunning
--- PASS: TestSessionCopyin_Execute_SessionNotRunning (0.02s)
=== RUN   TestSessionCopyin_Execute_InvalidInput
--- PASS: TestSessionCopyin_Execute_InvalidInput (0.00s)
=== RUN   TestSessionCopyin_Execute_UploadFailed
2025/08/01 17:00:48.120232 session_copyin.go:110: Session文件复制失败: session_id=1052, files_count=1
--- PASS: TestSessionCopyin_Execute_UploadFailed (0.01s)
=== RUN   TestSessionExec_Execute_Success
--- PASS: TestSessionExec_Execute_Success (0.01s)
=== RUN   TestSessionExec_Execute_SessionNotFound
--- PASS: TestSessionExec_Execute_SessionNotFound (0.00s)
=== RUN   TestSessionExec_Execute_SessionNotRunning
--- PASS: TestSessionExec_Execute_SessionNotRunning (0.04s)
=== RUN   TestSessionExec_Execute_InvalidInput
--- PASS: TestSessionExec_Execute_InvalidInput (0.00s)
=== RUN   TestSessionStop_Execute_Success
--- PASS: TestSessionStop_Execute_Success (0.02s)
=== RUN   TestSessionStop_Execute_SessionNotFound
2025/08/01 17:00:48.204389 session_stop.go:53: 通过SessionID查询Session失败: record not foundSession not found: sessionID=99999
    session_stop_test.go:79: 
        	Error Trace:	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/session/session_stop_test.go:79
        	Error:      	"record not foundSession not found: sessionID=99999" does not contain "Session不存在"
        	Test:       	TestSessionStop_Execute_SessionNotFound
--- FAIL: TestSessionStop_Execute_SessionNotFound (0.01s)
=== RUN   TestSessionStop_Execute_AlreadyStopped
2025/08/01 17:00:48.277666 session_stop.go:64: Session已经停止: session_id=1056
    session_stop_test.go:123: 
        	Error Trace:	/Users/<USER>/code/baidu/dataeng/mcp-online-server/model/service/session/session_stop_test.go:123
        	Error:      	Not equal: 
        	            	expected: "stopping"
        	            	actual  : "stopped"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-stopping
        	            	+stopped
        	Test:       	TestSessionStop_Execute_AlreadyStopped
--- FAIL: TestSessionStop_Execute_AlreadyStopped (0.07s)
=== RUN   TestSessionStop_Execute_NoJobID
--- PASS: TestSessionStop_Execute_NoJobID (0.06s)
=== RUN   TestSessionStop_Execute_AsyncStopping
--- PASS: TestSessionStop_Execute_AsyncStopping (0.19s)
=== RUN   TestSessionStop_Execute_AlreadyStopping
2025/08/01 17:00:48.531644 session_stop.go:74: Session正在停止中: session_id=1059
--- PASS: TestSessionStop_Execute_AlreadyStopping (0.01s)
FAIL
coverage: 45.9% of statements
FAIL	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/session	28.860s
=== RUN   TestSweAgentStrategy_BuildCommand
=== RUN   TestSweAgentStrategy_BuildCommand/file_editor_with_command_and_parameters
=== RUN   TestSweAgentStrategy_BuildCommand/tool_with_boolean_false
=== RUN   TestSweAgentStrategy_BuildCommand/tool_without_command
=== RUN   TestSweAgentStrategy_BuildCommand/tool_with_no_arguments
--- PASS: TestSweAgentStrategy_BuildCommand (0.00s)
    --- PASS: TestSweAgentStrategy_BuildCommand/file_editor_with_command_and_parameters (0.00s)
    --- PASS: TestSweAgentStrategy_BuildCommand/tool_with_boolean_false (0.00s)
    --- PASS: TestSweAgentStrategy_BuildCommand/tool_without_command (0.00s)
    --- PASS: TestSweAgentStrategy_BuildCommand/tool_with_no_arguments (0.00s)
=== RUN   TestCommandStrategyManager
=== RUN   TestCommandStrategyManager/get_existing_strategy
=== RUN   TestCommandStrategyManager/get_non-existing_strategy
=== RUN   TestCommandStrategyManager/build_command_with_strategy
=== RUN   TestCommandStrategyManager/build_command_with_invalid_JSON
=== RUN   TestCommandStrategyManager/build_command_with_empty_arguments
--- PASS: TestCommandStrategyManager (0.00s)
    --- PASS: TestCommandStrategyManager/get_existing_strategy (0.00s)
    --- PASS: TestCommandStrategyManager/get_non-existing_strategy (0.00s)
    --- PASS: TestCommandStrategyManager/build_command_with_strategy (0.00s)
    --- PASS: TestCommandStrategyManager/build_command_with_invalid_JSON (0.00s)
    --- PASS: TestCommandStrategyManager/build_command_with_empty_arguments (0.00s)
=== RUN   TestGlobalCommandStrategyManager
=== RUN   TestGlobalCommandStrategyManager/global_manager_has_sweagent_strategy
--- PASS: TestGlobalCommandStrategyManager (0.00s)
    --- PASS: TestGlobalCommandStrategyManager/global_manager_has_sweagent_strategy (0.00s)
=== RUN   TestValidateSession_Success_Cases
=== RUN   TestValidateSession_Success_Cases/ValidSessionCode_Success
=== RUN   TestValidateSession_Success_Cases/ValidSessionID_Success
--- PASS: TestValidateSession_Success_Cases (0.29s)
    --- PASS: TestValidateSession_Success_Cases/ValidSessionCode_Success (0.00s)
    --- PASS: TestValidateSession_Success_Cases/ValidSessionID_Success (0.00s)
=== RUN   TestValidateSession_Error_Cases
=== RUN   TestValidateSession_Error_Cases/SessionCode_QueryFailed
=== RUN   TestValidateSession_Error_Cases/BothParameters_Empty
--- PASS: TestValidateSession_Error_Cases (0.00s)
    --- PASS: TestValidateSession_Error_Cases/SessionCode_QueryFailed (0.00s)
    --- PASS: TestValidateSession_Error_Cases/BothParameters_Empty (0.00s)
=== RUN   TestWaitForTaskCompletion_Timeout_Error
--- PASS: TestWaitForTaskCompletion_Timeout_Error (0.00s)
=== RUN   TestWaitForTaskCompletion_TaskStatus_Errors
=== RUN   TestWaitForTaskCompletion_TaskStatus_Errors/TaskStatus_Failed
=== RUN   TestWaitForTaskCompletion_TaskStatus_Errors/TaskStatus_Timeout
--- PASS: TestWaitForTaskCompletion_TaskStatus_Errors (0.00s)
    --- PASS: TestWaitForTaskCompletion_TaskStatus_Errors/TaskStatus_Failed (0.00s)
    --- PASS: TestWaitForTaskCompletion_TaskStatus_Errors/TaskStatus_Timeout (0.00s)
=== RUN   TestGetTaskStatus_Error_Cases
=== RUN   TestGetTaskStatus_Error_Cases/TaskStatus_Failed
=== RUN   TestGetTaskStatus_Error_Cases/TaskStatus_Timeout
--- PASS: TestGetTaskStatus_Error_Cases (0.00s)
    --- PASS: TestGetTaskStatus_Error_Cases/TaskStatus_Failed (0.00s)
    --- PASS: TestGetTaskStatus_Error_Cases/TaskStatus_Timeout (0.00s)
=== RUN   TestValidateToolCallRequest_Parameter_Validation
=== RUN   TestValidateToolCallRequest_Parameter_Validation/Valid_Request_AutoGenerateCallID
=== RUN   TestValidateToolCallRequest_Parameter_Validation/Missing_SessionID_And_SessionCode
=== RUN   TestValidateToolCallRequest_Parameter_Validation/Missing_ToolName
=== RUN   TestValidateToolCallRequest_Parameter_Validation/Missing_Arguments
--- PASS: TestValidateToolCallRequest_Parameter_Validation (0.00s)
    --- PASS: TestValidateToolCallRequest_Parameter_Validation/Valid_Request_AutoGenerateCallID (0.00s)
    --- PASS: TestValidateToolCallRequest_Parameter_Validation/Missing_SessionID_And_SessionCode (0.00s)
    --- PASS: TestValidateToolCallRequest_Parameter_Validation/Missing_ToolName (0.00s)
    --- PASS: TestValidateToolCallRequest_Parameter_Validation/Missing_Arguments (0.00s)
=== RUN   TestCreateToolCallTask_Success
--- PASS: TestCreateToolCallTask_Success (0.00s)
=== RUN   TestValidateToolCallRequest_AutoGenerateCallID
--- PASS: TestValidateToolCallRequest_AutoGenerateCallID (0.00s)
=== RUN   TestValidateToolCallRequest_KeepExistingCallID
--- PASS: TestValidateToolCallRequest_KeepExistingCallID (0.00s)
=== RUN   TestToolList_Execute_ValidParameters
=== RUN   TestToolList_Execute_ValidParameters/ValidSessionID_RunningStatus_WithTools
=== RUN   TestToolList_Execute_ValidParameters/ValidSessionCode_RunningStatus_WithTools
--- PASS: TestToolList_Execute_ValidParameters (0.05s)
    --- PASS: TestToolList_Execute_ValidParameters/ValidSessionID_RunningStatus_WithTools (0.03s)
    --- PASS: TestToolList_Execute_ValidParameters/ValidSessionCode_RunningStatus_WithTools (0.01s)
=== RUN   TestToolList_Execute_InvalidParameters
=== RUN   TestToolList_Execute_InvalidParameters/MissingBothSessionIDAndCode
=== RUN   TestToolList_Execute_InvalidParameters/NonExistentSessionID
=== RUN   TestToolList_Execute_InvalidParameters/NonExistentSessionCode
--- PASS: TestToolList_Execute_InvalidParameters (0.00s)
    --- PASS: TestToolList_Execute_InvalidParameters/MissingBothSessionIDAndCode (0.00s)
    --- PASS: TestToolList_Execute_InvalidParameters/NonExistentSessionID (0.00s)
    --- PASS: TestToolList_Execute_InvalidParameters/NonExistentSessionCode (0.00s)
=== RUN   TestToolList_Execute_SessionStatusEdgeCases
=== RUN   TestToolList_Execute_SessionStatusEdgeCases/PendingStatus
=== RUN   TestToolList_Execute_SessionStatusEdgeCases/RunningStatus_NoTools
=== RUN   TestToolList_Execute_SessionStatusEdgeCases/FailedStatus_WithTools
=== RUN   TestToolList_Execute_SessionStatusEdgeCases/StoppedStatus_WithTools
=== RUN   TestToolList_Execute_SessionStatusEdgeCases/TimeoutStatus_WithTools
--- PASS: TestToolList_Execute_SessionStatusEdgeCases (0.04s)
    --- PASS: TestToolList_Execute_SessionStatusEdgeCases/PendingStatus (0.01s)
    --- PASS: TestToolList_Execute_SessionStatusEdgeCases/RunningStatus_NoTools (0.01s)
    --- PASS: TestToolList_Execute_SessionStatusEdgeCases/FailedStatus_WithTools (0.01s)
    --- PASS: TestToolList_Execute_SessionStatusEdgeCases/StoppedStatus_WithTools (0.01s)
    --- PASS: TestToolList_Execute_SessionStatusEdgeCases/TimeoutStatus_WithTools (0.00s)
=== RUN   TestToolList_Execute_MCPToolsDataFormats
=== RUN   TestToolList_Execute_MCPToolsDataFormats/ValidToolsFormat
=== RUN   TestToolList_Execute_MCPToolsDataFormats/EmptyToolsArray
=== RUN   TestToolList_Execute_MCPToolsDataFormats/MalformedToolsFormat
=== RUN   TestToolList_Execute_MCPToolsDataFormats/MissingToolsField
=== RUN   TestToolList_Execute_MCPToolsDataFormats/PartiallyValidTools
--- PASS: TestToolList_Execute_MCPToolsDataFormats (0.03s)
    --- PASS: TestToolList_Execute_MCPToolsDataFormats/ValidToolsFormat (0.01s)
    --- PASS: TestToolList_Execute_MCPToolsDataFormats/EmptyToolsArray (0.00s)
    --- PASS: TestToolList_Execute_MCPToolsDataFormats/MalformedToolsFormat (0.00s)
    --- PASS: TestToolList_Execute_MCPToolsDataFormats/MissingToolsField (0.00s)
    --- PASS: TestToolList_Execute_MCPToolsDataFormats/PartiallyValidTools (0.00s)
=== RUN   TestToolList_Execute_BoundaryValues
=== RUN   TestToolList_Execute_BoundaryValues/ZeroSessionID
=== RUN   TestToolList_Execute_BoundaryValues/NegativeSessionID
=== RUN   TestToolList_Execute_BoundaryValues/EmptySessionCode
=== RUN   TestToolList_Execute_BoundaryValues/WhitespaceSessionCode
=== RUN   TestToolList_Execute_BoundaryValues/VeryLongSessionCode
--- PASS: TestToolList_Execute_BoundaryValues (0.00s)
    --- PASS: TestToolList_Execute_BoundaryValues/ZeroSessionID (0.00s)
    --- PASS: TestToolList_Execute_BoundaryValues/NegativeSessionID (0.00s)
    --- PASS: TestToolList_Execute_BoundaryValues/EmptySessionCode (0.00s)
    --- PASS: TestToolList_Execute_BoundaryValues/WhitespaceSessionCode (0.00s)
    --- PASS: TestToolList_Execute_BoundaryValues/VeryLongSessionCode (0.00s)
=== RUN   TestToolList_Execute_ConcurrentAccess
--- PASS: TestToolList_Execute_ConcurrentAccess (0.04s)
=== RUN   TestToolList_Execute_ResponseFormat
--- PASS: TestToolList_Execute_ResponseFormat (0.01s)
=== RUN   TestToolList_Execute_LargeDataSet
--- PASS: TestToolList_Execute_LargeDataSet (0.04s)
=== RUN   TestToolList_Execute_SessionCodePriority
--- PASS: TestToolList_Execute_SessionCodePriority (0.02s)
PASS
coverage: 48.0% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool	2.341s	coverage: 48.0% of statements
=== RUN   TestCallProcessStrategyManager
=== RUN   TestCallProcessStrategyManager/has_sweagent_strategy
=== RUN   TestCallProcessStrategyManager/unknown_strategy_returns_error
=== RUN   TestCallProcessStrategyManager/build_command_with_sweagent_strategy
=== RUN   TestCallProcessStrategyManager/assemble_response_with_sweagent_strategy
--- PASS: TestCallProcessStrategyManager (0.00s)
    --- PASS: TestCallProcessStrategyManager/has_sweagent_strategy (0.00s)
    --- PASS: TestCallProcessStrategyManager/unknown_strategy_returns_error (0.00s)
    --- PASS: TestCallProcessStrategyManager/build_command_with_sweagent_strategy (0.00s)
    --- PASS: TestCallProcessStrategyManager/assemble_response_with_sweagent_strategy (0.00s)
=== RUN   TestGlobalCallProcessStrategyManager
=== RUN   TestGlobalCallProcessStrategyManager/global_manager_is_initialized
--- PASS: TestGlobalCallProcessStrategyManager (0.00s)
    --- PASS: TestGlobalCallProcessStrategyManager/global_manager_is_initialized (0.00s)
PASS
coverage: 54.1% of statements
ok  	icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool/command	1.349s	coverage: 54.1% of statements
FAIL

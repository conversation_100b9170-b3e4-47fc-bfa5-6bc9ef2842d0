FROM ubuntu:22.04

ARG OLD_COMMIT

RUN apt-get update -y && apt-get upgrade -y

ENV DEBIAN_FRONTEND noninteractive

RUN echo "tzdata tzdata/Areas select America" | debconf-set-selections && echo "tzdata tzdata/Zones/America select Los_Angeles" | debconf-set-selections

# Install standard and python specific system dependencies
RUN apt-get install -y git curl wget build-essential ca-certificates python3-dev python3.11-dev gcc g++ gfortran libopenblas-dev liblapack-dev pkg-config glibc-tools libpq-dev libgl1-mesa-glx freetds-dev libxcb1 libxcb1-dev libx11-xcb1 libx11-xcb-dev libxcb-keysyms1  libxcb-keysyms1-dev libxcb-image0 libxcb-image0-dev libxcb-shm0 libxcb-shm0-dev libxcb-icccm4 libxcb-icccm4-dev libxcb-sync1 libxcb-sync-dev libxcb-xfixes0-dev libxcb-shape0-dev libxcb-randr0-dev  libxcb-render-util0 libxcb-render-util0-dev libxcb-glx0-dev

RUN apt-get install -y xvfb

RUN ln -s /usr/include/locale.h /usr/include/xlocale.h

RUN curl -LsSf https://astral.sh/uv/install.sh | sh

ENV PATH="/root/.cargo/bin:${PATH}"

ENV PATH="/root/.local/bin:${PATH}"

RUN git clone https://github.com/biolab/orange3.git testbed

COPY run_tests.sh /testbed/run_tests.sh

COPY install.sh /testbed/install.sh

WORKDIR /testbed

RUN git checkout $OLD_COMMIT

RUN git submodule update --init --recursive

RUN git status

RUN bash install.sh

RUN uv pip install tree_sitter_languages

ENV VIRTUAL_ENV=/testbed/.venv

ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY r2e_tests /r2e_tests

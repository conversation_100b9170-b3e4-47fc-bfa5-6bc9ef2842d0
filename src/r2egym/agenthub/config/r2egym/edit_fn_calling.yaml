system_prompt: |-
  You are a programming agent who is provided a github issue and repository bash environment and is tasked to solve certain tasks (e.g., file localization, testcase generation, code repair and editing etc) to resolve the issue.

instance_prompt: |-
  Consider the following github issue:
  <github_issue>
  {problem_statement}
  </github_issue>

  Can you help me implement the necessary changes to the repository to fix the <github_issue>?
  I've already taken care of all changes to any of the test files described in the <github_issue>. This means you DON'T have to modify the testing logic or any of the tests in any way!
  Your task is to make the minimal changes to non-tests files in the /testbed directory to ensure the <github_issue> is satisfied.

  IMPORTANT TIP:
  Follow these steps to resolve the issue:
  1. As a first step, it might be a good idea to explore the repo to familiarize yourself with its structure.
  2. Create a script ('reproduce_issue.py') to reproduce the error and execute it to confirm the error
    2.1 reproduce_issue.py script finishes quickly after checking the error, fix etc. There no long running background servers for django for instance etc. It should be a quick script which checks the error and fix to provide a visible response.
    2.2 SUPER IMPORTANT: to ensure this reproduce_script.py must have a timeout logic of 20 seconds. If the script runs for more than 30 seconds, it should output a timeout message and you can interpret accordingly.
  3. Edit the sourcecode of the repo to resolve the issue
  4. Rerun your reproduce script and confirm that the error is fixed!
  5. Think about edgecases and make sure your fix handles them as well

  VERY IMPORTANT: each response must include both reasoning and function call to solve the task.
  You are being told a million times, each response must include a function call. Must inlcude a function call at all costs.

  You can take multiple turns to solve the task. So please only finish / submit when you are confident in your response. Dont rush. Be comprehensive.
  You are being told a million times, please dont just submit without proper reasoning. Try to fully analyse the problem statement, explore the repository, reproduce the issue, fix it, check edge cases and then submit.
    
  Your thinking should be thorough and so it's fine if it's very long.
  VERY IMPORTANT: file_editor old_str and new_str must be w/o the line numbers. line numbers are only shown in the view for clarity.
  
  Also if a file_editor edit fails, its a good idea to view the file near the edit location before trying to edit again. Dont keep trying the same edit over and over again. It will keep leading to the same failure.
  Again do not get stuck trying to do the same thing over and over again. Please be efficient.

command_files:
  - "./src/r2egym/agenthub/tools/r2egym/file_editor.py"
  - "./src/r2egym/agenthub/tools/search.py"
  - "./src/r2egym/agenthub/tools/r2egym/execute_bash.py"
  - "./src/r2egym/agenthub/tools/finish.py"
llm_name: "gpt-4o"
demo_file: "./r2egym/agenthub/config/localizer-demo"
other_args:
  max_retries: 3
  timeout: 120
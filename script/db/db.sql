use mcp_online_server;

-- 镜像表: 存储容器镜像信息
CREATE TABLE obj_image (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    image_id VARCHAR(256) NOT NULL DEFAULT "" COMMENT '镜像ID，唯一，不重复',
    image_path VARCHAR(256) NOT NULL DEFAULT "" COMMENT '镜像地址，如mcp/mcp-rumtime:latest',
    image_description TEXT COMMENT '镜像描述',
    container_port INT DEFAULT 0 COMMENT '容器端口，如8080',
    container_env JSON COMMENT '容器环境变量，JSON格式存储, 如{"PYTHONPATH": "/opt/python3.10/bin/python3.10"}',
    container_command VARCHAR(256) NOT NULL DEFAULT "" COMMENT '容器启动命令，如npx、python等',
    container_args JSON COMMENT '容器启动参数数组，JSON格式存储',
    container_mounts JSON COMMENT '容器挂载卷，JSON格式存储, 如{"input": "/home/<USER>/input", "output": "/home/<USER>/output"}',
    container_resources JSON COMMENT '容器资源配置，JSON格式存储, 如{"cpu": 1, "memory": {"value": 1, "type": "G"}}',
    root_user tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否以root用户运行容器',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_image_path (image_path) COMMENT '镜像地址索引',
    UNIQUE KEY uk_image_id (image_id) COMMENT '镜像ID唯一约束'
) COMMENT = 'MCP服务器注册表，存储服务器镜像配置';

-- MCP服务器表: 存储注册的MCP服务器配置信息
CREATE TABLE obj_register_mcp_server (
    server_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    server_name VARCHAR(64) NOT NULL DEFAULT "" COMMENT 'MCP服务器名称，全局唯一，如filesystem、sqlite等',
    type VARCHAR(32) NOT NULL DEFAULT 'local' COMMENT '服务器类型，local-本地stdio，remote-远程streamable，sse-服务器发送事件',
    url VARCHAR(256) NOT NULL DEFAULT "" COMMENT '如果是远程服务器或sse，则填写服务器地址，如http://127.0.0.1:8080',
    headers JSON COMMENT '远程服务器请求头，JSON格式存储，如{"Authorization": "Bearer token"}',
    command VARCHAR(256) NOT NULL DEFAULT "" COMMENT '启动命令，如npx、python等',
    args JSON COMMENT '启动参数数组，JSON格式存储',
    description TEXT COMMENT '服务器功能描述',
    env JSON COMMENT '环境变量，JSON格式存储, 如{"PYTHONPATH": "/opt/python3.10/bin/python3.10"}',
    server_code_bos_url VARCHAR(256) NOT NULL DEFAULT "" COMMENT '用户上传的mcp server code的地址',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_server_name (server_name) COMMENT '服务器名称索引'
) COMMENT = 'MCP服务器注册表，存储所有可用的MCP服务器配置';

-- 环境表: 存储MCP执行环境的配置和状态
CREATE TABLE obj_mcp_env (
    env_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    env_md5 VARCHAR(32) NOT NULL DEFAULT "" COMMENT '环境MD5哈希值，用于唯一标识环境版本',
    name VARCHAR(128) NOT NULL DEFAULT "" COMMENT '环境名称，用户友好的标识',
    description TEXT COMMENT '环境描述信息',
    bos_url VARCHAR(512) NOT NULL DEFAULT "" COMMENT 'BOS存储的环境压缩包下载地址',
    env_dependency JSON COMMENT '环境依赖配置，JSON格式存储文件、目录、数据库等依赖信息',
    file_size BIGINT DEFAULT 0 COMMENT '环境压缩包文件大小，单位字节',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_env_md5 (env_md5) COMMENT '环境MD5索引'
) COMMENT = 'MCP执行环境表，存储环境配置和BOS地址信息';

-- 会话状态表: 存储MCP会话的生命周期状态
CREATE TABLE obj_session (
    session_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键，会话ID',
    session_code VARCHAR(255) NOT NULL DEFAULT '' COMMENT '会话的唯一业务编码',
    job_id VARCHAR(128) NOT NULL DEFAULT "" COMMENT '容器平台返回的作业ID，用于容器管理',
    env_id BIGINT NOT NULL DEFAULT "0" COMMENT '关联的环境ID，外键引用environments表的id字段',
    image_id VARCHAR(256) NOT NULL DEFAULT "" COMMENT '关联的镜像ID，用于环境初始化',
    server_ids JSON COMMENT '关联的MCP服务器ID列表，JSON数组格式，存储mcp_servers表的id字段',
    mcp_tools JSON COMMENT '容器启动后上报的可用工具列表，包含工具名称、描述、参数等',
    container_status VARCHAR(32) DEFAULT 'init' COMMENT '容器状态： init-初始化，pending-等待中，running-运行中，rewarding-计算奖励中，stopping-停止中，stopped-已停止，timeout-超时，failed-失败',
    timeout_seconds INT DEFAULT 0 COMMENT '会话超时时间，单位秒，0表示无超时限制',
    reward JSON NULL COMMENT '奖励值JSON对象，存储k8sexecute接口返回结果，包含code、stderr_url、stdout_url字段',
    err_msg TEXT NULL COMMENT '会话失败原因，记录初始化或运行过程中的错误信息，成功时为NULL',
    log_url VARCHAR(512) NULL COMMENT '会话日志地址',
    stopped_at TIMESTAMP(3) NULL COMMENT '停止时间，用于计算会话持续时间',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    UNIQUE KEY uk_session_code (session_code),
    INDEX idx_job_id (job_id) COMMENT '作业ID索引',
    INDEX idx_container_status (container_status) COMMENT '容器状态索引'
) COMMENT = 'MCP会话管理表，跟踪会话生命周期和容器状态';

-- 工具调用记录表: 存储所有MCP工具调用的详细记录
CREATE TABLE obj_tool_call_task (
    task_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键，任务ID',
    call_id VARCHAR(64) NOT NULL COMMENT '调用标识符，UUID格式，在同一会话内唯一',
    session_id BIGINT NOT NULL COMMENT '关联的会话ID，外键引用sessions表',
    tool_name VARCHAR(128) NOT NULL COMMENT '工具名称，格式为server_name__tool_name，如filesystem__read_file',
    arguments TEXT COMMENT '工具调用参数，JSON格式存储',
    result TEXT COMMENT '工具执行结果，JSON格式存储',
    old_env_md5 VARCHAR(32) COMMENT '执行前环境MD5值',
    new_env_md5 VARCHAR(32) COMMENT '执行后环境MD5值，用于跟踪环境变化',
    old_env_url VARCHAR(512) COMMENT '执行前环境包url',
    new_env_url VARCHAR(512) COMMENT '执行后环境包url',
    tool_call_status VARCHAR(32) DEFAULT 'pending' COMMENT '执行状态：pending-等待执行，running-执行中，success-成功，failed-失败，timeout-超时',
    error_message TEXT COMMENT '错误信息，失败时记录具体错误内容',
    started_at TIMESTAMP(3) NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP(3) NULL COMMENT '完成时间',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_session_id (session_id) COMMENT '会话ID索引',
    INDEX idx_tool_name (tool_name) COMMENT '工具名称索引',
    INDEX idx_tool_call_status (tool_call_status) COMMENT '状态索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引',
    UNIQUE KEY uk_call_id_session_id (call_id, session_id) COMMENT '调用ID和会话ID组合唯一约束'
) COMMENT = 'MCP工具调用记录表，存储所有工具调用的详细信息和执行结果';